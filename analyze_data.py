import pandas as pd
import numpy as np
import os

def analyze_csv_file(file_path):
    print(f"Analyzing file: {file_path}")
    
    # Check if file exists
    if not os.path.exists(file_path):
        print(f"Error: File {file_path} does not exist")
        return
    
    # Get file size
    file_size_bytes = os.path.getsize(file_path)
    file_size_mb = file_size_bytes / (1024 * 1024)
    print(f"File size: {file_size_mb:.2f} MB")
    
    try:
        # Read the first few rows to get column info
        df_sample = pd.read_csv(file_path, nrows=5)
        print(f"Number of columns: {len(df_sample.columns)}")
        print(f"Column names: {df_sample.columns.tolist()}")
        
        # Read the full file with error handling
        print("Reading full file...")
        df = pd.read_csv(file_path)
        
        # Basic statistics
        print(f"Number of rows: {len(df)}")
        print(f"Number of columns: {len(df.columns)}")
        
        # Check for NaN values
        nan_counts = df.isna().sum()
        print("\nNaN counts per column:")
        for col, count in nan_counts.items():
            if count > 0:
                print(f"  {col}: {count} ({count/len(df)*100:.2f}%)")
        
        # Check for infinite values
        inf_counts = np.isinf(df.select_dtypes(include=[np.number])).sum()
        print("\nInfinite value counts per column:")
        for col, count in inf_counts.items():
            if count > 0:
                print(f"  {col}: {count} ({count/len(df)*100:.2f}%)")
        
        # Data types
        print("\nData types:")
        for col, dtype in df.dtypes.items():
            print(f"  {col}: {dtype}")
        
        # Basic statistics for numeric columns
        print("\nBasic statistics for numeric columns:")
        numeric_stats = df.describe()
        print(numeric_stats)
        
        # Check for columns with all NaN values
        all_nan_cols = [col for col in df.columns if df[col].isna().all()]
        if all_nan_cols:
            print("\nColumns with all NaN values:")
            for col in all_nan_cols:
                print(f"  {col}")
        
        # Check for columns with all zeros
        numeric_cols = df.select_dtypes(include=[np.number]).columns
        all_zero_cols = [col for col in numeric_cols if (df[col] == 0).all()]
        if all_zero_cols:
            print("\nNumeric columns with all zeros:")
            for col in all_zero_cols:
                print(f"  {col}")
        
        # Check for columns with constant values
        constant_cols = [col for col in df.columns if df[col].nunique() == 1]
        if constant_cols:
            print("\nColumns with constant values:")
            for col in constant_cols:
                print(f"  {col}: {df[col].iloc[0]}")
        
        return df
        
    except Exception as e:
        print(f"Error analyzing file: {e}")
        return None

if __name__ == "__main__":
    file_path = "data/processed/borg_traces_cleanedb.csv"
    df = analyze_csv_file(file_path)
    
    # If successful, print additional information about the first few rows
    if df is not None:
        print("\nFirst 5 rows:")
        print(df.head())
