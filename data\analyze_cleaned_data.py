"""
Analyze the cleaned Borg traces dataset for:
1. Missing values
2. Data types
3. Negative values
4. Boolean values
5. Column statistics
"""
import pandas as pd
import numpy as np
import os
import time
import logging

# Setup logging
os.makedirs("logs", exist_ok=True)
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s',
                   handlers=[logging.FileHandler("logs/data_analysis.log"), logging.StreamHandler()])

def analyze_data(input_file, output_file, chunk_size=5000):
    """Analyze the cleaned dataset and generate statistics"""
    logging.info(f"Analyzing cleaned dataset: {input_file}")
    
    # Check if file exists
    if not os.path.exists(input_file):
        logging.error(f"Input file {input_file} not found")
        return
    
    # Initialize analysis results
    column_dtypes = {}
    missing_values = {}
    negative_values = {}
    boolean_columns = []
    column_stats = {}
    
    # Process in chunks to handle large file
    start_time = time.time()
    total_rows = 0
    
    # First pass: Get column names and types from first chunk
    first_chunk = pd.read_csv(input_file, nrows=1)
    all_columns = first_chunk.columns.tolist()
    
    # Initialize counters for all columns
    for col in all_columns:
        missing_values[col] = 0
        column_dtypes[col] = None
        negative_values[col] = 0
        column_stats[col] = {
            'min': None,
            'max': None,
            'mean': None,
            'median': None,
            'std': None,
            'unique_values': None
        }
    
    # Process chunks
    chunks = pd.read_csv(input_file, chunksize=chunk_size, low_memory=False)
    
    # Running statistics for numeric columns
    running_sum = {col: 0 for col in all_columns}
    running_sum_squares = {col: 0 for col in all_columns}
    min_values = {col: float('inf') for col in all_columns}
    max_values = {col: float('-inf') for col in all_columns}
    unique_values = {col: set() for col in all_columns}
    
    for i, chunk in enumerate(chunks):
        chunk_rows = len(chunk)
        total_rows += chunk_rows
        
        # Update column types
        for col in all_columns:
            if col in chunk.columns:
                # Update data type
                col_dtype = chunk[col].dtype
                if column_dtypes[col] is None:
                    column_dtypes[col] = col_dtype
                elif column_dtypes[col] != col_dtype:
                    column_dtypes[col] = 'mixed'
                
                # Count missing values
                missing_values[col] += chunk[col].isna().sum()
                
                # Check for boolean columns
                if col_dtype == 'bool' or (pd.api.types.is_numeric_dtype(col_dtype) and set(chunk[col].dropna().unique()).issubset({0, 1, True, False})):
                    if col not in boolean_columns:
                        boolean_columns.append(col)
                
                # Count negative values for numeric columns
                if pd.api.types.is_numeric_dtype(col_dtype) and not col_dtype == 'bool':
                    try:
                        neg_count = (chunk[col] < 0).sum()
                        negative_values[col] += neg_count
                    except:
                        pass
                
                # Update running statistics for numeric columns
                if pd.api.types.is_numeric_dtype(col_dtype) and not col_dtype == 'bool':
                    try:
                        # Update min/max
                        col_min = chunk[col].min()
                        col_max = chunk[col].max()
                        if col_min < min_values[col]:
                            min_values[col] = col_min
                        if col_max > max_values[col]:
                            max_values[col] = col_max
                        
                        # Update sum and sum of squares for mean and std
                        running_sum[col] += chunk[col].sum()
                        running_sum_squares[col] += (chunk[col] ** 2).sum()
                        
                        # Update unique values (limit to 100 for memory efficiency)
                        if len(unique_values[col]) < 100:
                            unique_values[col].update(chunk[col].dropna().unique()[:100])
                    except:
                        pass
        
        # Log progress
        if (i+1) % 10 == 0:
            elapsed = time.time() - start_time
            logging.info(f"Processed {total_rows} rows ({i+1} chunks) in {elapsed:.2f} seconds")
    
    # Calculate final statistics
    for col in all_columns:
        if pd.api.types.is_numeric_dtype(column_dtypes[col]) and not column_dtypes[col] == 'bool':
            try:
                # Calculate mean
                column_stats[col]['mean'] = running_sum[col] / (total_rows - missing_values[col]) if total_rows > missing_values[col] else None
                
                # Calculate standard deviation
                if total_rows > missing_values[col]:
                    variance = (running_sum_squares[col] / (total_rows - missing_values[col])) - (column_stats[col]['mean'] ** 2)
                    column_stats[col]['std'] = np.sqrt(variance) if variance > 0 else 0
                
                # Set min/max
                column_stats[col]['min'] = min_values[col] if min_values[col] != float('inf') else None
                column_stats[col]['max'] = max_values[col] if max_values[col] != float('-inf') else None
                
                # Set unique values
                column_stats[col]['unique_values'] = len(unique_values[col]) if len(unique_values[col]) < 100 else '100+'
            except:
                pass
    
    # Calculate missing value percentages
    missing_percentages = {col: (count / total_rows * 100) for col, count in missing_values.items()}
    
    # Generate report
    with open(output_file, 'w') as f:
        f.write("# Cleaned Data Analysis Report\n\n")
        
        f.write(f"## Dataset Overview\n")
        f.write(f"- Total rows: {total_rows:,}\n")
        f.write(f"- Total columns: {len(all_columns)}\n\n")
        
        f.write("## Data Types\n")
        f.write("| Column | Data Type |\n")
        f.write("|--------|----------|\n")
        for col, dtype in sorted(column_dtypes.items()):
            f.write(f"| {col} | {dtype} |\n")
        f.write("\n")
        
        f.write("## Missing Values\n")
        f.write("| Column | Missing Count | Missing Percentage |\n")
        f.write("|--------|--------------|-------------------|\n")
        for col, count in sorted(missing_values.items(), key=lambda x: x[1], reverse=True):
            if count > 0:
                f.write(f"| {col} | {count:,} | {missing_percentages[col]:.2f}% |\n")
        f.write("\n")
        
        f.write("## Negative Values in Numeric Columns\n")
        f.write("| Column | Negative Count | Negative Percentage |\n")
        f.write("|--------|---------------|--------------------|\n")
        for col, count in sorted(negative_values.items(), key=lambda x: x[1], reverse=True):
            if count > 0:
                f.write(f"| {col} | {count:,} | {count / (total_rows - missing_values[col]) * 100:.2f}% |\n")
        f.write("\n")
        
        f.write("## Boolean Columns\n")
        f.write("| Column |\n")
        f.write("|--------|\n")
        for col in sorted(boolean_columns):
            f.write(f"| {col} |\n")
        f.write("\n")
        
        f.write("## Column Statistics (Numeric Columns)\n")
        f.write("| Column | Min | Max | Mean | Std Dev | Unique Values |\n")
        f.write("|--------|-----|-----|------|---------|---------------|\n")
        for col in all_columns:
            if pd.api.types.is_numeric_dtype(column_dtypes[col]) and not column_dtypes[col] == 'bool':
                stats = column_stats[col]
                min_val = f"{stats['min']:.2f}" if stats['min'] is not None else "N/A"
                max_val = f"{stats['max']:.2f}" if stats['max'] is not None else "N/A"
                mean_val = f"{stats['mean']:.2f}" if stats['mean'] is not None else "N/A"
                std_val = f"{stats['std']:.2f}" if stats['std'] is not None else "N/A"
                unique_val = stats['unique_values'] if stats['unique_values'] is not None else "N/A"
                f.write(f"| {col} | {min_val} | {max_val} | {mean_val} | {std_val} | {unique_val} |\n")
    
    logging.info(f"Analysis completed in {time.time() - start_time:.2f} seconds")
    logging.info(f"Report saved to {output_file}")
    print(f"Analysis completed. Report saved to {output_file}")

def main():
    """Main function to run data analysis"""
    input_file = "data/processed/borg_traces_cleaned.csv"
    output_file = "data/processed/data_analysis_report.md"
    
    # Run analysis
    logging.info("Starting data analysis")
    print("Starting data analysis...")
    print("This may take a while depending on the size of the dataset.")
    
    try:
        analyze_data(input_file, output_file)
    except Exception as e:
        logging.error(f"Error: {e}")
        print(f"Error: {e}")

if __name__ == "__main__":
    main()
