"""
<PERSON><PERSON>t to convert boolean values ('True', 'False', 'TRUE', 'FALSE') to numerical values (1, 0)
in the borg_traces_cleanedb.csv file.
"""
import pandas as pd
import numpy as np
import os
import time

def convert_booleans_to_numeric(input_file, output_file=None, chunk_size=10000):
    """
    Convert boolean values to numeric in a CSV file.

    Args:
        input_file (str): Path to the input CSV file
        output_file (str, optional): Path to save the output CSV file. If None, overwrites the input file.
        chunk_size (int): Number of rows to process at a time
    """
    if output_file is None:
        output_file = input_file + ".temp"

    print(f"Converting boolean values in {input_file} to numeric values...")
    start_time = time.time()

    # Read the entire file at once (if it's not too large)
    try:
        print("Attempting to read the entire file...")
        df = pd.read_csv(input_file)

        # Convert boolean-like string values to numeric
        bool_columns = []
        for col in df.columns:
            # Check if column contains boolean values
            if df[col].dtype == bool:
                bool_columns.append(col)
                df[col] = df[col].astype(int)
            elif df[col].dtype == 'object':
                # Check if column contains boolean-like strings
                unique_vals = set(df[col].dropna().astype(str).str.lower().unique())
                if unique_vals.issubset({'true', 'false', ''}):
                    bool_columns.append(col)
                    # Convert to numeric: True -> 1, False -> 0
                    df[col] = df[col].map({'True': 1, 'TRUE': 1, 'true': 1, True: 1,
                                          'False': 0, 'FALSE': 0, 'false': 0, False: 0})
            # Also check for columns with '_outlier' suffix that might have float values
            elif col.endswith('_outlier') and df[col].dtype in ['float64', 'float32']:
                bool_columns.append(col)
                # Fill NaN values with 0 before converting to int
                df[col] = df[col].fillna(0).astype(int)

        print(f"Found {len(bool_columns)} boolean columns: {bool_columns[:10]}...")

        # Save the updated dataframe
        df.to_csv(output_file, index=False)

        # If successful and output_file is temporary, replace the original file
        if output_file.endswith('.temp'):
            import shutil
            shutil.move(output_file, input_file)
            print(f"Replaced original file with updated version")

        elapsed_time = time.time() - start_time
        print(f"Conversion completed in {elapsed_time:.2f} seconds.")
        print(f"Output saved to {input_file}")
        return

    except (MemoryError, Exception) as e:
        print(f"Could not read entire file at once: {e}")
        print("Processing in chunks instead...")

    # Process the file in chunks if reading the entire file failed
    try:
        # Get the total number of rows for progress reporting
        with open(input_file, 'r') as f:
            total_rows = sum(1 for _ in f) - 1  # Subtract header row

        # Process in chunks
        chunks = pd.read_csv(input_file, chunksize=chunk_size)

        # Process each chunk
        for i, chunk in enumerate(chunks):
            # Convert boolean-like string values to numeric
            for col in chunk.columns:
                if chunk[col].dtype == bool:
                    chunk[col] = chunk[col].astype(int)
                elif chunk[col].dtype == 'object':
                    # Check if column contains boolean-like strings
                    unique_vals = set(chunk[col].dropna().astype(str).str.lower().unique())
                    if unique_vals.issubset({'true', 'false', ''}):
                        # Convert to numeric: True -> 1, False -> 0
                        chunk[col] = chunk[col].map({'True': 1, 'TRUE': 1, 'true': 1, True: 1,
                                                    'False': 0, 'FALSE': 0, 'false': 0, False: 0})
                # Also check for columns with '_outlier' suffix that might have float values
                elif col.endswith('_outlier') and chunk[col].dtype in ['float64', 'float32']:
                    # Fill NaN values with 0 before converting to int
                    chunk[col] = chunk[col].fillna(0).astype(int)

            # Save the chunk
            mode = 'w' if i == 0 else 'a'
            header = i == 0
            chunk.to_csv(output_file, mode=mode, index=False, header=header)

            # Print progress
            rows_processed = min((i + 1) * chunk_size, total_rows)
            if (i + 1) % 10 == 0 or rows_processed == total_rows:
                print(f"Processed {rows_processed:,}/{total_rows:,} rows ({rows_processed/total_rows:.1%})...")

        # If successful and output_file is temporary, replace the original file
        if output_file.endswith('.temp'):
            import shutil
            shutil.move(output_file, input_file)
            print(f"Replaced original file with updated version")

        elapsed_time = time.time() - start_time
        print(f"Conversion completed in {elapsed_time:.2f} seconds.")
        print(f"Output saved to {input_file}")

    except Exception as e:
        print(f"Error processing file: {e}")
        if os.path.exists(output_file):
            os.remove(output_file)
            print(f"Removed temporary file {output_file}")
        raise

if __name__ == "__main__":
    input_file = "data/processed/borg_traces_cleanedb.csv"

    # Check if input file exists
    if not os.path.exists(input_file):
        print(f"Error: Input file {input_file} not found")
    else:
        # Create a backup of the original file
        backup_file = input_file + ".backup"
        if not os.path.exists(backup_file):
            print(f"Creating backup of original file at {backup_file}")
            import shutil
            shutil.copy2(input_file, backup_file)

        # Convert boolean values to numeric
        convert_booleans_to_numeric(input_file)
