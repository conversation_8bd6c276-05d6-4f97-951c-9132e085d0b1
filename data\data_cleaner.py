"""Optimized data cleaning for full Borg traces dataset with type conversion"""
import pandas as pd
import numpy as np
import os, json, logging, time, gc
from ast import literal_eval
from tqdm import tqdm
from sklearn.experimental import enable_iterative_imputer
from sklearn.impute import IterativeImputer

# Setup
os.makedirs("logs", exist_ok=True)
for d in ["data/raw", "data/processed", "data/exploration"]: os.makedirs(d, exist_ok=True)
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s',
                   handlers=[logging.FileHandler("logs/data_cleaning.log"), logging.StreamHandler()])

def clean_data(input_file, output_file, chunk_size=5000):
    """Clean and enhance the full Borg traces dataset with strict type enforcement"""
    logging.info(f"Processing dataset: {input_file}")

    # Count rows efficiently
    try:
        with open(input_file, 'r') as f:
            for i, _ in enumerate(f): pass
        total_rows = i
        logging.info(f"Processing {total_rows} rows")
    except:
        total_rows = 1000000
        logging.warning(f"Row count failed. Using estimate: {total_rows}")

    # Define strict schema for consistent types across chunks
    schema = {
        # Boolean columns - always bool
        'failed': 'bool',
        'negative_duration': 'bool',
        'has_constraints': 'bool',

        # All outlier columns - always bool
        **{f'{col}_outlier': 'bool' for col in [
            'assigned_memory', 'collection_type', 'cycles_per_instruction',
            'duration_category', 'end_time_hour_tan', 'end_time_minute_tan',
            'end_time_second_cos', 'end_time_second_sin', 'instance_index',
            'memory_accesses_per_instruction', 'page_cache_memory', 'priority',
            'start_time_hour_tan', 'start_time_minute_tan', 'start_time_second_cos',
            'start_time_second_sin', 'time_hour_tan', 'time_minute_tan',
            'time_second_tan', 'time_weekday_tan',
            'resource_request_cpu', 'resource_request_memory',
            'average_usage_cpu', 'average_usage_memory',
            'maximum_usage_cpu', 'maximum_usage_memory',
            'cpu_usage_distribution_mean', 'cpu_usage_distribution_median', 'cpu_usage_distribution_max'
        ]},

        # Time components - always int32
        **{f'{prefix}_{comp}': 'int32' for prefix in ['time', 'start_time', 'end_time']
           for comp in ['hour', 'minute', 'second', 'weekday', 'day_part']},

        # Timestamps - always float64
        'time': 'float64',
        'start_time': 'float64',
        'end_time': 'float64',

        # Trigonometric columns - always float64
        **{f'{prefix}_{comp}_{trig}': 'float64' for prefix in ['time', 'start_time', 'end_time']
           for comp in ['hour', 'minute', 'second', 'weekday']
           for trig in ['sin', 'cos', 'tan']},

        # Duration columns - specific types
        'duration_integer': 'int64',
        'duration_category': 'int32',
        'duration_seconds': 'float64',
        'duration_minutes': 'float64',
        'duration_hours': 'float64',

        # Categorical columns - always int32
        'cluster': 'int32',
        'cluster_scheduling': 'int32',
        'collection_type': 'int32',
        'scheduler': 'int32',
        'scheduling_class': 'int32',

        # ID columns - always int64 (except machine_id which is float64)
        'alloc_collection_id': 'int64',
        'collection_id': 'int64',
        'instance_index': 'int64',
        'machine_id': 'float64',

        # Resource usage columns - always float64
        'assigned_memory': 'float64',
        'cycles_per_instruction': 'float64',
        'memory_accesses_per_instruction': 'float64',
        'page_cache_memory': 'float64',
        'priority': 'float64',
        'sample_rate': 'float64',

        # Newly extracted columns from JSON structures - always float64
        'resource_request_cpu': 'float64',
        'resource_request_memory': 'float64',
        'average_usage_cpu': 'float64',
        'average_usage_memory': 'float64',
        'maximum_usage_cpu': 'float64',
        'maximum_usage_memory': 'float64',
        'cpu_usage_distribution_mean': 'float64',
        'cpu_usage_distribution_median': 'float64',
        'cpu_usage_distribution_max': 'float64',

        # Other columns - specific types
        'collections_events_type': 'float64',
        'instance_events_type': 'float64',
        'vertical_scaling': 'int32'
    }

    # Single-phase approach to avoid chunk boundary issues
    logging.info("Processing data with single-phase approach")

    # Read the first chunk to determine column structure
    try:
        # Use a smaller chunk size for the first read to avoid memory issues
        first_chunk = pd.read_csv(input_file, nrows=100, low_memory=False)

        # Get initial column list from raw data
        raw_columns = list(first_chunk.columns)
        logging.info(f"Raw data has {len(raw_columns)} columns")

        # Process first chunk to get expected columns after processing
        first_processed = process_chunk(first_chunk)
        processed_columns = list(first_processed.columns)
        logging.info(f"Processed data will have {len(processed_columns)} columns")

        # Update schema with any missing columns
        missing_schema = [col for col in processed_columns if col not in schema]
        if missing_schema:
            logging.warning(f"Found {len(missing_schema)} columns not in schema: {missing_schema}")
            # Add missing columns to schema with default types
            for col in missing_schema:
                if col.endswith('_outlier'):
                    schema[col] = 'bool'
                elif any(x in col for x in ['_sin', '_cos', '_tan']):
                    schema[col] = 'float64'
                elif any(x in col for x in ['_hour', '_minute', '_second', '_weekday', '_day_part']):
                    schema[col] = 'int32'
                elif 'id' in col.lower():
                    schema[col] = 'int64'
                else:
                    schema[col] = 'float64'

        # Create a list of all expected columns in a consistent order
        all_columns = sorted(processed_columns)

        # Process the data in chunks with consistent handling
        chunks = pd.read_csv(input_file, chunksize=chunk_size, low_memory=False,
                            dtype={col: 'str' for col in raw_columns if col in ['failed', 'negative_duration', 'has_constraints']})

        # Initialize output file
        with open(output_file, 'w') as f:
            # Just create an empty file
            pass

        # Process chunks
        rows_processed = 0
        for i, chunk in enumerate(tqdm(chunks, total=(total_rows // chunk_size), desc="Processing")):
            try:
                # Process chunk
                processed_chunk = process_chunk(chunk)

                # Ensure all columns are present
                for col in all_columns:
                    if col not in processed_chunk.columns:
                        if schema.get(col) == 'bool':
                            processed_chunk[col] = False
                        elif schema.get(col) in ['int32', 'int64']:
                            processed_chunk[col] = 0
                        else:
                            processed_chunk[col] = 0.0

                # Apply schema with direct type casting
                for col in all_columns:
                    if col in schema:
                        dtype = schema[col]
                        try:
                            # Check for NaN values before conversion
                            nan_count = processed_chunk[col].isna().sum()
                            if nan_count > 0:
                                # Display message about NaN values
                                logging.info(f"Found {nan_count} NaN values in column '{col}' (chunk {i}), filling with 0 before conversion to {dtype}")

                            if dtype == 'bool':
                                # Convert to numeric first, then to boolean
                                # Using int8 as an intermediate step helps with consistency
                                processed_chunk[col] = pd.to_numeric(processed_chunk[col], errors='coerce').fillna(0).astype('int8').astype(bool)
                            else:
                                # Handle numeric columns
                                processed_chunk[col] = pd.to_numeric(processed_chunk[col], errors='coerce').fillna(0).astype(dtype)
                        except Exception as e:
                            logging.error(f"Error converting {col} to {dtype} in chunk {i}: {e}")
                            # Force conversion as a last resort
                            if dtype == 'bool':
                                processed_chunk[col] = False
                            elif dtype in ['int32', 'int64']:
                                processed_chunk[col] = 0
                            else:
                                processed_chunk[col] = 0.0

                # Save chunk with consistent columns and types
                processed_chunk[all_columns].to_csv(output_file, mode='a', index=False, header=(i==0))

                # Log progress
                rows_processed += len(chunk)
                if (i+1) % 5 == 0:
                    logging.info(f"Processed {rows_processed:,} rows ({i+1} chunks)")

                # Free memory
                del processed_chunk
                gc.collect()

            except Exception as e:
                logging.error(f"Error processing chunk {i}: {e}")
                # Continue with next chunk
                continue

        logging.info(f"Processing completed: {rows_processed:,} rows processed")

    except Exception as e:
        logging.error(f"Error during data processing: {e}")
        raise

    logging.info(f"Data cleaning completed: {output_file}")

    # Verify the output file
    try:
        # Read a sample from the output file to verify structure
        sample = pd.read_csv(output_file, nrows=10)
        logging.info(f"Output file contains {len(sample.columns)} columns")

        # Check for any missing columns
        missing_cols = set(all_columns) - set(sample.columns)
        if missing_cols:
            logging.warning(f"Output file is missing {len(missing_cols)} columns: {missing_cols}")

        # Check for any mixed datatypes
        for col in sample.columns:
            if col in schema:
                expected_type = schema[col]
                actual_type = str(sample[col].dtype)
                if expected_type == 'bool' and actual_type != 'bool':
                    logging.warning(f"Column {col} has type {actual_type}, expected bool")
                elif expected_type in ['int32', 'int64'] and 'int' not in actual_type:
                    logging.warning(f"Column {col} has type {actual_type}, expected {expected_type}")
                elif expected_type == 'float64' and 'float' not in actual_type:
                    logging.warning(f"Column {col} has type {actual_type}, expected float64")
    except Exception as e:
        logging.error(f"Error verifying output file: {e}")

def fix_negative_values(df, force=False):
    """Fix negative values in all non-trigonometric columns

    This function handles negative values in various column types:
    - Time components (hour, minute, second, weekday)
    - Categorical columns (day_part, duration_category)
    - Other numeric columns

    Trigonometric columns (sin, cos, tan) can legitimately have negative values,
    so they are preserved unless force=True.

    Args:
        df: DataFrame to fix
        force: If True, force all columns to be non-negative, even trigonometric ones
    """
    # Get all numeric columns
    numeric_cols = df.select_dtypes(include=['number']).columns.tolist()

    # Separate trigonometric columns from other columns
    trig_cols = [col for col in numeric_cols if any(x in col for x in ['_sin', '_cos', '_tan'])]
    non_trig_cols = [col for col in numeric_cols if col not in trig_cols]

    # Track columns with negative values for logging
    fixed_cols = {}

    # Define problematic columns that need special attention
    problem_cols = [
        'cluster_scheduling', 'time_weekday', 'time_day_part',
        'duration_category', 'duration_integer', 'duration_minutes', 'duration_hours',
        'start_time_minute', 'cluster', 'end_time_hour', 'start_time_hour',
        'time_hour', 'end_time_minute'
    ]

    # First pass: Fix known problematic columns directly
    for col in problem_cols:
        if col in df.columns and (df[col] < 0).any():
            neg_count = (df[col] < 0).sum()

            if col == 'cluster_scheduling':
                # Force to non-negative integers
                df.loc[df[col] < 0, col] = 0
                fixed_cols[col] = (neg_count, "forced to 0")
            elif col == 'cluster':
                # Force to non-negative integers
                df.loc[df[col] < 0, col] = df.loc[df[col] < 0, col].abs()
                fixed_cols[col] = (neg_count, "abs")
            elif col == 'duration_category':
                # Should be 0-5
                df.loc[df[col] < 0, col] = 0  # Default to 0 (negative duration)
                fixed_cols[col] = (neg_count, "forced to 0")
            elif col == 'duration_integer':
                # Should be non-negative
                df.loc[df[col] < 0, col] = 0  # Default to 0 for negative durations
                fixed_cols[col] = (neg_count, "forced to 0")
            elif col in ['duration_minutes', 'duration_hours']:
                # Should be non-negative
                df.loc[df[col] < 0, col] = 0  # Default to 0 for negative durations
                fixed_cols[col] = (neg_count, "forced to 0")
            elif col.endswith('_day_part'):
                # Should be 0-3
                df.loc[df[col] < 0, col] = 0  # Default to 0 (night)
                fixed_cols[col] = (neg_count, "forced to 0")
            elif col.endswith('_hour'):
                # Hour should be 0-23
                df.loc[df[col] < 0, col] = 0  # Default to 0 hour
                fixed_cols[col] = (neg_count, "forced to 0")
            elif col.endswith('_minute'):
                # Minute should be 0-59
                df.loc[df[col] < 0, col] = 0  # Default to 0 minute
                fixed_cols[col] = (neg_count, "forced to 0")
            elif col.endswith('_second'):
                # Second should be 0-59
                df.loc[df[col] < 0, col] = 0  # Default to 0 second
                fixed_cols[col] = (neg_count, "forced to 0")
            elif col.endswith('_weekday'):
                # Weekday should be 0-6
                df.loc[df[col] < 0, col] = 0  # Default to 0 (Monday)
                fixed_cols[col] = (neg_count, "forced to 0")

    # Columns to process for general fixing
    cols_to_process = numeric_cols if force else non_trig_cols

    # Second pass: Fix remaining columns
    for col in cols_to_process:
        if col in df.columns and (df[col] < 0).any() and col not in problem_cols:
            neg_count = (df[col] < 0).sum()

            # Special handling for specific column types
            if col.endswith('_hour'):
                # Hour should be 0-23
                df.loc[df[col] < 0, col] = 0  # Default to 0 hour
                fixed_cols[col] = (neg_count, "forced to 0")
            elif col.endswith('_minute') or col.endswith('_second'):
                # Minute and second should be 0-59
                df.loc[df[col] < 0, col] = 0  # Default to 0
                fixed_cols[col] = (neg_count, "forced to 0")
            elif col.endswith('_weekday'):
                # Weekday should be 0-6
                df.loc[df[col] < 0, col] = 0  # Default to 0 (Monday)
                fixed_cols[col] = (neg_count, "forced to 0")
            elif col.endswith('_day_part'):
                # Day part should be 0-3
                df.loc[df[col] < 0, col] = 0  # Default to 0 (night)
                fixed_cols[col] = (neg_count, "forced to 0")
            elif col == 'duration_category':
                # Duration category should be 0-5
                df.loc[df[col] < 0, col] = 0  # Default to 0 (negative)
                fixed_cols[col] = (neg_count, "forced to 0")
            elif any(x in col for x in ['_sin', '_cos']) and force:
                # Sin/cos values should be between -1 and 1, but we're forcing non-negative
                df.loc[df[col] < 0, col] = 0  # Default to 0
                fixed_cols[col] = (neg_count, "forced to 0")
            elif '_tan' in col and force:
                # Tan can have any value, but we're forcing non-negative
                df.loc[df[col] < 0, col] = 0  # Default to 0
                fixed_cols[col] = (neg_count, "forced to 0")
            # Handle the newly extracted columns
            elif col.startswith(('resource_request_', 'average_usage_', 'maximum_usage_', 'cpu_usage_distribution_')):
                # These should be non-negative resource usage values
                df.loc[df[col] < 0, col] = 0  # Default to 0
                fixed_cols[col] = (neg_count, "forced to 0")
            else:
                # For all other columns, force to 0
                df.loc[df[col] < 0, col] = 0
                fixed_cols[col] = (neg_count, "forced to 0")

    # Log the fixes if any were made
    if fixed_cols and len(fixed_cols) > 0:
        log_msg = f"Fixed negative values in {len(fixed_cols)} columns: "
        log_details = [f"{col}: {count} values ({fix_type})" for col, (count, fix_type) in fixed_cols.items()]
        if len(log_details) <= 5:
            log_msg += ", ".join(log_details)
        else:
            log_msg += ", ".join(log_details[:5]) + f", and {len(log_details) - 5} more columns"
        logging.info(log_msg)

    # Verify no negative values remain in specified columns
    cols_to_check = numeric_cols if force else non_trig_cols
    remaining_neg = {col: (df[col] < 0).sum() for col in cols_to_check if (df[col] < 0).sum() > 0}
    if remaining_neg:
        logging.warning(f"Negative values still present in {len(remaining_neg)} columns after fixing: {list(remaining_neg.keys())}")
        # Final aggressive approach for any remaining negative values
        for col in remaining_neg:
            df.loc[df[col] < 0, col] = 0
            logging.info(f"Forced column {col} negative values to zero")

    # Final verification
    final_check = {col: (df[col] < 0).sum() for col in cols_to_check if (df[col] < 0).sum() > 0}
    if final_check:
        logging.error(f"CRITICAL: Negative values still present after aggressive fixing: {final_check}")

    return df

def process_chunk(chunk):
    """Process a chunk of data with all cleaning steps"""
    try:
        # Create a fresh copy
        df = chunk.copy()

        # Handle problematic data in the raw input
        for col in df.select_dtypes(include=['object']).columns:
            # Replace empty strings, 'None', 'nan', etc. with NaN
            df[col] = df[col].replace(['', 'None', 'none', 'NaN', 'nan', 'NULL', 'null'], np.nan)

            # Handle boolean columns specially
            if col in ['failed', 'negative_duration', 'has_constraints']:
                df[col] = df[col].map({
                    '1': True, '0': False, 'True': True, 'False': False,
                    'true': True, 'false': False, 1: True, 0: False,
                    'yes': True, 'no': False, 'Y': True, 'N': False,
                    'y': True, 'n': False
                })

        # Apply cleaning steps in sequence
        df = remove_unnecessary_columns(df)
        df = parse_nested_structures(df)
        df = df.copy()  # Defragment after adding columns
        df = convert_data_types(df)

        # Fix negative timestamps
        for col in ['time', 'start_time', 'end_time']:
            if col in df.columns:
                df[col] = pd.to_numeric(df[col], errors='coerce').fillna(0)
                df.loc[df[col] < 0, col] = 0

        # Apply feature engineering
        df = extract_temporal_features(df)
        df = df.copy()  # Defragment after feature engineering
        df = engineer_features(df)
        df = handle_outliers(df)
        df = handle_missing_values(df)
        df = fix_negative_values(df, force=False)

        # Check for and fill any remaining missing values
        missing_cols = [col for col in df.columns if df[col].isna().any()]
        if missing_cols:
            total_remaining_nans = sum(df[col].isna().sum() for col in missing_cols)
            logging.info(f"Found {total_remaining_nans} remaining NaN values across {len(missing_cols)} columns after all processing")

            for col in missing_cols:
                nan_count = df[col].isna().sum()
                fill_value = 0 if pd.api.types.is_numeric_dtype(df[col]) else 'unknown'
                logging.info(f"Filling {nan_count} remaining NaN values with {fill_value} in column '{col}'")
                df[col] = df[col].fillna(fill_value)

        return df.copy()  # Return defragmented dataframe

    except Exception as e:
        logging.error(f"Error in process_chunk: {e}")
        return pd.DataFrame()



def remove_unnecessary_columns(df):
    """Remove columns not needed for GMM-DQN-LSTM prediction

    Note: We now extract useful data from JSON-like columns before dropping them
    """
    # Define columns to drop
    drop_cols = ['Unnamed: 0', 'tail_cpu_usage_distribution',
                'random_sample_usage', 'random_sample_usage_memory',
                'user', 'collection_name', 'collection_logical_name',
                'start_after_collection_ids', 'constraint', 'event']

    # Only drop these columns after we've extracted their data in parse_nested_structures
    json_cols_to_drop = ['cpu_usage_distribution', 'resource_request',
                         'average_usage', 'maximum_usage']

    # Check if we have the extracted columns before dropping the source columns
    for json_col in json_cols_to_drop:
        if json_col in df.columns:
            # For each JSON column, check if we have the corresponding extracted columns
            if json_col == 'resource_request':
                if 'resource_request_cpu' in df.columns and 'resource_request_memory' in df.columns:
                    drop_cols.append(json_col)
            elif json_col == 'average_usage':
                if 'average_usage_cpu' in df.columns and 'average_usage_memory' in df.columns:
                    drop_cols.append(json_col)
            elif json_col == 'maximum_usage':
                if 'maximum_usage_cpu' in df.columns and 'maximum_usage_memory' in df.columns:
                    drop_cols.append(json_col)
            elif json_col == 'cpu_usage_distribution':
                if all(col in df.columns for col in ['cpu_usage_distribution_mean',
                                                    'cpu_usage_distribution_median',
                                                    'cpu_usage_distribution_max']):
                    drop_cols.append(json_col)

    # Drop columns that exist
    existing_cols = [col for col in drop_cols if col in df.columns]
    if existing_cols:
        df = df.drop(columns=existing_cols)
        logging.info(f"Dropped {len(existing_cols)} unnecessary columns")

    return df

def handle_missing_values(df):
    """Handle missing values in all columns"""
    # Replace empty strings with NaN
    df.replace('', np.nan, inplace=True)

    # Count total NaN values before handling
    total_nan_before = df.isna().sum().sum()
    if total_nan_before > 0:
        logging.info(f"Found {total_nan_before} NaN values across all columns before handling missing values")

        # Log columns with significant NaN counts
        nan_counts = df.isna().sum()
        significant_nans = {col: count for col, count in nan_counts.items() if count > 0 and count > len(df) * 0.01}
        if significant_nans:
            for col, count in significant_nans.items():
                percent = (count / len(df)) * 100
                logging.info(f"Column '{col}' has {count} NaN values ({percent:.2f}% of data)")

    # Handle categorical columns
    cat_cols = df.select_dtypes(include=['object']).columns.tolist()
    cat_missing_cols = [col for col in cat_cols if df[col].isna().sum() > 0]

    if cat_missing_cols:
        logging.info(f"Filling NaN values in {len(cat_missing_cols)} categorical columns with 'unknown'")
        for col in cat_missing_cols:
            nan_count = df[col].isna().sum()
            if nan_count > 0:
                logging.info(f"Filling {nan_count} NaN values in categorical column '{col}' with 'unknown'")
                df[col] = df[col].fillna('unknown')

    # Handle numeric columns with MICE
    num_cols = df.select_dtypes(include=['number']).columns.tolist()
    missing_cols = [col for col in num_cols if df[col].isna().sum() > 0]

    if missing_cols:
        logging.info(f"Found {len(missing_cols)} numeric columns with NaN values")

        # Use MICE for columns with <50% missing
        impute_cols = [col for col in missing_cols if df[col].isna().sum() / len(df) < 0.5]
        if impute_cols:
            logging.info(f"Using MICE imputation for {len(impute_cols)} columns with <50% missing values")
            try:
                imputer = IterativeImputer(max_iter=10, random_state=42)
                imputed_data = imputer.fit_transform(df[impute_cols].copy())
                for i, col in enumerate(impute_cols):
                    nan_count = df[col].isna().sum()
                    df[col] = imputed_data[:, i]
                    logging.info(f"MICE imputation filled {nan_count} NaN values in column '{col}'")
            except Exception as e:
                logging.warning(f"MICE imputation failed: {e}. Using simple imputation with 0 instead.")
                for col in impute_cols:
                    nan_count = df[col].isna().sum()
                    df[col] = df[col].fillna(0)
                    logging.info(f"Filled {nan_count} NaN values with 0 in column '{col}' after MICE failure")

        # Simple imputation for remaining columns
        simple_impute_cols = [col for col in missing_cols if col not in impute_cols or df[col].isna().sum() > 0]
        if simple_impute_cols:
            logging.info(f"Using simple imputation with 0 for {len(simple_impute_cols)} columns")
            for col in simple_impute_cols:
                nan_count = df[col].isna().sum()
                if nan_count > 0:
                    logging.info(f"Filling {nan_count} NaN values with 0 in numeric column '{col}'")
                    df[col] = df[col].fillna(0)

    # Verify no NaN values remain
    total_nan_after = df.isna().sum().sum()
    if total_nan_after > 0:
        logging.warning(f"WARNING: {total_nan_after} NaN values still remain after handling missing values")
        # Force fill any remaining NaNs with 0
        for col in df.columns:
            if df[col].isna().sum() > 0:
                df[col] = df[col].fillna(0 if pd.api.types.is_numeric_dtype(df[col]) else 'unknown')
                logging.info(f"Force filled remaining NaN values in column '{col}'")
    else:
        logging.info("Successfully handled all missing values - no NaN values remain")

    return df

def convert_data_types(df):
    """Convert columns to appropriate data types"""
    # Define type mapping dictionary
    type_map = {
        # Boolean columns
        **{col: 'bool' for col in df.columns if col.endswith('_outlier') or col in ['failed', 'negative_duration', 'has_constraints']},

        # Time components
        **{col: 'int32' for col in df.columns if any(x in col for x in ['_hour', '_minute', '_second', '_weekday'])
           and not any(x in col for x in ['_sin', '_cos', '_tan'])},
        **{col: 'int32' for col in df.columns if '_day_part' in col},

        # Timestamps
        **{col: 'float64' for col in ['time', 'start_time', 'end_time'] if col in df.columns},

        # Trigonometric columns
        **{col: 'float64' for col in df.columns if any(x in col for x in ['_sin', '_cos', '_tan'])
           and not col.endswith('_outlier')},

        # Duration columns
        'duration_integer': 'int64',
        'duration_category': 'int32',
        'duration_seconds': 'float64',
        'duration_minutes': 'float64',
        'duration_hours': 'float64',

        # Categorical columns
        **{col: 'int32' for col in ['cluster', 'cluster_scheduling', 'collection_type', 'scheduler', 'scheduling_class',
                                   'instance_index', 'priority', 'instance_events_type',
                                   'collections_events_type', 'vertical_scaling'] if col in df.columns},

        # ID columns
        **{col: 'int64' for col in df.columns if 'id' in col.lower() and not col.endswith('_outlier')},
        'machine_id': 'float64',

        # Resource usage columns
        **{col: 'float64' for col in df.columns if any(x in col for x in ['memory', 'cpu', 'usage'])
           and not col.endswith('_outlier')},

        # Newly extracted columns from JSON structures
        **{col: 'float64' for col in df.columns if col.startswith(('resource_request_', 'average_usage_',
                                                                  'maximum_usage_', 'cpu_usage_distribution_'))}
    }

    # Check for NaN values before conversion
    nan_columns = {}
    for col in df.columns:
        nan_count = df[col].isna().sum()
        if nan_count > 0:
            nan_columns[col] = nan_count

    # Log NaN values if found
    if nan_columns:
        total_nan_values = sum(nan_columns.values())
        logging.info(f"Found {total_nan_values} NaN values across {len(nan_columns)} columns before type conversion")

        # Log details for columns with significant NaN counts
        significant_nans = {col: count for col, count in nan_columns.items() if count > len(df) * 0.01}  # >1% of data
        if significant_nans:
            for col, count in significant_nans.items():
                percent = (count / len(df)) * 100
                logging.info(f"Column '{col}' has {count} NaN values ({percent:.2f}% of data)")

        # Log summary for less significant NaN columns
        minor_nans = {col: count for col, count in nan_columns.items() if count <= len(df) * 0.01}
        if minor_nans:
            logging.info(f"Additional {len(minor_nans)} columns have minor NaN counts (<1% of data)")

    # Apply type conversions
    for col in df.columns:
        dtype = type_map.get(col)
        if dtype:
            try:
                # Check for NaN values in this specific column
                nan_count = df[col].isna().sum()
                if nan_count > 0:
                    logging.info(f"Filling {nan_count} NaN values with 0 in column '{col}' before conversion to {dtype}")

                if dtype == 'bool':
                    if df[col].dtype == 'object':
                        df[col] = df[col].map({'True': 1, 'False': 0, 'true': 1, 'false': 0,
                                              '1': 1, '0': 0, 1: 1, 0: 0, True: 1, False: 0})
                    df[col] = pd.to_numeric(df[col], errors='coerce').fillna(0).astype(bool)
                else:
                    df[col] = pd.to_numeric(df[col], errors='coerce').fillna(0).astype(dtype)
            except Exception as e:
                logging.warning(f"Error converting column '{col}' to {dtype}: {e}")
                if dtype == 'bool':
                    df[col] = False
                elif dtype in ['int32', 'int64']:
                    df[col] = 0
                else:
                    df[col] = 0.0
        else:
            try:
                # Check for NaN values in columns without predefined types
                nan_count = df[col].isna().sum()
                if nan_count > 0:
                    logging.info(f"Filling {nan_count} NaN values with 0 in column '{col}' (no predefined type)")

                df[col] = pd.to_numeric(df[col], errors='coerce').fillna(0)
                df[col] = df[col].astype('int32' if (df[col] == df[col].astype(int)).all() else 'float64')
            except:
                pass

    return df

def extract_temporal_features(df):
    """Extract cyclical temporal features from unix timestamps"""
    for col in ['time', 'start_time', 'end_time']:
        if col in df.columns and df[col].notna().any():
            # Ensure numeric and fix negative values
            df[col] = pd.to_numeric(df[col], errors='coerce').fillna(0)
            df.loc[df[col] < 0, col] = 0

            # Extract time components efficiently
            ts = df[col]
            seconds_per_day, seconds_per_hour = 86400, 3600

            # Calculate components and store directly with proper types
            df[f'{col}_hour'] = ((ts % seconds_per_day) // seconds_per_hour).clip(0, 23).astype('int32')
            df[f'{col}_minute'] = ((ts % seconds_per_hour) // 60).clip(0, 59).astype('int32')
            df[f'{col}_second'] = (ts % 60).clip(0, 59).astype('int32')
            df[f'{col}_weekday'] = ((ts // seconds_per_day) + 4).clip(0, 6).astype('int32')

            # Generate cyclical features for all components at once
            for comp, (cycle, col_name) in {
                'hour': (24, f'{col}_hour'),
                'minute': (60, f'{col}_minute'),
                'second': (60, f'{col}_second'),
                'weekday': (7, f'{col}_weekday')
            }.items():
                # Calculate angle once
                angle = 2 * np.pi * df[col_name] / cycle

                # Generate sin/cos features with consistent float64 type
                df[f'{col}_{comp}_sin'] = np.sin(angle).astype('float64')
                df[f'{col}_{comp}_cos'] = np.cos(angle).astype('float64')

                # Handle tan values with proper bounds
                tan_vals = np.tan(angle)
                df[f'{col}_{comp}_tan'] = np.nan_to_num(tan_vals, nan=0.0, posinf=1e6, neginf=-1e6).astype('float64')

            # Create day part categories
            df[f'{col}_day_part'] = pd.cut(
                df[f'{col}_hour'],
                bins=[0, 6, 12, 18, 24],
                labels=[0, 1, 2, 3],
                include_lowest=True
            ).astype('int32')

            # Calculate duration if needed
            if col == 'start_time' and 'end_time' in df.columns:
                df = calculate_duration(df)

    return df

def calculate_duration(df):
    """Calculate duration and create duration-related features"""
    # Ensure numeric timestamps
    df['start_time'] = pd.to_numeric(df['start_time'], errors='coerce').fillna(0)
    df['end_time'] = pd.to_numeric(df['end_time'], errors='coerce').fillna(0)

    # Calculate duration and create negative flag
    df['duration_seconds'] = df['end_time'] - df['start_time']
    df['negative_duration'] = (df['duration_seconds'] < 0).astype(bool)

    # Use absolute value for calculations
    duration_abs = df['duration_seconds'].abs()

    # Calculate all duration metrics at once with proper types
    df['duration_minutes'] = (duration_abs / 60).astype('float64')
    df['duration_hours'] = (df['duration_minutes'] / 60).astype('float64')
    df['duration_integer'] = duration_abs.astype('int64')

    # Set duration_integer to 0 for negative durations
    df.loc[df['negative_duration'], 'duration_integer'] = 0

    # Create duration categories efficiently
    minutes = df['duration_minutes']
    df['duration_category'] = np.select(
        [
            df['negative_duration'],
            (~df['negative_duration']) & (minutes <= 5),
            (minutes > 5) & (minutes <= 15),
            (minutes > 15) & (minutes <= 60),
            (minutes > 60) & (minutes <= 360),
            (minutes > 360)
        ],
        [0, 1, 2, 3, 4, 5],
        default=0
    ).astype('int32')

    # Ensure consistent types for all duration columns
    df['duration_seconds'] = df['duration_seconds'].astype('float64')

    return df

def engineer_features(df):
    """Create new features from existing data"""
    # Dictionary to hold new columns
    new_columns = {}

    # Cluster and scheduling combination
    if all(col in df.columns for col in ['cluster', 'scheduling_class']):
        # Ensure cluster and scheduling_class are non-negative
        neg_cluster = (df['cluster'] < 0).sum()
        neg_scheduling = (df['scheduling_class'] < 0).sum()

        if neg_cluster > 0:
            logging.info(f"Found {neg_cluster} negative values in cluster column, forcing to 0")
            # Force negative values to 0 instead of using abs()
            df.loc[df['cluster'] < 0, 'cluster'] = 0

        if neg_scheduling > 0:
            logging.info(f"Found {neg_scheduling} negative values in scheduling_class column, forcing to 0")
            # Force negative values to 0 instead of using abs()
            df.loc[df['scheduling_class'] < 0, 'scheduling_class'] = 0

        # Double-check for any remaining negative values
        if (df['cluster'] < 0).any() or (df['scheduling_class'] < 0).any():
            logging.warning("Still found negative values in cluster or scheduling_class after fixing")
            df.loc[df['cluster'] < 0, 'cluster'] = 0
            df.loc[df['scheduling_class'] < 0, 'scheduling_class'] = 0

        # Create a numeric encoding for cluster_scheduling
        # Use a more efficient approach to avoid fragmentation
        temp_col = df['cluster'].astype(str) + '_' + df['scheduling_class'].astype(str)
        unique_vals = temp_col.unique()
        mapping = {val: i for i, val in enumerate(unique_vals)}
        new_columns['cluster_scheduling'] = temp_col.map(mapping).fillna(0).astype(int)

        # Ensure cluster_scheduling is non-negative
        neg_mask = new_columns['cluster_scheduling'] < 0
        if neg_mask.any():
            neg_count = neg_mask.sum()
            logging.warning(f"Found {neg_count} negative values in cluster_scheduling, forcing to 0")
            new_columns['cluster_scheduling'][neg_mask] = 0

    # Constraint features
    if 'constraint_count' in df.columns:
        new_columns['has_constraints'] = df['constraint_count'] > 0

    # Add all new columns at once to avoid fragmentation
    if new_columns:
        # Create a DataFrame from the new columns
        new_df = pd.DataFrame(new_columns, index=df.index)

        # Remove any existing columns that will be replaced
        existing_cols = [col for col in df.columns if col in new_columns]
        if existing_cols:
            df = df.drop(columns=existing_cols)

        # Join the new DataFrame to the original DataFrame
        df = pd.concat([df, new_df], axis=1)

    return df

def parse_nested_structures(df):
    """Parse nested structures like JSON and extract values from dictionary-like strings"""
    # Create a dictionary to hold all new columns to avoid DataFrame fragmentation
    new_columns = {}

    # Parse constraint column if it exists
    if 'constraint' in df.columns:
        try:
            new_columns['constraint_count'] = df['constraint'].apply(
                lambda x: len(x.split(',')) if isinstance(x, str) and x != '' else 0)
        except Exception as e:
            logging.warning(f"Error parsing constraint column: {e}")
            new_columns['constraint_count'] = 0

    # Extract CPU and memory values from resource_request
    if 'resource_request' in df.columns:
        try:
            # Use literal_eval to safely parse the dictionary-like strings
            new_columns['resource_request_cpu'] = df['resource_request'].apply(
                lambda x: extract_value_from_dict_str(x, 'cpus', float) if pd.notna(x) else np.nan)
            new_columns['resource_request_memory'] = df['resource_request'].apply(
                lambda x: extract_value_from_dict_str(x, 'memory', float) if pd.notna(x) else np.nan)
            logging.info(f"Extracted CPU and memory from resource_request column")
        except Exception as e:
            logging.warning(f"Error extracting from resource_request: {e}")
            # Create empty columns if extraction fails
            new_columns['resource_request_cpu'] = np.nan
            new_columns['resource_request_memory'] = np.nan

    # Extract CPU and memory values from average_usage
    if 'average_usage' in df.columns:
        try:
            new_columns['average_usage_cpu'] = df['average_usage'].apply(
                lambda x: extract_value_from_dict_str(x, 'cpus', float) if pd.notna(x) else np.nan)
            new_columns['average_usage_memory'] = df['average_usage'].apply(
                lambda x: extract_value_from_dict_str(x, 'memory', float) if pd.notna(x) else np.nan)
            logging.info(f"Extracted CPU and memory from average_usage column")
        except Exception as e:
            logging.warning(f"Error extracting from average_usage: {e}")
            new_columns['average_usage_cpu'] = np.nan
            new_columns['average_usage_memory'] = np.nan

    # Extract CPU and memory values from maximum_usage
    if 'maximum_usage' in df.columns:
        try:
            new_columns['maximum_usage_cpu'] = df['maximum_usage'].apply(
                lambda x: extract_value_from_dict_str(x, 'cpus', float) if pd.notna(x) else np.nan)
            new_columns['maximum_usage_memory'] = df['maximum_usage'].apply(
                lambda x: extract_value_from_dict_str(x, 'memory', float) if pd.notna(x) else np.nan)
            logging.info(f"Extracted CPU and memory from maximum_usage column")
        except Exception as e:
            logging.warning(f"Error extracting from maximum_usage: {e}")
            new_columns['maximum_usage_cpu'] = np.nan
            new_columns['maximum_usage_memory'] = np.nan

    # Extract and process cpu_usage_distribution
    if 'cpu_usage_distribution' in df.columns:
        try:
            # Extract the list of values and calculate statistics using the consolidated function
            for stat in ['mean', 'median', 'max']:
                new_columns[f'cpu_usage_distribution_{stat}'] = df['cpu_usage_distribution'].apply(
                    lambda x: calculate_array_stat(x, stat) if pd.notna(x) else np.nan)
            logging.info(f"Extracted statistics from cpu_usage_distribution column")
        except Exception as e:
            logging.warning(f"Error extracting from cpu_usage_distribution: {e}")
            for stat in ['mean', 'median', 'max']:
                new_columns[f'cpu_usage_distribution_{stat}'] = np.nan

    # Fill NaN values with 0 for all new columns
    for col in new_columns:
        new_columns[col] = new_columns[col].fillna(0)

    # Add all new columns at once to avoid fragmentation
    if new_columns:
        # Create a DataFrame from the new columns
        new_df = pd.DataFrame(new_columns, index=df.index)

        # Remove any existing columns that will be replaced
        existing_cols = [col for col in df.columns if col in new_columns]
        if existing_cols:
            df = df.drop(columns=existing_cols)

        # Join the new DataFrame to the original DataFrame
        df = pd.concat([df, new_df], axis=1)

    return df

def extract_value_from_dict_str(dict_str, key, dtype=float):
    """Extract a value from a dictionary-like string

    Args:
        dict_str: String representation of a dictionary
        key: Key to extract
        dtype: Type to convert the value to

    Returns:
        Extracted value converted to the specified type, or 0 if extraction fails
    """
    try:
        # Handle different string formats
        if isinstance(dict_str, str):
            # Clean the string for literal_eval
            dict_str = dict_str.replace("'", '"').strip()

            # Try different parsing approaches
            try:
                # Try json.loads first (faster)
                data = json.loads(dict_str)
            except:
                try:
                    # Try literal_eval as fallback
                    data = literal_eval(dict_str)
                except:
                    # Last resort: manual parsing for simple cases
                    if key in dict_str:
                        # Extract value using regex-like approach
                        parts = dict_str.split(key + "':")
                        if len(parts) > 1:
                            value_part = parts[1].split(",")[0].strip()
                            return dtype(value_part)
                        return 0
                    return 0

            # Extract the value from the parsed dictionary
            if isinstance(data, dict) and key in data:
                return dtype(data[key]) if data[key] is not None else 0
        return 0
    except:
        return 0

def calculate_array_stat(array_str, stat_func='mean'):
    """Calculate statistics from an array-like string

    Args:
        array_str: String representation of an array
        stat_func: Statistic to calculate ('mean', 'median', or 'max')

    Returns:
        Calculated statistic or 0 if calculation fails
    """
    try:
        values = extract_array_values(array_str)
        if len(values) == 0:
            return 0

        if stat_func == 'mean':
            return np.mean(values)
        elif stat_func == 'median':
            return np.median(values)
        elif stat_func == 'max':
            return np.max(values)
        return 0
    except:
        return 0

# Define specific functions for backward compatibility
def calculate_array_mean(array_str):
    return calculate_array_stat(array_str, 'mean')

def calculate_array_median(array_str):
    return calculate_array_stat(array_str, 'median')

def calculate_array_max(array_str):
    return calculate_array_stat(array_str, 'max')

def extract_array_values(array_str):
    """Extract numeric values from an array-like string"""
    if not isinstance(array_str, str) or not array_str.strip():
        return np.array([])

    # Clean the string
    array_str = array_str.strip()

    # Fast path for common case: array in brackets with space-separated values
    if array_str.startswith('[') and array_str.endswith(']'):
        try:
            return np.fromstring(array_str[1:-1], sep=' ')
        except:
            # If fromstring fails, continue with other methods
            pass

    # Try json.loads for valid JSON arrays
    try:
        values = json.loads(array_str)
        if isinstance(values, list):
            return np.array(values, dtype=float)
    except:
        pass

    # Try literal_eval as fallback
    try:
        values = literal_eval(array_str)
        return np.array(values, dtype=float)
    except:
        pass

    # Last resort: manual parsing for simple cases
    if array_str.startswith('[') and array_str.endswith(']'):
        try:
            values = array_str[1:-1].split()
            return np.array([float(v) for v in values if v.strip()])
        except:
            pass

    return np.array([])

def handle_outliers(df):
    """Detect and handle outliers in numeric columns"""
    # Get numeric columns (excluding IDs and timestamps)
    num_cols = [col for col in df.select_dtypes(include=['number']).columns
               if not ('id' in col.lower() or col in ['time', 'start_time', 'end_time'])]

    # Store outlier flags
    outlier_flags = {}

    # Process each numeric column
    for col in num_cols:
        try:
            # Skip columns with insufficient unique values
            if df[col].nunique() < 5 or (df[col] == 0).all():
                continue

            # Calculate IQR and bounds
            Q1, Q3 = df[col].quantile(0.25), df[col].quantile(0.75)
            IQR = Q3 - Q1

            # Skip columns with zero IQR
            if IQR == 0:
                continue

            # Calculate bounds and flag outliers
            lower, upper = Q1 - 1.5 * IQR, Q3 + 1.5 * IQR
            outliers = ((df[col] < lower) | (df[col] > upper))

            # Only create outlier columns if outliers are found
            if outliers.sum() > 0:
                outlier_flags[f'{col}_outlier'] = outliers

                # Cap resource usage outliers
                if any(x in col for x in ['usage', 'memory', 'cpu']):
                    df.loc[df[col] < lower, col] = lower
                    df.loc[df[col] > upper, col] = upper
        except:
            continue

    # Create a dictionary of all outlier flags
    outlier_dict = {}
    for col, flags in outlier_flags.items():
        outlier_dict[col] = flags.astype(bool)

    # Add all outlier flags at once to avoid fragmentation
    if outlier_dict:
        # Create a DataFrame from the outlier flags and join it to the original DataFrame
        outlier_df = pd.DataFrame(outlier_dict, index=df.index)

        # Remove any existing outlier columns that will be replaced
        existing_outlier_cols = [col for col in df.columns if col in outlier_dict]
        if existing_outlier_cols:
            df = df.drop(columns=existing_outlier_cols)

        # Join the outlier DataFrame to the original DataFrame
        df = pd.concat([df, outlier_df], axis=1)

    # Ensure all existing outlier columns are boolean
    outlier_cols = [col for col in df.columns if col.endswith('_outlier')]
    if outlier_cols:
        # Convert all outlier columns to boolean in one operation
        for col in outlier_cols:
            if df[col].dtype == 'object':
                df[col] = df[col].map({'True': 1, 'False': 0, 'true': 1, 'false': 0,
                                      '1': 1, '0': 0, 1: 1, 0: 0, True: 1, False: 0})

        # Convert all numeric outlier columns to boolean at once
        df[outlier_cols] = df[outlier_cols].fillna(0).astype(bool)

    return df

def main():
    """Main function to run data cleaning process"""
    input_file = "data/raw/borg_traces_dataa.csv"
    output_file = "data/processed/borg_traces_cleaned.csv"

    # Check input file
    if not os.path.exists(input_file):
        logging.error(f"Input file {input_file} not found")
        print(f"Error: Input file {input_file} not found")
        return

    # Remove existing output file if it exists
    if os.path.exists(output_file):
        os.remove(output_file)
        logging.info(f"Removed existing output file: {output_file}")

    # Run cleaning process
    logging.info("Starting data cleaning for the entire dataset")
    print("Starting data cleaning process...")
    print("Extracting CPU/memory values from JSON structures and applying optimizations")

    try:
        start_time = time.time()
        clean_data(input_file, output_file, chunk_size=1000)
        elapsed_time = time.time() - start_time

        # Print completion message
        print(f"Data cleaning completed in {elapsed_time:.2f} seconds.")
        print(f"Cleaned data saved to: {output_file}")

        # Verify the output file exists and has data
        if os.path.exists(output_file):
            file_size = os.path.getsize(output_file) / (1024 * 1024)  # Size in MB
            print(f"Output file size: {file_size:.2f} MB")

            # Verify file structure
            sample = pd.read_csv(output_file, nrows=1)
            print(f"Output contains {len(sample.columns)} columns")
        else:
            print("Warning: Output file not found after processing")
    except Exception as e:
        logging.error(f"Error: {e}")
        print(f"Error: {e}")

if __name__ == "__main__":
    main()
