"""
Generate a sample labeled anomaly dataset for testing the augmentation functionality.
This script creates a CSV file with labeled anomalies based on the original dataset.
"""
import pandas as pd
import numpy as np
import os
from sklearn.preprocessing import StandardScaler

def generate_labeled_anomalies(input_file, output_file, anomaly_ratio=0.1, magnitude=3.0):
    """Generate a labeled anomaly dataset based on the original dataset
    
    Args:
        input_file: Path to the original dataset
        output_file: Path to save the labeled anomaly dataset
        anomaly_ratio: Ratio of anomalies to generate
        magnitude: Magnitude of anomalies
    """
    print(f"Generating labeled anomaly dataset from {input_file}...")
    
    # Load original dataset
    try:
        df = pd.read_csv(input_file)
        print(f"Loaded original dataset with {df.shape[0]} rows and {df.shape[1]} columns")
    except Exception as e:
        print(f"Error loading original dataset: {e}")
        return
    
    # Create a copy of the dataset
    anomaly_df = df.copy()
    
    # Add anomaly column
    anomaly_df['anomaly'] = 0
    
    # Select random rows to make anomalies
    n_samples = anomaly_df.shape[0]
    n_anomalies = int(n_samples * anomaly_ratio)
    anomaly_indices = np.random.choice(n_samples, n_anomalies, replace=False)
    
    # Get numeric columns
    numeric_cols = anomaly_df.select_dtypes(include=['number']).columns.tolist()
    if 'anomaly' in numeric_cols:
        numeric_cols.remove('anomaly')
    
    # Create anomalies by modifying feature values
    for idx in anomaly_indices:
        # Select random features to modify
        n_features_to_modify = np.random.randint(1, max(2, len(numeric_cols) // 3))
        features_to_modify = np.random.choice(numeric_cols, n_features_to_modify, replace=False)
        
        # Modify selected features
        for feature in features_to_modify:
            if np.random.random() > 0.5:
                anomaly_df.loc[idx, feature] += magnitude * anomaly_df[feature].std()
            else:
                anomaly_df.loc[idx, feature] -= magnitude * anomaly_df[feature].std()
        
        # Mark as anomaly
        anomaly_df.loc[idx, 'anomaly'] = 1
    
    # Save labeled anomaly dataset
    os.makedirs(os.path.dirname(output_file), exist_ok=True)
    anomaly_df.to_csv(output_file, index=False)
    
    print(f"Generated labeled anomaly dataset with {n_anomalies} anomalies")
    print(f"Saved to {output_file}")

def main():
    """Main function to generate labeled anomaly dataset"""
    input_file = "data/processed/borg_traces_cleaned.csv"
    output_file = "data/processed/labeled_anomalies.csv"
    
    generate_labeled_anomalies(input_file, output_file)

if __name__ == "__main__":
    main()
