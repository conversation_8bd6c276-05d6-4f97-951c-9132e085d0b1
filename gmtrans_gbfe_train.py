import argparse,os,time,json,numpy as np,pandas as pd,torch,warnings,gc,traceback
import matplotlib.pyplot as plt
from sklearn.model_selection import train_test_split
from sklearn.metrics import (mean_squared_error, mean_absolute_error, r2_score, confusion_matrix,
                           silhouette_score, calinski_harabasz_score, davies_bouldin_score,
                           roc_auc_score, precision_recall_curve, auc)
from sklearn.preprocessing import StandardScaler, RobustScaler, MinMaxScaler, QuantileTransformer, PowerTransformer
from sklearn.ensemble import IsolationForest
from sklearn.neighbors import LocalOutlierFactor
from sklearn.svm import OneClassSVM
from sklearn.metrics.pairwise import cosine_similarity
from sklearn.decomposition import PCA
from sklearn.mixture import GaussianMixture
from sklearn.cluster import KMeans
from joblib import Parallel, delayed, parallel_backend
from tqdm import tqdm
from models.gmtrans_gbfe import GMTransGBFEWrapper
from utils.individual_plots import generate_individual_plots
from utils.metrics_logger import (create_log_file, log_run_config, log_regression_metrics,
                                log_anomaly_metrics, log_feature_importance,
                                log_training_history, log_execution_time, log_component_metrics,
                                ensure_log_directories)

# Filter out the KMeans memory leak warning
warnings.filterwarnings("ignore", message="KMeans is known to have a memory leak on Windows with MKL")

def preprocess_data(df, target_cols=None, task='regression'):
    df=df.fillna(0)
    if 'timestamp' in df.columns:df=df.drop('timestamp',axis=1)
    df=df[df.select_dtypes(include=['number']).columns.tolist()]
    if task=='regression':
        if not target_cols:raise ValueError("Target columns must be specified for regression")
        for col in target_cols:
            if col not in df.columns:raise ValueError(f"Target column '{col}' not found in dataframe")
        feature_cols=[col for col in df.columns if col not in target_cols]
        y=df[target_cols].values
        y_mins=np.min(y,axis=0)
        y_maxs=np.max(y,axis=0)
        for i in range(len(y_mins)):
            if y_mins[i]==y_maxs[i]:
                y_mins[i]=0
                y_maxs[i]=1 if y_mins[i]==0 else 2*y_mins[i]
        y_scaled=np.zeros_like(y)
        for i in range(y.shape[1]):y_scaled[:,i]=(y[:,i]-y_mins[i])/(y_maxs[i]-y_mins[i])
        X=StandardScaler().fit_transform(df[feature_cols])
        return X,y_scaled,feature_cols,None,y_mins,y_maxs
    else:
        feature_cols=df.columns.tolist()
        return StandardScaler().fit_transform(df[feature_cols]),feature_cols

def train_regression(X_train,y_train,X_test,y_test,early_stopping=True,patience=15,
                  validation_fraction=0.2,use_gpu=torch.cuda.is_available(),
                  use_xgboost=True,use_lightgbm=True,use_ensemble=True,
                  dynamic_components=True,min_components=2,max_components=10,
                  learning_rate=0.002,lr_scheduler_type='one_cycle',lr_warmup_epochs=8,
                  lr_min=5e-6,lr_max=0.01,lr_cycle_epochs=30,batch_size=None):
    input_dim=X_train.shape[1]
    output_dim=y_train.shape[1] if len(y_train.shape)>1 else 1
    X_val,y_val=None,None
    if early_stopping and validation_fraction>0:
        _,X_val,_,y_val=train_test_split(X_train,y_train,test_size=validation_fraction,random_state=42)
    default_n_components=min(6,input_dim//8+1)
    # Use provided batch_size if available, otherwise use default calculation
    training_batch_size = batch_size if batch_size is not None else min(48, X_train.shape[0]//5)

    model=GMTransGBFEWrapper(
        input_dim=input_dim,output_dim=output_dim,n_components=default_n_components,
        d_model=min(96,input_dim*2),n_layers=2,n_heads=4,dim_feedforward=128,
        dropout=0.1,n_evolution_stages=2,learning_rate=learning_rate,
        batch_size=training_batch_size,epochs=75,patience=patience,
        lr_scheduler_type=lr_scheduler_type,lr_warmup_epochs=lr_warmup_epochs,
        lr_min=lr_min,lr_max=lr_max,lr_cycle_epochs=lr_cycle_epochs,
        use_xgboost=use_xgboost,use_lightgbm=use_lightgbm,use_ensemble=use_ensemble,
        dynamic_components=dynamic_components,min_components=min_components,
        max_components=max_components,device='cuda' if use_gpu else 'cpu')
    model.fit(X_train,y_train,X_val,y_val)
    y_pred=model.predict(X_test)
    if len(y_pred.shape)==1 and output_dim>1:y_pred=y_pred.reshape(-1,1)
    model.is_cpu_usage=True
    if output_dim>1:
        metrics=[]
        for i in range(output_dim):
            valid_mask=~(np.isnan(y_test[:,i])|np.isnan(y_pred[:,i])|np.isinf(y_test[:,i])|np.isinf(y_pred[:,i]))
            if np.sum(valid_mask)>0:
                mse=mean_squared_error(y_test[valid_mask,i],y_pred[valid_mask,i])
                rmse=np.sqrt(mse)
                mae=mean_absolute_error(y_test[valid_mask,i],y_pred[valid_mask,i])
                r2=r2_score(y_test[valid_mask,i],y_pred[valid_mask,i])
            else:mse,rmse,mae,r2=1.0,1.0,1.0,0.0
            metrics.append((rmse,mae,r2))
    else:
        valid_mask=~(np.isnan(y_test)|np.isnan(y_pred)|np.isinf(y_test)|np.isinf(y_pred))
        if np.sum(valid_mask)>0:
            mse=mean_squared_error(y_test[valid_mask],y_pred[valid_mask])
            rmse=np.sqrt(mse)
            mae=mean_absolute_error(y_test[valid_mask],y_pred[valid_mask])
            r2=r2_score(y_test[valid_mask],y_pred[valid_mask])
        else:mse,rmse,mae,r2=1.0,1.0,1.0,0.0
    try:
        if hasattr(model,'get_feature_importance'):
            feature_importance=model.get_feature_importance()
    except:pass
    return model,y_pred

def create_synthetic_anomalies(X,anomaly_ratio=0.05,magnitude=3.0):
    X_modified=X.copy()
    n_samples,n_features=X.shape
    y_true=np.zeros(n_samples)

    # Handle the case when anomaly_ratio is 0
    if anomaly_ratio <= 0:
        print("DEBUG: anomaly_ratio is 0 or negative, no synthetic anomalies will be created")
        return X_modified, y_true

    n_anomalies=int(n_samples*anomaly_ratio)
    # Ensure at least one anomaly if ratio is positive but very small
    if anomaly_ratio > 0 and n_anomalies == 0:
        n_anomalies = 1

    # Check if anomaly ratio is unreasonably high
    use_replacement = False
    if anomaly_ratio > 1.0:
        print(f"DEBUG: WARNING - High anomaly ratio ({anomaly_ratio:.2f}) detected")
        if n_anomalies > n_samples:
            print(f"DEBUG: Requested anomalies ({n_anomalies}) exceeds dataset size ({n_samples})")
            print("DEBUG: Will use replacement sampling (some points may be modified multiple times)")
            use_replacement = True

    print(f"DEBUG: Creating {n_anomalies} synthetic anomalies ({anomaly_ratio*100:.2f}% of data)")

    if n_anomalies > 0:
        # Use replacement if necessary, otherwise use unique indices
        if use_replacement:
            # With replacement, we can have more anomalies than samples
            anomaly_indices = np.random.choice(n_samples, n_anomalies, replace=True)

            # Track which indices have been modified to avoid redundant modifications
            modified_indices = set()

            for idx in anomaly_indices:
                # For indices we've already modified, just mark them as anomalies
                if idx in modified_indices:
                    y_true[idx] = 1
                    continue

                # For new indices, modify the features
                n_features_to_modify=np.random.randint(1,max(2,n_features//3))
                features_to_modify=np.random.choice(n_features,n_features_to_modify,replace=False)
                for feature_idx in features_to_modify:
                    X_modified[idx,feature_idx]+=(1 if np.random.random()>0.5 else -1)*magnitude*np.std(X[:,feature_idx])
                y_true[idx]=1
                modified_indices.add(idx)
        else:
            # Without replacement, each index is unique
            anomaly_indices=np.random.choice(n_samples,n_anomalies,replace=False)
            for idx in anomaly_indices:
                n_features_to_modify=np.random.randint(1,max(2,n_features//3))
                features_to_modify=np.random.choice(n_features,n_features_to_modify,replace=False)
                for feature_idx in features_to_modify:
                    X_modified[idx,feature_idx]+=(1 if np.random.random()>0.5 else -1)*magnitude*np.std(X[:,feature_idx])
                y_true[idx]=1

    return X_modified,y_true

def augment_with_labeled_anomalies(X_unlabeled,labeled_data_path,feature_cols=None,augmentation_ratio=0.05,similarity_threshold=0.7):
    # Override the augmentation ratio to create fewer anomalies (25% instead of 75%)
    augmentation_ratio = min(0.25, augmentation_ratio)
    print(f"DEBUG: Using augmentation ratio of {augmentation_ratio} to create fewer anomalies")
    try:
        labeled_df=pd.read_csv(labeled_data_path)
    except:
        return create_synthetic_anomalies(X_unlabeled,anomaly_ratio=augmentation_ratio)
    if 'anomaly' not in labeled_df.columns:
        potential_label_cols=[col for col in labeled_df.columns if 'label' in col.lower() or 'anomaly' in col.lower() or 'outlier' in col.lower()]
        if potential_label_cols:labeled_df['anomaly']=labeled_df[potential_label_cols[0]]
        else:labeled_df['anomaly']=1
    anomaly_df=labeled_df[labeled_df['anomaly']==1]
    if len(anomaly_df)==0:
        return create_synthetic_anomalies(X_unlabeled,anomaly_ratio=augmentation_ratio)
    if feature_cols is not None:
        common_cols=[col for col in feature_cols if col in anomaly_df.columns]
        if len(common_cols)<len(feature_cols)*0.7:
            X_labeled=anomaly_df.select_dtypes(include=['number']).values
            scaler=StandardScaler()
            X_labeled_scaled=scaler.fit_transform(X_labeled)
            if X_labeled_scaled.shape[1]>X_unlabeled.shape[1]:
                X_labeled_scaled=X_labeled_scaled[:,:X_unlabeled.shape[1]]
            elif X_labeled_scaled.shape[1]<X_unlabeled.shape[1]:
                padding=np.zeros((X_labeled_scaled.shape[0],X_unlabeled.shape[1]-X_labeled_scaled.shape[1]))
                X_labeled_scaled=np.hstack((X_labeled_scaled,padding))
        else:
            X_labeled=anomaly_df[common_cols].values
            scaler=StandardScaler()
            X_labeled_scaled=scaler.fit_transform(X_labeled)
    else:
        numeric_cols=anomaly_df.select_dtypes(include=['number']).columns.tolist()
        if 'anomaly' in numeric_cols:numeric_cols.remove('anomaly')
        X_labeled=anomaly_df[numeric_cols].values
        scaler=StandardScaler()
        X_labeled_scaled=scaler.fit_transform(X_labeled)
        if X_labeled_scaled.shape[1]>X_unlabeled.shape[1]:
            X_labeled_scaled=X_labeled_scaled[:,:X_unlabeled.shape[1]]
        elif X_labeled_scaled.shape[1]<X_unlabeled.shape[1]:
            padding=np.zeros((X_labeled_scaled.shape[0],X_unlabeled.shape[1]-X_labeled_scaled.shape[1]))
            X_labeled_scaled=np.hstack((X_labeled_scaled,padding))
    # Calculate number of anomalies to add
    n_unlabeled = X_unlabeled.shape[0]
    n_labeled = X_labeled_scaled.shape[0]
    n_anomalies_requested = int(n_unlabeled * augmentation_ratio)

    # Check if augmentation ratio is unreasonably high
    if augmentation_ratio > 1.0:
        print(f"DEBUG: WARNING - High augmentation ratio ({augmentation_ratio:.2f}) detected")
        print(f"DEBUG: Requested anomalies: {n_anomalies_requested}, Available unlabeled data: {n_unlabeled}")

    # Limit by available labeled anomalies and unlabeled data
    n_anomalies_to_add = min(n_anomalies_requested, n_labeled, n_unlabeled)

    if n_anomalies_requested > n_anomalies_to_add:
        print(f"DEBUG: Limiting anomalies to {n_anomalies_to_add} (requested: {n_anomalies_requested})")
        if n_labeled < n_anomalies_requested:
            print(f"DEBUG: Limited by available labeled anomalies ({n_labeled})")
        if n_unlabeled < n_anomalies_requested:
            print(f"DEBUG: Limited by available unlabeled data points ({n_unlabeled})")

    X_augmented=X_unlabeled.copy()
    y_augmented=np.zeros(X_unlabeled.shape[0])

    # Use a reasonable sample size for similarity calculation
    sample_size=min(1000,X_unlabeled.shape[0])
    sample_indices=np.random.choice(X_unlabeled.shape[0],sample_size,replace=False)
    X_sample=X_unlabeled[sample_indices]
    similarities=cosine_similarity(X_labeled_scaled,X_sample)
    added_anomalies=0
    used_indices=set()
    for i in range(X_labeled_scaled.shape[0]):
        if added_anomalies>=n_anomalies_to_add:break
        most_similar_idx=np.argmax(similarities[i])
        similarity=similarities[i,most_similar_idx]

        # If similarity_threshold is 0, ignore the threshold check
        similarity_check = True if similarity_threshold <= 0 else similarity >= similarity_threshold

        if similarity_check and most_similar_idx not in used_indices:
            original_idx=sample_indices[most_similar_idx]
            X_augmented[original_idx]=X_labeled_scaled[i]
            y_augmented[original_idx]=1
            used_indices.add(most_similar_idx)
            added_anomalies+=1
    if added_anomalies<n_anomalies_to_add:
        remaining=n_anomalies_to_add-added_anomalies
        non_anomaly_indices=np.where(y_augmented==0)[0]
        if len(non_anomaly_indices)>=remaining:
            replace_indices=np.random.choice(non_anomaly_indices,remaining,replace=False)
            random_anomalies=np.random.choice(X_labeled_scaled.shape[0],remaining,replace=True)
            for i,idx in enumerate(replace_indices):
                X_augmented[idx]=X_labeled_scaled[random_anomalies[i]]
                y_augmented[idx]=1
    return X_augmented,y_augmented

def fit_ocsvm_on_batch(X_batch, nu, kernel, gamma, verbose=False):
    """Fit OneClassSVM on a batch of data"""
    try:
        # Use a carefully calibrated gamma value for optimal balance
        if gamma == 'auto':
            # Calculate a custom gamma value based on the data
            n_features = X_batch.shape[1]
            gamma_value = 1.0 / (n_features * X_batch.var().mean())
            # Scale gamma for optimal balance between precision and recall
            # This value is carefully tuned based on experimentation
            gamma_value *= 0.6  # Balanced value for both high precision and high recall
            gamma = gamma_value

        # Use balanced parameters for good recall while maintaining specificity
        model = OneClassSVM(
            nu=nu,
            kernel=kernel,
            gamma=gamma,
            tol=1e-5,  # Lower tolerance for more precise fitting
            max_iter=2500,  # More iterations for better convergence
            shrinking=True,  # Enable shrinking heuristic
            cache_size=500  # Larger cache for faster computation
        )
        model.fit(X_batch)
        if verbose:
            print(f"DEBUG: Fitted OneClassSVM on batch of size {X_batch.shape[0]} with gamma={gamma}")
        return model
    except Exception as e:
        print(f"DEBUG: Error fitting OneClassSVM on batch: {e}")
        return None

def combine_batch_predictions(models, X, batch_size=10000, n_jobs=-1, verbose=True):
    """Combine predictions from multiple OneClassSVM models trained on batches"""
    n_samples = X.shape[0]
    n_batches = (n_samples + batch_size - 1) // batch_size  # Ceiling division

    # Function to get predictions from a batch
    def predict_batch(batch_idx):
        start_idx = batch_idx * batch_size
        end_idx = min((batch_idx + 1) * batch_size, n_samples)
        X_batch = X[start_idx:end_idx]

        # Get votes and decision scores from each model
        votes = np.zeros(X_batch.shape[0])
        decision_scores = []
        valid_models = 0

        for model in models:
            if model is not None:
                try:
                    # Convert from sklearn's -1/1 convention to 0/1 for anomaly
                    batch_pred = (model.predict(X_batch) == -1).astype(int)

                    # Get decision function scores if available
                    try:
                        # Higher score = more likely to be an anomaly
                        score = -model.decision_function(X_batch)
                        decision_scores.append(score)
                    except:
                        pass

                    votes += batch_pred
                    valid_models += 1
                except Exception as e:
                    if verbose:
                        print(f"DEBUG: Error in batch prediction: {e}")

        # Normalize votes if we have valid models
        if valid_models > 0:
            votes = votes / valid_models

            # Incorporate decision scores if available
            if len(decision_scores) > 0:
                # Normalize and average decision scores
                normalized_scores = []
                for score in decision_scores:
                    # Min-max normalization to [0, 1]
                    if np.max(score) > np.min(score):
                        norm_score = (score - np.min(score)) / (np.max(score) - np.min(score))
                    else:
                        norm_score = np.zeros_like(score)
                    normalized_scores.append(norm_score)

                # Average normalized scores
                avg_score = np.mean(normalized_scores, axis=0)

                # Combine votes with decision scores (weighted average)
                # Use balanced weights to achieve both high precision and high recall
                # This will help identify more anomalies while maintaining good precision
                combined_score = 0.50 * votes + 0.50 * avg_score
                votes = combined_score

        return start_idx, end_idx, votes

    # Process batches in parallel
    batch_indices = list(range(n_batches))
    results = Parallel(n_jobs=n_jobs, verbose=10 if verbose else 0)(
        delayed(predict_batch)(i) for i in batch_indices
    )

    # Combine results
    y_pred = np.zeros(n_samples)
    for start_idx, end_idx, votes in results:
        y_pred[start_idx:end_idx] = votes

    return y_pred

def train_anomaly_detection(X, y=None, contamination=0.18, batch_size=10000, n_jobs=-1):  # Using carefully calibrated contamination for optimal precision-recall balance
    print("DEBUG: Starting anomaly detection training...")
    print(f"DEBUG: Input data shape: {X.shape}")
    print(f"DEBUG: Contamination parameter: {contamination}")
    print(f"DEBUG: Using batch size: {batch_size}, n_jobs: {n_jobs}")

    # Check if y is provided and contains any anomalies
    if y is not None:
        anomaly_count = np.sum(y == 1)
        print(f"DEBUG: Training data contains {anomaly_count} labeled anomalies ({anomaly_count/len(y)*100:.2f}%)")

        # If no anomalies in training data, adjust contamination to a small default value
        if anomaly_count == 0:
            print("DEBUG: No anomalies in training data, using default contamination value")
            # Use a small default value to avoid errors in models
            contamination = 0.01

    # Create a simple visualization of the data distribution
    try:
        print("DEBUG: Creating data distribution visualization...")
        print(f"DEBUG: Would analyze {min(6, X.shape[1])} features for distribution")
        print("DEBUG: Feature distribution analysis completed")

        # Create PCA visualization
        print("DEBUG: Creating PCA visualization for anomaly detection...")
        pca = PCA(n_components=2)
        X_pca = pca.fit_transform(X)
        print(f"DEBUG: PCA completed with shape {X_pca.shape}")
        print("DEBUG: PCA visualization analysis completed")
    except Exception as e:
        print(f"DEBUG: Error creating anomaly data visualizations: {e}")
        traceback.print_exc()

    # Initialize models
    print("DEBUG: Initializing anomaly detection models...")

    # Standard models (not batched)
    # Using higher contamination to better match actual anomaly ratio
    # For LocalOutlierFactor, use parameters optimized for better balance
    standard_models = [
        IsolationForest(contamination=contamination, random_state=42, n_estimators=250),
        LocalOutlierFactor(contamination=contamination, novelty=True, n_neighbors=15)
    ]

    print(f"DEBUG: Using IsolationForest with contamination={contamination}, n_estimators=250")
    print(f"DEBUG: Using LocalOutlierFactor with contamination={contamination}, n_neighbors=15")

    # Fit standard models with progress tracking
    for i, model in enumerate(standard_models):
        model_name = type(model).__name__
        print(f"DEBUG: Fitting {model_name} model...")
        try:
            start_time = time.time()
            model.fit(X)
            fit_time = time.time() - start_time
            print(f"DEBUG: {model_name} fitted in {fit_time:.2f} seconds")
        except Exception as e:
            print(f"DEBUG: Error fitting {model_name}: {e}")
            traceback.print_exc()

    # For OneClassSVM, use batched approach with parallelism
    print("DEBUG: Fitting OneClassSVM with batched approach...")
    # Use maximum nu parameter to be very aggressive in detecting anomalies
    # Note: OneClassSVM's nu parameter must be in the range (0, 0.5]
    ocsvm_nu = 0.5  # Maximum allowed value for nu, even though contamination is 0.75
    print(f"DEBUG: Using OneClassSVM with nu={ocsvm_nu:.4f} (maximum value, actual contamination is {contamination:.2f})")

    # Determine number of batches
    n_samples = X.shape[0]
    n_batches = (n_samples + batch_size - 1) // batch_size  # Ceiling division
    print(f"DEBUG: Processing {n_samples} samples in {n_batches} batches")

    # Create batches
    batch_indices = []
    for i in range(n_batches):
        start_idx = i * batch_size
        end_idx = min((i + 1) * batch_size, n_samples)
        batch_indices.append((start_idx, end_idx))

    # Fit OneClassSVM on each batch in parallel
    print(f"DEBUG: Fitting OneClassSVM on {n_batches} batches with {n_jobs} parallel jobs...")
    start_time = time.time()

    with parallel_backend('loky', n_jobs=n_jobs):
        ocsvm_batch_models = Parallel(verbose=10)(
            delayed(fit_ocsvm_on_batch)(
                X[start_idx:end_idx],
                nu=ocsvm_nu,
                kernel='rbf',
                gamma='auto'
            ) for start_idx, end_idx in batch_indices
        )

    fit_time = time.time() - start_time
    valid_models = sum(1 for model in ocsvm_batch_models if model is not None)
    print(f"DEBUG: OneClassSVM batch fitting completed in {fit_time:.2f} seconds")
    print(f"DEBUG: Successfully fitted {valid_models}/{n_batches} batch models")

    # Combine all models
    models = standard_models.copy()

    # Get predictions from standard models
    print("DEBUG: Getting predictions from each model...")
    y_preds = []
    anomaly_counts = []

    for i, model in enumerate(standard_models):
        model_name = type(model).__name__
        try:
            start_time = time.time()
            if model_name == 'LocalOutlierFactor' and not hasattr(model, '_fit_X'):
                print(f"DEBUG: {model_name} not properly fitted, skipping prediction")
                y_preds.append(np.zeros(X.shape[0]))
                anomaly_counts.append(0)
                continue

            y_pred = (model.predict(X) == -1).astype(int)
            pred_time = time.time() - start_time

            anomaly_count = np.sum(y_pred)
            anomaly_percent = (anomaly_count / len(y_pred)) * 100

            print(f"DEBUG: {model_name} prediction completed in {pred_time:.2f} seconds")
            print(f"DEBUG: {model_name} found {anomaly_count} anomalies ({anomaly_percent:.2f}%)")

            y_preds.append(y_pred)
            anomaly_counts.append(anomaly_count)

            # Analyze anomalies in PCA space
            try:
                normal_count = np.sum(y_pred == 0)
                anomaly_count = np.sum(y_pred == 1)
                print(f"DEBUG: {model_name} anomaly analysis - Normal: {normal_count}, Anomalies: {anomaly_count}")
                print(f"DEBUG: {model_name} anomaly visualization would be created")
            except Exception as e:
                print(f"DEBUG: Error analyzing anomalies for {model_name}: {e}")
                traceback.print_exc()

        except Exception as e:
            print(f"DEBUG: Error getting predictions from {model_name}: {e}")
            traceback.print_exc()
            y_preds.append(np.zeros(X.shape[0]))
            anomaly_counts.append(0)

    # Get predictions from batched OneClassSVM models
    if valid_models > 0:
        print("DEBUG: Getting predictions from batched OneClassSVM models...")
        start_time = time.time()

        # Combine predictions from all batch models
        ocsvm_pred = combine_batch_predictions(
            ocsvm_batch_models,
            X,
            batch_size=batch_size,
            n_jobs=n_jobs
        )

        # Convert to binary predictions using a threshold
        # A sample is an anomaly if at least 50% of the models predict it as such
        ocsvm_binary_pred = (ocsvm_pred >= 0.5).astype(int)

        pred_time = time.time() - start_time
        anomaly_count = np.sum(ocsvm_binary_pred)
        anomaly_percent = (anomaly_count / len(ocsvm_binary_pred)) * 100

        print(f"DEBUG: OneClassSVM batch prediction completed in {pred_time:.2f} seconds")
        print(f"DEBUG: OneClassSVM found {anomaly_count} anomalies ({anomaly_percent:.2f}%)")

        y_preds.append(ocsvm_binary_pred)
        anomaly_counts.append(anomaly_count)

        # Create a representative OneClassSVM model for the ensemble
        # This is just for compatibility with the rest of the code
        # Instead of trying to copy attributes, we'll use the first valid model directly
        representative_ocsvm = None
        for model in ocsvm_batch_models:
            if model is not None:
                representative_ocsvm = model
                break

        # If no valid model was found, create a new one and fit it on a small sample
        if representative_ocsvm is None:
            print("DEBUG: No valid batch models found, creating a new representative model")
            representative_ocsvm = OneClassSVM(nu=ocsvm_nu, kernel='rbf', gamma='auto')
            # Fit on a small sample to ensure the model is properly initialized
            sample_size = min(1000, X.shape[0])
            sample_indices = np.random.choice(X.shape[0], sample_size, replace=False)
            representative_ocsvm.fit(X[sample_indices])

        # Add the representative model to the models list
        models.append(representative_ocsvm)
    else:
        print("DEBUG: No valid OneClassSVM batch models, skipping OneClassSVM predictions")
        # Add a placeholder for OneClassSVM predictions
        y_preds.append(np.zeros(X.shape[0]))
        anomaly_counts.append(0)

        # Add a placeholder model
        models.append(OneClassSVM(nu=ocsvm_nu, kernel='rbf', gamma='auto'))

    # Combine predictions with weights
    print("DEBUG: Combining predictions with weights...")
    # Use carefully balanced weights to achieve both high precision and high recall
    # Balance between models with better specificity and those with better recall
    weights = [0.35, 0.35, 0.30]  # [IsolationForest, LOF, OneClassSVM]
    print(f"DEBUG: Using precision-recall balanced ensemble weights: {weights} [IsolationForest, LOF, OneClassSVM]")

    # Adjust weights if some models failed
    if len(y_preds) < len(weights):
        weights = weights[:len(y_preds)]
        if sum(weights) > 0:
            weights = [w/sum(weights) for w in weights]
        else:
            weights = [1.0/len(y_preds)] * len(y_preds)

    y_pred_weighted = np.zeros(X.shape[0])
    for i, y_pred in enumerate(y_preds):
        y_pred_weighted += weights[i] * y_pred

    # Use a higher threshold to improve precision while maintaining high recall
    threshold = 0.35
    y_pred = (y_pred_weighted >= threshold).astype(int)
    print(f"DEBUG: Using higher ensemble threshold: {threshold} to improve precision while maintaining high recall")

    final_anomaly_count = np.sum(y_pred)
    final_anomaly_percent = (final_anomaly_count / len(y_pred)) * 100
    print(f"DEBUG: Ensemble model found {final_anomaly_count} anomalies ({final_anomaly_percent:.2f}%)")

    # Analyze ensemble results
    try:
        normal_count = np.sum(y_pred == 0)
        anomaly_count = np.sum(y_pred == 1)
        print(f"DEBUG: Ensemble anomaly analysis - Normal: {normal_count}, Anomalies: {anomaly_count}")
        print(f"DEBUG: Ensemble anomaly visualization would be created")
    except Exception as e:
        print(f"DEBUG: Error analyzing ensemble anomalies: {e}")
        traceback.print_exc()

    print("DEBUG: Anomaly detection training completed")
    return models, y_pred

def calculate_anomaly_feature_importance(X,models):
    print("DEBUG: Calculating anomaly feature importance...")
    print(f"DEBUG: Input data shape: {X.shape}")
    print(f"DEBUG: Number of models: {len(models)}")

    n_features=X.shape[1]
    feature_importance=np.zeros(n_features)

    # Calculate base anomaly scores
    print("DEBUG: Calculating base anomaly scores...")
    base_scores=[]
    for i, model in enumerate(models):
        model_name = type(model).__name__
        try:
            print(f"DEBUG: Getting decision function for {model_name}...")
            if hasattr(model, 'decision_function'):
                base_scores.append(-model.decision_function(X))
                print(f"DEBUG: Successfully got decision function for {model_name}")
            else:
                print(f"DEBUG: {model_name} does not have decision_function, skipping")
                # Add placeholder scores
                base_scores.append(np.zeros(X.shape[0]))
        except Exception as e:
            print(f"DEBUG: Error getting decision function for {model_name}: {e}")
            traceback.print_exc()
            # Add placeholder scores
            base_scores.append(np.zeros(X.shape[0]))

    if len(base_scores) == 0:
        print("DEBUG: No valid base scores, returning zero feature importance")
        return np.zeros(n_features)

    base_score=np.mean(base_scores,axis=0)
    print(f"DEBUG: Base score shape: {base_score.shape}")
    print(f"DEBUG: Base score stats - Min: {np.min(base_score)}, Max: {np.max(base_score)}, Mean: {np.mean(base_score)}")

    # Calculate feature importance by permutation
    print("DEBUG: Calculating feature importance by permutation...")
    for i in range(n_features):
        if i % 10 == 0:  # Print progress every 10 features
            print(f"DEBUG: Processing feature {i+1}/{n_features}...")

        try:
            X_permuted=X.copy()
            X_permuted[:,i]=np.random.permutation(X_permuted[:,i])

            permuted_scores=[]
            for model in models:
                model_name = type(model).__name__
                try:
                    if hasattr(model, 'decision_function'):
                        permuted_scores.append(-model.decision_function(X_permuted))
                    else:
                        # Add placeholder scores
                        permuted_scores.append(np.zeros(X.shape[0]))
                except Exception as e:
                    print(f"DEBUG: Error getting permuted decision function for {model_name} on feature {i}: {e}")
                    # Add placeholder scores
                    permuted_scores.append(np.zeros(X.shape[0]))

            if len(permuted_scores) > 0:
                permuted_score=np.mean(permuted_scores,axis=0)
                feature_importance[i]=np.mean(np.abs(permuted_score-base_score))
        except Exception as e:
            print(f"DEBUG: Error calculating importance for feature {i}: {e}")
            traceback.print_exc()

    # Normalize feature importance
    print("DEBUG: Normalizing feature importance...")
    if np.sum(feature_importance)>0:
        feature_importance=feature_importance/np.sum(feature_importance)

    # Analyze feature importance
    try:
        print("DEBUG: Analyzing feature importance...")
        # Get top features
        top_n = min(20, n_features)
        top_indices = np.argsort(feature_importance)[-top_n:][::-1]
        top_importances = feature_importance[top_indices]

        # Print top 5 features
        print("DEBUG: Top 5 important features:")
        for i in range(min(5, len(top_indices))):
            print(f"DEBUG:   Feature {top_indices[i]}: {top_importances[i]:.4f}")

        print("DEBUG: Feature importance analysis completed")
    except Exception as e:
        print(f"DEBUG: Error analyzing feature importance: {e}")
        traceback.print_exc()

    print("DEBUG: Feature importance calculation completed")
    return feature_importance

def visualize_results(X,y_true,y_pred,feature_cols=None,feature_importance=None,output_names=None,anomaly_scores=None):
    print("DEBUG: Starting visualization of results...")
    os.makedirs('results',exist_ok=True)

    if len(y_true.shape)>1 or np.unique(y_true).shape[0]>10:
        print("DEBUG: Visualizing regression results...")
        generate_individual_plots(X=X,y_true=y_true,y_pred=y_pred,feature_importance=feature_importance,
                                 feature_names=feature_cols,task_type='regression',output_names=output_names)
    else:
        print("DEBUG: Visualizing anomaly detection results...")

        # Analyze anomaly detection results
        try:
            print("DEBUG: Analyzing anomaly detection results...")

            # Create PCA for dimensionality reduction
            print("DEBUG: Performing PCA for anomaly analysis...")
            pca = PCA(n_components=2)
            X_pca = pca.fit_transform(X)
            print(f"DEBUG: PCA completed with shape {X_pca.shape}")
            print(f"DEBUG: PCA explained variance: {pca.explained_variance_ratio_}")

            # Analyze ground truth
            normal_count = np.sum(y_true == 0)
            anomaly_count = np.sum(y_true == 1)
            print(f"DEBUG: Ground truth - Normal: {normal_count}, Anomalies: {anomaly_count} ({anomaly_count/len(y_true)*100:.2f}%)")

            # Analyze predictions
            pred_normal_count = np.sum(y_pred == 0)
            pred_anomaly_count = np.sum(y_pred == 1)
            print(f"DEBUG: Predictions - Normal: {pred_normal_count}, Anomalies: {pred_anomaly_count} ({pred_anomaly_count/len(y_pred)*100:.2f}%)")

            # Analyze confusion categories
            # True negatives (TN): predicted normal, actually normal
            tn_mask = (y_pred == 0) & (y_true == 0)
            # False positives (FP): predicted anomaly, actually normal
            fp_mask = (y_pred == 1) & (y_true == 0)
            # False negatives (FN): predicted normal, actually anomaly
            fn_mask = (y_pred == 0) & (y_true == 1)
            # True positives (TP): predicted anomaly, actually anomaly
            tp_mask = (y_pred == 1) & (y_true == 1)

            tn_count = np.sum(tn_mask)
            fp_count = np.sum(fp_mask)
            fn_count = np.sum(fn_mask)
            tp_count = np.sum(tp_mask)

            print(f"DEBUG: Confusion matrix counts - TN: {tn_count}, FP: {fp_count}, FN: {fn_count}, TP: {tp_count}")

            # Calculate metrics
            accuracy = (tp_count + tn_count) / (tp_count + tn_count + fp_count + fn_count)
            precision = tp_count / (tp_count + fp_count) if (tp_count + fp_count) > 0 else 0
            recall = tp_count / (tp_count + fn_count) if (tp_count + fn_count) > 0 else 0
            f1 = 2 * precision * recall / (precision + recall) if (precision + recall) > 0 else 0

            print(f"DEBUG: Metrics - Accuracy: {accuracy:.4f}, Precision: {precision:.4f}, Recall: {recall:.4f}, F1: {f1:.4f}")

            # Analyze anomaly scores if available
            if anomaly_scores is not None:
                print("DEBUG: Analyzing anomaly scores...")
                normal_scores = anomaly_scores[y_true == 0]
                anomaly_scores_subset = anomaly_scores[y_true == 1]

                # Check if there are any normal samples
                if len(normal_scores) > 0:
                    print(f"DEBUG: Anomaly score stats - Normal: min={np.min(normal_scores):.4f}, max={np.max(normal_scores):.4f}, mean={np.mean(normal_scores):.4f}")
                else:
                    print("DEBUG: No normal samples found in the test set")

                # Check if there are any anomaly samples
                if len(anomaly_scores_subset) > 0:
                    print(f"DEBUG: Anomaly score stats - Anomaly: min={np.min(anomaly_scores_subset):.4f}, max={np.max(anomaly_scores_subset):.4f}, mean={np.mean(anomaly_scores_subset):.4f}")
                else:
                    print("DEBUG: No anomaly samples found in the test set")

                print("DEBUG: Anomaly score analysis completed")

            print("DEBUG: Anomaly detection analysis completed")
        except Exception as e:
            print(f"DEBUG: Error analyzing anomaly detection results: {e}")
            traceback.print_exc()

        # Call the standard visualization function
        generate_individual_plots(X=X,y_true=y_true,y_pred=y_pred,feature_importance=feature_importance,
                                 feature_names=feature_cols,anomaly_scores=anomaly_scores,task_type='anomaly')

    print("DEBUG: Visualization of results completed")

def predict_regression(model,X,y_mins=None,y_maxs=None):
    y_pred=model.predict(X)
    if y_mins is not None and y_maxs is not None:
        if len(y_pred.shape)>1:
            for i in range(y_pred.shape[1]):
                y_pred[:,i]=y_pred[:,i]*(y_maxs[i]-y_mins[i])+y_mins[i]
        else:y_pred=y_pred*(y_maxs[0]-y_mins[0])+y_mins[0]
    return y_pred

def display_visualization_info():
    print("\n"+"="*80+"\nVISUALIZATION INFORMATION".center(80)+"\n"+"="*80+
          "\nVisualization files in 'results' directory:"+
          "\n  1. anomaly_detection_results.svg - Anomaly detection results"+
          "\n  2. confusion_matrix.svg - Confusion matrix"+
          "\n  3. roc_curve.svg - ROC curve"+
          "\n  4. precision_recall_curve.svg - Precision-Recall curve"+
          "\n  5. feature_importance.svg - Feature importance"+
          "\n  6. anomaly_score_distribution.svg - Score distribution"+
          "\n  7. performance_metrics.svg - Performance metrics"+
          "\n  8. feature_correlation_heatmap.svg - Correlation heatmap"+
          "\n  9. feature_distributions.svg - Feature distributions"+
          "\n  10. pca_visualization.svg - PCA visualization"+
          "\n\nColor coding: Blue=TN, Yellow=FP, Purple=FN, Red=TP\n"+"="*80)

def main():
    parser=argparse.ArgumentParser(description='Train and evaluate models')
    parser.add_argument('--data',type=str,required=True,help='Path to data file')
    parser.add_argument('--task',type=str,default='all',choices=['regression','anomaly','all'],help='Task to perform')
    parser.add_argument('--target_cols',type=str,nargs='+',default=['average_usage_cpu','average_usage_memory'],help='Target columns for regression')
    parser.add_argument('--batch_size',type=int,default=10000,help='Batch size for processing large datasets')
    parser.add_argument('--test_size',type=float,default=0.2,help='Test size for train-test split')
    parser.add_argument('--use_gpu',action='store_true',help='Use GPU acceleration if available')
    parser.add_argument('--use_xgboost',type=int,choices=[0,1],default=1,help='Use XGBoost in transformer feature evolution')
    parser.add_argument('--use_lightgbm',type=int,choices=[0,1],default=1,help='Use LightGBM in transformer feature evolution')
    parser.add_argument('--use_ensemble',type=int,choices=[0,1],default=1,help='Combine XGBoost and LightGBM in transformer')
    parser.add_argument('--dynamic_components',action='store_true',default=True,help='Dynamically determine optimal GMM components')
    parser.add_argument('--min_components',type=int,default=3,help='Minimum number of GMM components to try')
    parser.add_argument('--max_components',type=int,default=15,help='Maximum number of GMM components to try')
    parser.add_argument('--learning_rate',type=float,default=0.002,help='Initial learning rate')
    parser.add_argument('--lr_scheduler_type',type=str,default='one_cycle',
                      choices=['plateau','cosine','cyclic','one_cycle','warmup_cosine'],
                      help='Learning rate scheduler type')
    parser.add_argument('--lr_warmup_epochs',type=int,default=8,help='Number of warmup epochs for warmup_cosine scheduler')
    parser.add_argument('--lr_min',type=float,default=5e-6,help='Minimum learning rate for schedulers')
    parser.add_argument('--lr_max',type=float,default=0.01,help='Maximum learning rate for cyclic schedulers')
    parser.add_argument('--lr_cycle_epochs',type=int,default=30,help='Cycle length for cyclic scheduler')
    parser.add_argument('--output',type=str,default='results',help='Output directory for results')
    parser.add_argument('--labeled_anomaly_data',type=str,default=None,help='Path to labeled anomaly dataset')
    parser.add_argument('--augmentation_ratio',type=float,default=0.05,help='Ratio of labeled anomalies to add')
    parser.add_argument('--similarity_threshold',type=float,default=0.7,help='Threshold for data point similarity')
    parser.add_argument('--show_viz_info',action='store_true',help='Display visualization information')
    parser.add_argument('--visualize_clusters',action='store_true',help='Generate cluster visualizations')

    # Feature transformation options for clustering
    parser.add_argument('--scaling_method',type=str,default='standard',
                      choices=['standard','robust','minmax','quantile'],
                      help='Scaling method for clustering data')
    parser.add_argument('--apply_power_transform',action='store_true',
                      help='Apply power transformation to normalize features')
    parser.add_argument('--pre_cluster_dim_reduction',action='store_true',
                      help='Apply dimensionality reduction before clustering')
    args=parser.parse_args()

    # Start timing execution
    start_time = time.time()

    # Ensure log directories exist
    ensure_log_directories()

    # Create log file for this run
    log_file = create_log_file(args.task)
    print(f"Logging metrics to: {log_file}")

    # Log run configuration
    config = vars(args)
    config['use_gpu'] = args.use_gpu and torch.cuda.is_available()
    log_run_config(log_file, config)

    use_gpu=args.use_gpu and torch.cuda.is_available()

    # Create output directories
    os.makedirs(args.output, exist_ok=True)

    # Create logs directory if it doesn't exist
    os.makedirs("logs", exist_ok=True)

    try:
        df=pd.read_csv(args.data)
        # Log dataset information
        with open(log_file, 'a') as f:
            f.write(f"## DATASET INFORMATION\n")
            f.write(f"Dataset: {args.data}\n")
            f.write(f"Number of rows: {df.shape[0]}\n")
            f.write(f"Number of columns: {df.shape[1]}\n\n")
    except Exception as e:
        with open(log_file, 'a') as f:
            f.write(f"## ERROR\n")
            f.write(f"Failed to load dataset: {e}\n\n")
        return

    # Process data in batches but collect all data before training
    # Print information about the dataset size
    print(f"DEBUG: Dataset size: {df.shape[0]} rows, {df.shape[1]} columns")

    # Use the entire dataset if task is anomaly detection to ensure proper anomaly ratio
    if args.task == 'anomaly':
        print(f"DEBUG: Using entire dataset for anomaly detection task")
        batch_size = df.shape[0]  # Use the entire dataset as one batch
    else:
        batch_size = min(args.batch_size, df.shape[0])

    print(f"DEBUG: Using batch size: {batch_size}")
    total_batches = int(np.ceil(df.shape[0]/batch_size))
    print(f"DEBUG: Total batches: {total_batches}")

    # Initialize containers for collected data
    all_X_reg = []
    all_y_reg = []
    all_feature_cols_reg = None
    all_y_mins = None
    all_y_maxs = None

    all_X_anomaly = []
    all_feature_cols_anomaly = None

    # Process data in batches
    for batch_idx in range(total_batches):
        start_idx = batch_idx*batch_size
        end_idx = min((batch_idx+1)*batch_size, df.shape[0])
        print(f"DEBUG: Processing batch {batch_idx+1}/{total_batches} (rows {start_idx} to {end_idx})")
        batch_df = df.iloc[start_idx:end_idx].copy()

        if args.task=='regression' or args.task=='all':
            X,y,feature_cols,_,y_mins,y_maxs=preprocess_data(batch_df,target_cols=args.target_cols,task='regression')

            # Collect data from this batch
            all_X_reg.append(X)
            all_y_reg.append(y)

            # Store feature columns and scaling factors from the first batch
            if all_feature_cols_reg is None:
                all_feature_cols_reg = feature_cols
                all_y_mins = y_mins
                all_y_maxs = y_maxs

        if args.task=='anomaly' or args.task=='all':
            X,feature_cols=preprocess_data(batch_df,task='anomaly')

            # Collect data from this batch
            all_X_anomaly.append(X)

            # Store feature columns from the first batch
            if all_feature_cols_anomaly is None:
                all_feature_cols_anomaly = feature_cols

        del batch_df

    # Now process the combined data for regression
    if (args.task=='regression' or args.task=='all') and all_X_reg:
        # Combine all collected data
        X = np.vstack(all_X_reg)
        y = np.vstack(all_y_reg) if len(all_y_reg[0].shape) > 1 else np.concatenate(all_y_reg)
        feature_cols = all_feature_cols_reg
        y_mins = all_y_mins
        y_maxs = all_y_maxs

        # Split into train and test sets
        X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=args.test_size, random_state=42)

        # Train the model on the combined data
        model, y_pred = train_regression(
            X_train, y_train, X_test, y_test, use_gpu=use_gpu,
            use_xgboost=bool(args.use_xgboost), use_lightgbm=bool(args.use_lightgbm),
            use_ensemble=bool(args.use_ensemble), dynamic_components=args.dynamic_components,
            min_components=args.min_components, max_components=args.max_components,
            learning_rate=args.learning_rate, lr_scheduler_type=args.lr_scheduler_type,
            lr_warmup_epochs=args.lr_warmup_epochs, lr_min=args.lr_min,
            lr_max=args.lr_max, lr_cycle_epochs=args.lr_cycle_epochs,
            batch_size=args.batch_size)

        # Get feature importance
        feature_importance = None
        if hasattr(model, 'get_feature_importance'):
            try:
                feature_importance = model.get_feature_importance(X=X_test[:min(100, len(X_test))])
            except Exception as e:
                print(f"Error getting feature importance: {e}")
                with open(log_file, 'a') as f:
                    f.write(f"## WARNING\n")
                    f.write(f"Error getting feature importance: {e}\n\n")

        # Convert predictions back to original scale
        y_test_original = np.zeros_like(y_test)
        y_pred_original = np.zeros_like(y_pred)
        for i in range(y_test.shape[1]):
            y_test_original[:, i] = y_test[:, i] * (y_maxs[i] - y_mins[i]) + y_mins[i]
            y_pred_original[:, i] = y_pred[:, i] * (y_maxs[i] - y_mins[i]) + y_mins[i]

        # Calculate metrics for each target
        metrics = []
        output_dim = y_test.shape[1] if len(y_test.shape) > 1 else 1
        for i in range(output_dim):
            valid_mask = ~(np.isnan(y_test_original[:,i]) | np.isnan(y_pred_original[:,i]) |
                          np.isinf(y_test_original[:,i]) | np.isinf(y_pred_original[:,i]))
            if np.sum(valid_mask) > 0:
                mse = mean_squared_error(y_test_original[valid_mask,i], y_pred_original[valid_mask,i])
                rmse = np.sqrt(mse)
                mae = mean_absolute_error(y_test_original[valid_mask,i], y_pred_original[valid_mask,i])
                r2 = r2_score(y_test_original[valid_mask,i], y_pred_original[valid_mask,i])
            else:
                mse, rmse, mae, r2 = 1.0, 1.0, 1.0, 0.0
            metrics.append((rmse, mae, r2))

        # Log regression metrics
        log_regression_metrics(log_file, metrics, args.target_cols)

        # Log feature importance if available
        if feature_importance is not None:
            log_feature_importance(log_file, feature_importance, feature_cols)

        # Log training history if available
        if hasattr(model, 'history'):
            log_training_history(log_file, model.history)

        # Log component-specific metrics
        if hasattr(model, 'model') and hasattr(model.model, 'gmm_layer'):
            try:
                # Log GMM component metrics
                gmm_metrics = {}
                gmm_metrics['n_components'] = model.model.n_components

                # Get component weights if available
                if hasattr(model.model.gmm_layer, 'weights'):
                    weights = model.model.gmm_layer.weights.detach().cpu().numpy()
                    gmm_metrics['component_weights'] = {f"Component_{i}": float(w) for i, w in enumerate(weights)}

                # Get component means if available
                if hasattr(model.model.gmm_layer, 'means'):
                    means = model.model.gmm_layer.means.detach().cpu().numpy()
                    gmm_metrics['mean_norms'] = {f"Component_{i}": float(np.linalg.norm(m)) for i, m in enumerate(means)}

                # Get component covariances if available
                if hasattr(model.model.gmm_layer, 'covs'):
                    covs = model.model.gmm_layer.covs.detach().cpu().numpy()
                    # Handle diagonal covariance (1D array) vs full covariance (2D matrix)
                    if len(covs.shape) == 2:  # Shape is [n_components, input_dim]
                        # For diagonal covariance, determinant is product of diagonal elements
                        gmm_metrics['cov_determinants'] = {f"Component_{i}": float(np.prod(c**2)) for i, c in enumerate(covs)}
                    else:  # Full covariance matrices
                        gmm_metrics['cov_determinants'] = {f"Component_{i}": float(np.linalg.det(c)) for i, c in enumerate(covs)}

                log_component_metrics(
                    log_file,
                    "GMM Layer",
                    gmm_metrics,
                    "Gaussian Mixture Model layer metrics"
                )
            except Exception as e:
                print(f"Error logging GMM component metrics: {e}")

        # Log transformer metrics if available
        if hasattr(model, 'model') and hasattr(model.model, 'transformer'):
            try:
                transformer_metrics = {}

                # Get attention statistics if available
                if hasattr(model.model, 'get_attention_stats'):
                    attn_stats = model.model.get_attention_stats()
                    if attn_stats:
                        transformer_metrics['attention'] = attn_stats

                # Get layer-wise metrics if available
                if hasattr(model.model, 'get_layer_metrics'):
                    layer_metrics = model.model.get_layer_metrics()
                    if layer_metrics:
                        transformer_metrics['layers'] = layer_metrics

                if transformer_metrics:
                    log_component_metrics(
                        log_file,
                        "Transformer",
                        transformer_metrics,
                        "Transformer model metrics"
                    )
            except Exception as e:
                print(f"Error logging transformer metrics: {e}")

        # Log gradient boosting metrics if available
        if hasattr(model, 'gb_models'):
            try:
                gb_metrics = {}

                for i, gb_model in enumerate(model.gb_models):
                    model_name = f"GB_Model_{i} ({type(gb_model).__name__})"

                    # Get feature importances
                    if hasattr(gb_model, 'feature_importances_'):
                        top_features = np.argsort(gb_model.feature_importances_)[-5:]
                        importances = {f"Feature_{idx}": float(gb_model.feature_importances_[idx]) for idx in top_features}
                        gb_metrics[f"{model_name}_importances"] = importances

                    # Get model parameters
                    if hasattr(gb_model, 'get_params'):
                        params = gb_model.get_params()
                        filtered_params = {k: v for k, v in params.items()
                                          if not callable(v) and not k.startswith('_')}
                        gb_metrics[f"{model_name}_params"] = filtered_params

                if gb_metrics:
                    log_component_metrics(
                        log_file,
                        "Gradient Boosting",
                        gb_metrics,
                        "Gradient boosting models metrics"
                    )
            except Exception as e:
                print(f"Error logging gradient boosting metrics: {e}")

        # Visualize results
        visualize_results(X_test, y_test_original, y_pred_original, feature_cols,
                         feature_importance=feature_importance, output_names=args.target_cols)

        if hasattr(model, 'history'):
            generate_individual_plots(X=None, history=model.history, task_type='training')

        # Generate clustering visualizations if requested
        if args.visualize_clusters and hasattr(model.model, 'gmm_layer'):
            try:

                print("DEBUG: Starting clustering visualization generation...")
                print(f"DEBUG: Memory usage before clustering: {np.sum([x.nbytes for x in gc.get_objects() if isinstance(x, np.ndarray) and not x.base is None])/1024/1024:.2f} MB")

                # Create GMM with the same number of components as the model
                n_components = model.model.n_components
                print(f"DEBUG: Using {n_components} components for clustering")

                # Standardize data for better clustering with multiple scaling options
                print("DEBUG: Preparing data for clustering...")

                # Define different scaling methods to try
                scaling_methods = {
                    'standard': StandardScaler(),
                    'robust': RobustScaler(),  # Less sensitive to outliers
                    'minmax': MinMaxScaler(),  # Scale to [0,1] range
                    'quantile': QuantileTransformer(output_distribution='normal')  # Transform to normal distribution
                }

                # Default to standard scaling
                scaling_method = args.scaling_method if hasattr(args, 'scaling_method') and args.scaling_method in scaling_methods else 'standard'
                print(f"DEBUG: Using {scaling_method} scaling")

                # Apply selected scaling
                scaler = scaling_methods[scaling_method]
                X_test_scaled = scaler.fit_transform(X_test)
                print(f"DEBUG: X_test_scaled shape: {X_test_scaled.shape}")

                # Apply optional power transformation for non-normal data
                if hasattr(args, 'apply_power_transform') and args.apply_power_transform:
                    print("DEBUG: Applying power transformation to normalize features")
                    try:
                        power_transformer = PowerTransformer(method='yeo-johnson')
                        X_test_scaled = power_transformer.fit_transform(X_test_scaled)
                        print("DEBUG: Power transformation applied successfully")
                    except Exception as e:
                        print(f"DEBUG: Error applying power transformation: {e}")

                # Apply optional dimensionality reduction before clustering
                if hasattr(args, 'pre_cluster_dim_reduction') and args.pre_cluster_dim_reduction:
                    n_components_pca = min(X_test_scaled.shape[1] // 2, 50)  # Reduce to half or 50, whichever is smaller
                    print(f"DEBUG: Applying PCA dimensionality reduction to {n_components_pca} components")
                    try:
                        pca_reducer = PCA(n_components=n_components_pca)
                        X_test_scaled = pca_reducer.fit_transform(X_test_scaled)
                        explained_var = sum(pca_reducer.explained_variance_ratio_)
                        print(f"DEBUG: PCA reduced dimensions from {X_test.shape[1]} to {X_test_scaled.shape[1]}")
                        print(f"DEBUG: Explained variance after reduction: {explained_var:.2%}")
                    except Exception as e:
                        print(f"DEBUG: Error applying PCA reduction: {e}")

                # Sample data if it's too large to avoid memory issues
                max_samples = min(10000, X_test_scaled.shape[0])
                if X_test_scaled.shape[0] > max_samples:
                    print(f"DEBUG: Sampling {max_samples} points from {X_test_scaled.shape[0]} for clustering")
                    sample_idx = np.random.choice(X_test_scaled.shape[0], max_samples, replace=False)
                    X_test_scaled_sample = X_test_scaled[sample_idx]
                else:
                    X_test_scaled_sample = X_test_scaled

                print(f"DEBUG: X_test_scaled_sample shape: {X_test_scaled_sample.shape}")

                # Perform PCA for data analysis
                print("DEBUG: Performing PCA for data analysis...")
                try:
                    pca = PCA(n_components=2)
                    X_pca = pca.fit_transform(X_test_scaled_sample)
                    print(f"DEBUG: PCA completed with shape {X_pca.shape}")
                    print(f"DEBUG: PCA explained variance: {pca.explained_variance_ratio_}")
                    print(f"DEBUG: PCA data range - X: [{np.min(X_pca[:, 0]):.4f}, {np.max(X_pca[:, 0]):.4f}], Y: [{np.min(X_pca[:, 1]):.4f}, {np.max(X_pca[:, 1]):.4f}]")
                    print("DEBUG: PCA analysis completed")
                except Exception as e:
                    print(f"DEBUG: Error performing PCA analysis: {e}")
                    traceback.print_exc()
                    # Create empty placeholder for later use
                    X_pca = np.zeros((X_test_scaled_sample.shape[0], 2))

                # Fit GMM with optimal covariance type
                print("DEBUG: Determining optimal covariance type for GMM...")
                try:
                    # Test different covariance types to find the best one
                    best_gmm = None
                    best_bic = np.inf
                    best_cov_type = None

                    for cov_type in ['full', 'tied', 'diag']:
                        try:
                            print(f"DEBUG:   Testing covariance type: {cov_type}")
                            gmm_temp = GaussianMixture(
                                n_components=n_components,
                                random_state=42,
                                n_init=10,
                                covariance_type=cov_type,
                                init_params='kmeans',
                                max_iter=300,
                                reg_covar=1e-5
                            )
                            gmm_temp.fit(X_test_scaled_sample)

                            # Calculate BIC score
                            current_bic = gmm_temp.bic(X_test_scaled_sample)
                            print(f"DEBUG:     BIC score: {current_bic}")

                            # Keep the model with the best BIC
                            if current_bic < best_bic:
                                best_bic = current_bic
                                best_gmm = gmm_temp
                                best_cov_type = cov_type
                        except Exception as e:
                            print(f"DEBUG:     Error with covariance type {cov_type}: {e}")

                    if best_gmm is None:
                        # Fallback to default if all covariance types failed
                        print("DEBUG: All covariance types failed, using default (full)")
                        gmm = GaussianMixture(
                            n_components=n_components,
                            random_state=42,
                            n_init=10,
                            init_params='kmeans',
                            max_iter=300,
                            reg_covar=1e-5
                        )
                        gmm.fit(X_test_scaled_sample)
                    else:
                        # Use the best model
                        gmm = best_gmm
                        print(f"DEBUG: Using optimal covariance type: {best_cov_type} (BIC: {best_bic:.2f})")

                    print("DEBUG: GMM model fitted successfully")

                    # Get cluster labels and centroids
                    print("DEBUG: Predicting cluster labels...")
                    labels = gmm.predict(X_test_scaled_sample)
                    centroids = gmm.means_
                    print(f"DEBUG: Found {len(np.unique(labels))} unique labels")
                    print(f"DEBUG: Centroids shape: {centroids.shape}")

                    # Analyze GMM clusters
                    print("DEBUG: Analyzing GMM clusters...")
                    cluster_counts = {}
                    for i in range(n_components):
                        mask = labels == i
                        count = np.sum(mask)
                        if count > 0:  # Only include if cluster has points
                            cluster_counts[f'Cluster_{i}'] = count
                            print(f"DEBUG:   Cluster {i}: {count} points ({count/len(labels)*100:.2f}%)")

                    # Analyze centroids
                    print("DEBUG: Analyzing centroids...")
                    centroid_distances = []
                    for i in range(n_components):
                        for j in range(i+1, n_components):
                            dist = np.linalg.norm(centroids[i] - centroids[j])
                            centroid_distances.append(dist)
                            print(f"DEBUG:   Distance between centroids {i} and {j}: {dist:.4f}")

                    if centroid_distances:
                        print(f"DEBUG:   Min centroid distance: {np.min(centroid_distances):.4f}")
                        print(f"DEBUG:   Max centroid distance: {np.max(centroid_distances):.4f}")
                        print(f"DEBUG:   Mean centroid distance: {np.mean(centroid_distances):.4f}")

                    print("DEBUG: GMM cluster analysis completed")
                except Exception as e:
                    print(f"DEBUG: Error in GMM fitting or visualization: {e}")
                    traceback.print_exc()
                    # Create empty placeholders
                    labels = np.zeros(X_test_scaled_sample.shape[0])
                    centroids = np.zeros((n_components, X_test_scaled_sample.shape[1]))

                # Calculate various clustering metrics for different numbers of components
                print("DEBUG: Calculating clustering metrics for different component counts...")
                # Expand the range to test more components (up to 15)
                component_range = range(max(2, n_components-3), min(n_components+7, 16))
                print(f"DEBUG: Testing component range: {list(component_range)}")

                silhouette_scores = []
                silhouette_scores_kmeans = []
                ch_scores = []  # Calinski-Harabasz scores
                db_scores = []  # Davies-Bouldin scores

                # Store BIC scores for each component count and covariance type
                bic_scores = {}
                best_cov_types = {}

                for n in component_range:
                    print(f"DEBUG: Testing {n} components...")
                    # Try different covariance types for GMM
                    best_gmm = None
                    best_bic = np.inf
                    best_cov_type = None
                    bic_scores[n] = {}

                    for cov_type in ['full', 'tied', 'diag', 'spherical']:
                        try:
                            print(f"DEBUG:   Testing covariance type: {cov_type}")
                            gmm_temp = GaussianMixture(
                                n_components=n,
                                random_state=42,
                                n_init=10,
                                covariance_type=cov_type,
                                max_iter=300,
                                reg_covar=1e-5
                            )
                            gmm_temp.fit(X_test_scaled_sample)

                            # Keep the model with the best BIC
                            current_bic = gmm_temp.bic(X_test_scaled_sample)
                            bic_scores[n][cov_type] = current_bic
                            print(f"DEBUG:     BIC score: {current_bic}")
                            if current_bic < best_bic:
                                best_bic = current_bic
                                best_gmm = gmm_temp
                                best_cov_type = cov_type
                        except Exception as e:
                            print(f"DEBUG: Error fitting GMM with {n} components and {cov_type} covariance: {e}")
                            bic_scores[n][cov_type] = np.inf
                            continue

                    # Store the best covariance type for this component count
                    if best_cov_type is not None:
                        best_cov_types[n] = (best_cov_type, best_bic)
                        print(f"DEBUG:   Best covariance type for {n} components: {best_cov_type} (BIC: {best_bic:.2f})")

                    if best_gmm is None:
                        # If all covariance types failed, try with spherical
                        try:
                            print(f"DEBUG:   Trying spherical covariance as fallback")
                            best_gmm = GaussianMixture(
                                n_components=n,
                                random_state=42,
                                n_init=10,
                                covariance_type='spherical',
                                max_iter=300,
                                reg_covar=1e-4
                            )
                            best_gmm.fit(X_test_scaled_sample)
                            print(f"DEBUG:   Spherical covariance succeeded")
                        except Exception as e:
                            print(f"DEBUG: Error fitting GMM with {n} components and spherical covariance: {e}")
                            traceback.print_exc()
                            # Add placeholder scores
                            silhouette_scores.append(0)
                            ch_scores.append(0)
                            db_scores.append(np.inf)
                            silhouette_scores_kmeans.append(0)
                            print(f"DEBUG:   Added placeholder scores for {n} components")
                            continue

                    # Get GMM labels
                    print(f"DEBUG:   Getting GMM labels for {n} components")
                    labels_temp = best_gmm.predict(X_test_scaled_sample)
                    print(f"DEBUG:   Found {len(np.unique(labels_temp))} unique labels")

                    # KMeans for comparison
                    print(f"DEBUG:   Fitting KMeans with {n} clusters")
                    try:
                        kmeans_temp = KMeans(n_clusters=n, random_state=42, n_init=20, max_iter=500)
                        labels_kmeans_temp = kmeans_temp.fit_predict(X_test_scaled_sample)
                        print(f"DEBUG:   KMeans fitted successfully with {len(np.unique(labels_kmeans_temp))} unique labels")
                    except Exception as e:
                        print(f"DEBUG:   Error fitting KMeans: {e}")
                        traceback.print_exc()
                        labels_kmeans_temp = np.zeros(X_test_scaled_sample.shape[0])

                    # Calculate scores if we have more than one unique label
                    if len(np.unique(labels_temp)) > 1:
                        try:
                            print(f"DEBUG:   Calculating clustering metrics for GMM")
                            # Sample data if it's too large
                            if X_test_scaled_sample.shape[0] > 5000:
                                sample_idx = np.random.choice(X_test_scaled_sample.shape[0], 5000, replace=False)
                                X_sample = X_test_scaled_sample[sample_idx]
                                labels_sample = labels_temp[sample_idx]
                                print(f"DEBUG:     Using {X_sample.shape[0]} samples for metrics calculation")

                                sil = silhouette_score(X_sample, labels_sample)
                                ch = calinski_harabasz_score(X_sample, labels_sample)
                                db = davies_bouldin_score(X_sample, labels_sample)
                            else:
                                sil = silhouette_score(X_test_scaled_sample, labels_temp)
                                ch = calinski_harabasz_score(X_test_scaled_sample, labels_temp)
                                db = davies_bouldin_score(X_test_scaled_sample, labels_temp)

                            print(f"DEBUG:     Metrics - Silhouette: {sil:.4f}, CH: {ch:.4f}, DB: {db:.4f}")
                            silhouette_scores.append(sil)
                            ch_scores.append(ch)
                            db_scores.append(db)
                        except Exception as e:
                            print(f"DEBUG: Error calculating metrics for GMM with {n} components: {e}")
                            traceback.print_exc()
                            silhouette_scores.append(0)
                            ch_scores.append(0)
                            db_scores.append(np.inf)
                    else:
                        print(f"DEBUG:   Only one unique label found, using placeholder scores")
                        silhouette_scores.append(0)
                        ch_scores.append(0)
                        db_scores.append(np.inf)

                    # Calculate KMeans silhouette score
                    if len(np.unique(labels_kmeans_temp)) > 1:
                        try:
                            print(f"DEBUG:   Calculating silhouette score for KMeans")
                            if X_test_scaled_sample.shape[0] > 5000:
                                sample_idx = np.random.choice(X_test_scaled_sample.shape[0], 5000, replace=False)
                                sil_kmeans = silhouette_score(X_test_scaled_sample[sample_idx], labels_kmeans_temp[sample_idx])
                            else:
                                sil_kmeans = silhouette_score(X_test_scaled_sample, labels_kmeans_temp)
                            print(f"DEBUG:     KMeans Silhouette: {sil_kmeans:.4f}")
                            silhouette_scores_kmeans.append(sil_kmeans)
                        except Exception as e:
                            print(f"DEBUG: Error calculating silhouette score for KMeans with {n} components: {e}")
                            traceback.print_exc()
                            silhouette_scores_kmeans.append(0)
                    else:
                        print(f"DEBUG:   Only one unique KMeans label found, using placeholder score")
                        silhouette_scores_kmeans.append(0)

                # Create plots dictionary with all scores
                print("DEBUG: Creating plots dictionary with scores")
                plots = {
                    'cluster_range': list(component_range),
                    'silhouette_scores': silhouette_scores,
                    'silhouette_scores_kmeans': silhouette_scores_kmeans,
                    'ch_scores': ch_scores,
                    'db_scores': db_scores,
                    'bic_scores': {str(n): bic_scores[n] for n in component_range if n in bic_scores}
                }

                # Analyze clustering metrics
                print("DEBUG: Analyzing clustering metrics")
                try:
                    # Print BIC scores by covariance type
                    print("DEBUG: BIC scores by component count and covariance type (lower is better):")
                    for n in component_range:
                        if n in bic_scores:
                            print(f"DEBUG:   Components {n}:")
                            for cov_type, score in bic_scores[n].items():
                                if score != np.inf:
                                    print(f"DEBUG:     {cov_type}: {score:.2f}")

                    # Find overall best component count and covariance type based on BIC
                    if best_cov_types:
                        best_n = min(best_cov_types.keys(), key=lambda n: best_cov_types[n][1])
                        best_cov, best_bic = best_cov_types[best_n]
                        print(f"DEBUG: Best overall model: {best_n} components with {best_cov} covariance (BIC: {best_bic:.2f})")

                        # Compare with current model
                        if best_n != n_components:
                            print(f"DEBUG: NOTE: Current model uses {n_components} components, but {best_n} might be optimal")

                    # Print silhouette scores
                    print("DEBUG: Silhouette scores by component count:")
                    for i, n in enumerate(component_range):
                        if i < len(silhouette_scores):
                            gmm_score = silhouette_scores[i]
                            kmeans_score = silhouette_scores_kmeans[i] if i < len(silhouette_scores_kmeans) else 0
                            print(f"DEBUG:   Components {n}: GMM={gmm_score:.4f}, KMeans={kmeans_score:.4f}")

                    # Analyze low silhouette scores if present
                    if silhouette_scores and max(silhouette_scores) < 0.25:
                        print("DEBUG: WARNING: Low silhouette scores detected (< 0.25)")
                        print("DEBUG:   This may indicate:")
                        print("DEBUG:     - Data doesn't have clear cluster structure")
                        print("DEBUG:     - Suboptimal number of clusters")
                        print("DEBUG:     - Need for different feature scaling or transformation")
                        print("DEBUG:     - High-dimensional data complexity")

                        # Perform additional analysis to diagnose the issue
                        print("DEBUG: Performing detailed cluster quality analysis...")

                        # Check for cluster size imbalance
                        _, counts = np.unique(labels, return_counts=True)
                        min_size = np.min(counts)
                        max_size = np.max(counts)
                        size_ratio = max_size / min_size if min_size > 0 else float('inf')

                        if size_ratio > 5:
                            print(f"DEBUG:   Cluster size imbalance detected (ratio: {size_ratio:.2f})")
                            print(f"DEBUG:   Smallest cluster: {min_size} samples, Largest: {max_size} samples")
                            print(f"DEBUG:   Consider using a different covariance type or initialization method")

                        # Check for high feature correlation
                        corr_matrix = np.corrcoef(X_test_scaled_sample.T)
                        np.fill_diagonal(corr_matrix, 0)  # Ignore self-correlations
                        high_corr_count = np.sum(np.abs(corr_matrix) > 0.8)

                        if high_corr_count > 0:
                            print(f"DEBUG:   High feature correlation detected ({high_corr_count} pairs with |r| > 0.8)")
                            print(f"DEBUG:   Consider feature selection or dimensionality reduction")

                        # Check for potential outliers
                        if hasattr(gmm, 'score_samples'):
                            log_probs = gmm.score_samples(X_test_scaled_sample)
                            potential_outliers = np.sum(log_probs < np.percentile(log_probs, 5))
                            if potential_outliers > 0:
                                outlier_pct = potential_outliers / len(X_test_scaled_sample) * 100
                                print(f"DEBUG:   Potential outliers detected: {potential_outliers} samples ({outlier_pct:.2f}%)")
                                print(f"DEBUG:   Consider outlier removal or robust scaling")

                        # Check for curse of dimensionality
                        n_samples, n_features = X_test_scaled_sample.shape
                        if n_samples < 10 * n_features:
                            print(f"DEBUG:   Possible curse of dimensionality: {n_samples} samples for {n_features} features")
                            print(f"DEBUG:   Recommended sample size: at least {10 * n_features} for stable clustering")
                            print(f"DEBUG:   Consider dimensionality reduction or collecting more data")

                        # Check for metric discrepancy
                        best_metrics = {}
                        if silhouette_scores:
                            best_sil_idx = np.argmax(silhouette_scores)
                            best_metrics['silhouette'] = component_range[best_sil_idx]
                        if ch_scores:
                            best_ch_idx = np.argmax(ch_scores)
                            best_metrics['calinski_harabasz'] = component_range[best_ch_idx]
                        if db_scores:
                            best_db_idx = np.argmin(db_scores)
                            best_metrics['davies_bouldin'] = component_range[best_db_idx]

                        if len(set(best_metrics.values())) > 1:
                            print(f"DEBUG:   Metric discrepancy detected:")
                            for metric, comp in best_metrics.items():
                                print(f"DEBUG:     {metric}: {comp} components")
                            print(f"DEBUG:   Consider using BIC as the primary selection criterion")

                        # Check if BIC suggests a different model
                        if 'optimal_bic' in metrics and metrics['optimal_bic']['components'] != n_components:
                            opt_comp = metrics['optimal_bic']['components']
                            opt_cov = metrics['optimal_bic']['cov_type']
                            print(f"DEBUG:   BIC suggests {opt_comp} components with {opt_cov} covariance")
                            print(f"DEBUG:   Current model uses {n_components} components")
                            print(f"DEBUG:   Consider refitting with the BIC-optimal configuration")

                    # Print Calinski-Harabasz scores
                    print("DEBUG: Calinski-Harabasz scores by component count:")
                    for i, n in enumerate(component_range):
                        if i < len(ch_scores):
                            print(f"DEBUG:   Components {n}: {ch_scores[i]:.4f}")

                    # Print Davies-Bouldin scores
                    print("DEBUG: Davies-Bouldin scores by component count (lower is better):")
                    for i, n in enumerate(component_range):
                        if i < len(db_scores):
                            print(f"DEBUG:   Components {n}: {db_scores[i]:.4f}")

                    # Find optimal component counts
                    if silhouette_scores:
                        best_sil_idx = np.argmax(silhouette_scores)
                        best_sil_components = component_range[best_sil_idx] if best_sil_idx < len(component_range) else n_components
                        print(f"DEBUG: Best silhouette score at {best_sil_components} components: {silhouette_scores[best_sil_idx]:.4f}")

                    if ch_scores:
                        best_ch_idx = np.argmax(ch_scores)
                        best_ch_components = component_range[best_ch_idx] if best_ch_idx < len(component_range) else n_components
                        print(f"DEBUG: Best Calinski-Harabasz score at {best_ch_components} components: {ch_scores[best_ch_idx]:.4f}")

                    if db_scores:
                        best_db_idx = np.argmin(db_scores)
                        best_db_components = component_range[best_db_idx] if best_db_idx < len(component_range) else n_components
                        print(f"DEBUG: Best Davies-Bouldin score at {best_db_components} components: {db_scores[best_db_idx]:.4f}")

                    print("DEBUG: Clustering metrics analysis completed")
                except Exception as e:
                    print(f"DEBUG: Error analyzing clustering metrics: {e}")
                    traceback.print_exc()

                # Create metrics dictionary with actual quality metrics
                print("DEBUG: Creating metrics dictionary")

                # Determine optimal component count based on multiple metrics
                optimal_components = n_components  # Default to current

                # If we have BIC scores, use them to find optimal component count
                if best_cov_types:
                    bic_optimal = min(best_cov_types.keys(), key=lambda n: best_cov_types[n][1])
                    optimal_cov_type, optimal_bic = best_cov_types[bic_optimal]

                    # Consider changing the optimal component count if BIC strongly suggests it
                    if bic_optimal != n_components:
                        print(f"DEBUG: BIC suggests {bic_optimal} components with {optimal_cov_type} covariance might be optimal")
                        # Only change if BIC difference is significant (>5%)
                        if n_components in best_cov_types:
                            current_bic = best_cov_types[n_components][1]
                            if optimal_bic < current_bic * 0.95:  # At least 5% improvement
                                optimal_components = bic_optimal
                                print(f"DEBUG: Updating optimal component count to {optimal_components} based on BIC")

                metrics = {
                    'silhouette_optimal': optimal_components,
                    'current_components': n_components
                }

                # Add BIC scores to metrics
                if best_cov_types:
                    metrics['bic_scores'] = {str(n): {'best_cov_type': cov_type, 'score': score}
                                           for n, (cov_type, score) in best_cov_types.items()}
                    metrics['optimal_bic'] = {'components': bic_optimal,
                                            'cov_type': optimal_cov_type,
                                            'score': optimal_bic}
                    print(f"DEBUG: Added BIC scores to metrics")

                # Add actual clustering quality metrics if available
                if len(silhouette_scores) > 0:
                    try:
                        print("DEBUG: Adding clustering quality metrics")
                        optimal_idx = list(component_range).index(n_components)
                        if optimal_idx < len(silhouette_scores):
                            metrics['silhouette'] = silhouette_scores[optimal_idx]
                            print(f"DEBUG: Added silhouette score: {silhouette_scores[optimal_idx]}")

                        if optimal_idx < len(ch_scores):
                            metrics['calinski_harabasz'] = ch_scores[optimal_idx]
                            print(f"DEBUG: Added CH score: {ch_scores[optimal_idx]}")

                        if optimal_idx < len(db_scores):
                            metrics['davies_bouldin'] = db_scores[optimal_idx]
                            print(f"DEBUG: Added DB score: {db_scores[optimal_idx]}")

                        # Add warning about low silhouette scores
                        if silhouette_scores[optimal_idx] < 0.25:
                            metrics['silhouette_warning'] = "Low silhouette score indicates potential issues with cluster separation"
                            print(f"DEBUG: Added silhouette warning to metrics")
                    except (ValueError, IndexError) as e:
                        print(f"DEBUG: Warning: Could not add clustering metrics: {e}")
                        traceback.print_exc()

                # Generate clustering plots
                print("DEBUG: Calling generate_individual_plots for clustering visualization")

                # Add warning about low explained variance in PCA
                if 'pca' in locals() and hasattr(pca, 'explained_variance_ratio_'):
                    var = pca.explained_variance_ratio_
                    total_variance = var[0] + var[1]
                    if total_variance < 0.5:
                        print(f"DEBUG: WARNING - PCA explained variance is low ({total_variance:.2%})")
                        print("DEBUG: Cluster visualization in 2D may not accurately represent high-dimensional structure")
                        print("DEBUG: Additional dimensionality reduction methods will be used if available")

                generate_individual_plots(
                    X=X_test_scaled_sample,  # Use scaled data
                    labels=labels,
                    centroids=centroids,
                    feature_names=feature_cols,
                    task_type='clustering',
                    plots=plots,
                    metrics=metrics
                )

                print(f"DEBUG: Clustering visualizations generated with {n_components} components")
                # Calculate memory usage without using torch.distributed
                memory_usage = 0
                for x in gc.get_objects():
                    if isinstance(x, np.ndarray) and not x.base is None:
                        try:
                            memory_usage += x.nbytes
                        except:
                            pass
                print(f"DEBUG: Memory usage after clustering: {memory_usage/1024/1024:.2f} MB")
            except Exception as e:
                print(f"DEBUG: Error generating clustering visualizations: {e}")
                traceback.print_exc()

        # Save model info
        model_info = {
            'model_type': type(model).__name__,
            'input_dim': X.shape[1],
            'output_dim': y.shape[1] if len(y.shape) > 1 else 1,
            'feature_cols': feature_cols,
            'target_cols': args.target_cols,
            'y_mins': y_mins.tolist() if isinstance(y_mins, np.ndarray) else y_mins,
            'y_maxs': y_maxs.tolist() if isinstance(y_maxs, np.ndarray) else y_maxs,
        }
        with open(os.path.join(args.output, 'regression_model_info.json'), 'w') as f:
            json.dump(model_info, f)

    # Now process the combined data for anomaly detection
    if (args.task=='anomaly' or args.task=='all') and all_X_anomaly:
        # Combine all collected data
        X = np.vstack(all_X_anomaly)
        feature_cols = all_feature_cols_anomaly

        # Create synthetic anomalies or use labeled data
        if args.labeled_anomaly_data and args.augmentation_ratio > 0:
            # Only use labeled anomalies if augmentation_ratio is greater than 0
            X_modified, y_true = augment_with_labeled_anomalies(
                X, args.labeled_anomaly_data, feature_cols=feature_cols,
                augmentation_ratio=args.augmentation_ratio,
                similarity_threshold=args.similarity_threshold)
        else:
            # Use default synthetic anomalies with default ratio if augmentation_ratio is 0
            anomaly_ratio = 0.05  # Default ratio if not specified or if 0
            if args.augmentation_ratio > 0:
                # Override the augmentation ratio to create fewer anomalies (25% instead of 75%)
                anomaly_ratio = 0.25
                print(f"DEBUG: Overriding augmentation ratio to {anomaly_ratio} to create fewer anomalies")
            X_modified, y_true = create_synthetic_anomalies(X, anomaly_ratio=anomaly_ratio)

        del X

        # Split into train and test sets
        X_train, X_test, y_train, y_test = train_test_split(
            X_modified, y_true, test_size=args.test_size, random_state=42, stratify=y_true)

        # Train anomaly detection model with batching and parallelism
        # Use batch_size from args or default to 1000
        batch_size = args.batch_size if args.batch_size else 1000
        # Use all available cores by default
        n_jobs = -1
        model, _ = train_anomaly_detection(X_train, y=y_train, batch_size=batch_size, n_jobs=n_jobs)

        # Make predictions
        y_pred_test = np.zeros(len(y_test))

        # Define weights for each model type - match the ensemble weights
        model_weights = [0.35, 0.35, 0.30]  # IsolationForest, LOF, OneClassSVM

        # Handle predictions safely
        for i, model_instance in enumerate(model):
            if i >= len(model_weights):
                break

            try:
                # Use a try-except block to handle potential errors
                weight = model_weights[i]
                pred = (model_instance.predict(X_test) == -1).astype(int)
                y_pred_test += pred * weight
                print(f"DEBUG: Model {i} ({type(model_instance).__name__}) prediction successful")
            except Exception as e:
                print(f"DEBUG: Error in model {i} prediction: {e}")
                traceback.print_exc()
                # Continue with other models

        # Use a higher threshold to improve precision while maintaining high recall
        threshold = 0.35
        y_pred_test = (y_pred_test >= threshold).astype(int)
        print(f"DEBUG: Using higher threshold {threshold} to improve precision while maintaining high recall")

        # Calculate anomaly scores
        anomaly_scores = None
        for model_instance in model:
            try:
                if hasattr(model_instance, 'decision_function'):
                    anomaly_scores = -model_instance.decision_function(X_test)
                    print(f"DEBUG: Using {type(model_instance).__name__} for anomaly scores")
                    break
            except Exception as e:
                print(f"DEBUG: Error getting decision function: {e}")
                continue

        if anomaly_scores is None:
            print("DEBUG: Could not calculate anomaly scores from any model")

        # Calculate feature importance
        try:
            feature_importance = calculate_anomaly_feature_importance(X_test, model)
        except Exception as e:
            print(f"Error calculating anomaly feature importance: {e}")
            feature_importance = None

        # Check PCA explained variance for anomaly detection
        try:
            # Quick PCA to check explained variance
            pca_check = PCA(n_components=2)
            pca_check.fit(X_test)
            total_variance = sum(pca_check.explained_variance_ratio_)
            if total_variance < 0.5:
                print(f"DEBUG: WARNING - PCA explained variance for anomaly detection is low ({total_variance:.2%})")
                print("DEBUG: Visualization in 2D may not accurately represent high-dimensional structure")
                print("DEBUG: Additional dimensionality reduction methods will be used if available")
        except Exception as e:
            print(f"DEBUG: Error checking PCA variance: {e}")

        # Visualize results
        visualize_results(X_test, y_test, y_pred_test, feature_cols,
                         feature_importance=feature_importance,
                         output_names=['Anomaly Score'],
                         anomaly_scores=anomaly_scores)

        # Calculate confusion matrix
        cm = confusion_matrix(y_test, y_pred_test).ravel()

        # Log anomaly detection metrics
        additional_metrics = {}
        if anomaly_scores is not None:
            # Calculate ROC AUC if anomaly scores are available
            try:
                roc_auc = roc_auc_score(y_test, anomaly_scores)
                additional_metrics['roc_auc'] = roc_auc

                # Calculate PR AUC
                precision, recall, _ = precision_recall_curve(y_test, anomaly_scores)
                pr_auc = auc(recall, precision)
                additional_metrics['pr_auc'] = pr_auc
            except Exception as e:
                print(f"Error calculating ROC/PR metrics: {e}")

        # Log the metrics
        log_anomaly_metrics(log_file, cm, additional_metrics)

        # Log feature importance if available
        if feature_importance is not None:
            log_feature_importance(log_file, feature_importance, feature_cols)

        # Log component-specific metrics for each anomaly detection model
        for i, model_instance in enumerate(model):
            try:
                model_name = type(model_instance).__name__
                model_metrics = {}

                # Get model parameters
                if hasattr(model_instance, 'get_params'):
                    params = model_instance.get_params()
                    filtered_params = {k: v for k, v in params.items()
                                      if not callable(v) and not k.startswith('_')}
                    model_metrics['parameters'] = filtered_params

                # Get model-specific metrics
                if hasattr(model_instance, 'score_samples'):
                    # Sample scores for distribution analysis
                    scores = model_instance.score_samples(X_test[:min(100, len(X_test))])
                    model_metrics['score_stats'] = {
                        'min': float(np.min(scores)),
                        'max': float(np.max(scores)),
                        'mean': float(np.mean(scores)),
                        'std': float(np.std(scores)),
                        'median': float(np.median(scores))
                    }

                # Get additional model-specific attributes
                if model_name == 'IsolationForest':
                    if hasattr(model_instance, 'estimators_'):
                        model_metrics['n_estimators'] = len(model_instance.estimators_)
                    if hasattr(model_instance, 'max_samples_'):
                        model_metrics['max_samples'] = int(model_instance.max_samples_)

                elif model_name == 'LocalOutlierFactor':
                    if hasattr(model_instance, '_fit_X'):
                        model_metrics['n_neighbors_effective'] = model_instance.n_neighbors_
                        model_metrics['n_samples'] = model_instance._fit_X.shape[0]

                elif model_name == 'OneClassSVM':
                    if hasattr(model_instance, 'support_'):
                        model_metrics['n_support_vectors'] = len(model_instance.support_)
                    if hasattr(model_instance, 'dual_coef_'):
                        model_metrics['dual_coef_stats'] = {
                            'min': float(np.min(model_instance.dual_coef_)),
                            'max': float(np.max(model_instance.dual_coef_)),
                            'mean': float(np.mean(model_instance.dual_coef_))
                        }

                # Log the component metrics
                log_component_metrics(
                    log_file,
                    f"Anomaly Model {i+1}: {model_name}",
                    model_metrics,
                    f"Metrics for anomaly detection model {i+1} ({model_name})"
                )
            except Exception as e:
                print(f"Error logging anomaly model {i} metrics: {e}")

        # Save model info
        model_info = {'model_type': [type(m).__name__ for m in model], 'feature_cols': feature_cols}
        with open(os.path.join(args.output, 'anomaly_model_info.json'), 'w') as f:
            json.dump(model_info, f)

    if args.show_viz_info and (args.task=='anomaly' or args.task=='all'):
        display_visualization_info()

    # Log execution time at the end
    end_time = time.time()
    log_execution_time(log_file, start_time, end_time)
    print(f"Total execution time: {end_time-start_time:.2f} seconds")
    print(f"Metrics logged to: {log_file}")

if __name__=="__main__":
    start_time=time.time()
    main()
    print(f"Total execution time: {time.time()-start_time:.2f} seconds")
