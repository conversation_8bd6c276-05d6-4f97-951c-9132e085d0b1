# Metrics Log - ANOMALY
# Run ID: 20250508_205549
# Timestamp: 2025-05-08 20:55:49
================================================================================

## RUN CONFIGURATION
data: data\processed\borg_traces_cleaned.csv
task: anomaly
target_cols: ['average_usage_cpu', 'average_usage_memory']
batch_size: 1000
test_size: 0.2
use_gpu: False
use_xgboost: 1
use_lightgbm: 1
use_ensemble: 1
dynamic_components: True
min_components: 3
max_components: 15
learning_rate: 0.002
lr_scheduler_type: one_cycle
lr_warmup_epochs: 8
lr_min: 5e-06
lr_max: 0.01
lr_cycle_epochs: 30
output: results
labeled_anomaly_data: None
augmentation_ratio: 0.05
similarity_threshold: 0.7
show_viz_info: False
visualize_clusters: False
scaling_method: standard
apply_power_transform: False
pre_cluster_dim_reduction: False
max_anomaly_samples: 5000
anomaly_timeout: 900

================================================================================

## DATASET INFORMATION
Dataset: data\processed\borg_traces_cleaned.csv
Number of rows: 2950
Number of columns: 113

## ANOMALY DETECTION METRICS
### Confusion Matrix
True Negatives (TN): 394
False Positives (FP): 167
False Negatives (FN): 0
True Positives (TP): 29

### Performance Metrics
Accuracy:    0.7169
Precision:   0.1480
Recall:      1.0000
F1 Score:    0.2578
Specificity: 0.7023

### Additional Metrics
roc_auc: 0.8883
pr_auc: 0.3429

================================================================================

## FEATURE IMPORTANCE
Rank   Feature                        Importance
--------------------------------------------------
1      <USER>              <GROUP>.016371
2      end_time_hour_tan_outlier      0.016202
3      resource_request_cpu_outlier   0.015457
4      start_time_second_tan          0.015095
5      time_hour_tan                  0.014781
6      instance_index_outlier         0.014740
7      start_time_second_sin          0.014679
8      page_cache_memory_outlier      0.014363
9      end_time_second_tan            0.014170
10     end_time_second_sin            0.013650

================================================================================

## COMPONENT METRICS: ANOMALY MODEL 1: ISOLATIONFOREST
Metrics for anomaly detection model 1 (IsolationForest)

### parameters
Metric                    Value          
----------------------------------------
bootstrap                 False
contamination             0.300000       
max_features              1.000000       
max_samples               auto
n_estimators              200
n_jobs                    None
random_state              42
verbose                   0
warm_start                False

### score_stats
Metric                    Value          
----------------------------------------
min                       -0.551695      
max                       -0.379577      
mean                      -0.437484      
std                       0.039267       
median                    -0.427672      

### n_estimators

### max_samples


================================================================================

## COMPONENT METRICS: ANOMALY MODEL 2: LOCALOUTLIERFACTOR
Metrics for anomaly detection model 2 (LocalOutlierFactor)

### parameters
Metric                    Value          
----------------------------------------
algorithm                 auto
contamination             0.300000       
leaf_size                 30
metric                    minkowski
metric_params             None
n_jobs                    None
n_neighbors               20
novelty                   True
p                         2

### score_stats
Metric                    Value          
----------------------------------------
min                       -2.471981      
max                       -0.982236      
mean                      -1.137141      
std                       0.277923       
median                    -1.044340      

### n_neighbors_effective

### n_samples


================================================================================

## COMPONENT METRICS: ANOMALY MODEL 3: ONECLASSSVM
Metrics for anomaly detection model 3 (OneClassSVM)

### parameters
Metric                    Value          
----------------------------------------
cache_size                200
coef0                     0.000000       
degree                    3
gamma                     scale
kernel                    rbf
max_iter                  -1
nu                        0.360000       
shrinking                 True
tol                       0.001000       
verbose                   False

### score_stats
Metric                    Value          
----------------------------------------
min                       7.026494       
max                       142.878566     
mean                      98.849171      
std                       25.951774      
median                    104.575008     

### n_support_vectors

### dual_coef_stats
Metric                    Value          
----------------------------------------
min                       0.003531       
max                       1.000000       
mean                      0.986760       


================================================================================

## EXECUTION TIME
Start time: 2025-05-08 20:55:49
End time: 2025-05-08 20:56:27
Total execution time: 37.51 seconds (0.63 minutes)

================================================================================

