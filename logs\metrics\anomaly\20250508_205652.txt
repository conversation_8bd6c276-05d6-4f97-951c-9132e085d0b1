# Metrics Log - ANOMALY
# Run ID: 20250508_205652
# Timestamp: 2025-05-08 20:56:52
================================================================================

## RUN CONFIGURATION
data: data\processed\borg_traces_cleaned.csv
task: anomaly
target_cols: ['average_usage_cpu', 'average_usage_memory']
batch_size: 10000
test_size: 0.2
use_gpu: False
use_xgboost: 1
use_lightgbm: 1
use_ensemble: 1
dynamic_components: True
min_components: 3
max_components: 15
learning_rate: 0.002
lr_scheduler_type: one_cycle
lr_warmup_epochs: 8
lr_min: 5e-06
lr_max: 0.01
lr_cycle_epochs: 30
output: results
labeled_anomaly_data: None
augmentation_ratio: 0.05
similarity_threshold: 0.7
show_viz_info: False
visualize_clusters: False
scaling_method: standard
apply_power_transform: False
pre_cluster_dim_reduction: False
max_anomaly_samples: 2000
anomaly_timeout: 600

================================================================================

## DATASET INFORMATION
Dataset: data\processed\borg_traces_cleaned.csv
Number of rows: 2950
Number of columns: 113

## ANOMALY DETECTION METRICS
### Confusion Matrix
True Negatives (TN): 396
False Positives (FP): 165
False Negatives (FN): 1
True Positives (TP): 28

### Performance Metrics
Accuracy:    0.7186
Precision:   0.1451
Recall:      0.9655
F1 Score:    0.2523
Specificity: 0.7059

### Additional Metrics
roc_auc: 0.8025
pr_auc: 0.2997

================================================================================

## FEATURE IMPORTANCE
Rank   Feature                        Importance
--------------------------------------------------
1      <USER>                <GROUP>.017017
2      page_cache_memory_outlier      0.015885
3      start_time_hour_tan            0.015455
4      start_time_minute_tan          0.014889
5      instance_index_outlier         0.014873
6      end_time_second_sin            0.014156
7      memory_accesses_per_instruction_outlier 0.014130
8      end_time_second_tan            0.014114
9      start_time_hour_tan_outlier    0.013905
10     end_time_hour_tan_outlier      0.013784

================================================================================

## COMPONENT METRICS: ANOMALY MODEL 1: ISOLATIONFOREST
Metrics for anomaly detection model 1 (IsolationForest)

### parameters
Metric                    Value          
----------------------------------------
bootstrap                 False
contamination             0.300000       
max_features              1.000000       
max_samples               auto
n_estimators              200
n_jobs                    None
random_state              42
verbose                   0
warm_start                False

### score_stats
Metric                    Value          
----------------------------------------
min                       -0.543977      
max                       -0.383761      
mean                      -0.440483      
std                       0.038706       
median                    -0.428012      

### n_estimators

### max_samples


================================================================================

## COMPONENT METRICS: ANOMALY MODEL 2: LOCALOUTLIERFACTOR
Metrics for anomaly detection model 2 (LocalOutlierFactor)

### parameters
Metric                    Value          
----------------------------------------
algorithm                 auto
contamination             0.300000       
leaf_size                 30
metric                    minkowski
metric_params             None
n_jobs                    None
n_neighbors               20
novelty                   True
p                         2

### score_stats
Metric                    Value          
----------------------------------------
min                       -2.769050      
max                       -0.979407      
mean                      -1.163941      
std                       0.288613       
median                    -1.066592      

### n_neighbors_effective

### n_samples


================================================================================

## COMPONENT METRICS: ANOMALY MODEL 3: ONECLASSSVM
Metrics for anomaly detection model 3 (OneClassSVM)

### parameters
Metric                    Value          
----------------------------------------
cache_size                200
coef0                     0.000000       
degree                    3
gamma                     scale
kernel                    rbf
max_iter                  -1
nu                        0.360000       
shrinking                 True
tol                       0.001000       
verbose                   False

### score_stats
Metric                    Value          
----------------------------------------
min                       5.809815       
max                       123.596294     
mean                      84.505954      
std                       21.336059      
median                    87.972285      

### n_support_vectors

### dual_coef_stats
Metric                    Value          
----------------------------------------
min                       0.019485       
max                       1.000000       
mean                      0.986301       


================================================================================

## EXECUTION TIME
Start time: 2025-05-08 20:56:52
End time: 2025-05-08 20:57:28
Total execution time: 36.05 seconds (0.60 minutes)

================================================================================

