# Metrics Log - ANOMALY
# Run ID: 20250508_205936
# Timestamp: 2025-05-08 20:59:36
================================================================================

## RUN CONFIGURATION
data: data\processed\borg_traces_cleaned.csv
task: anomaly
target_cols: ['average_usage_cpu', 'average_usage_memory']
batch_size: 1000
test_size: 0.2
use_gpu: False
use_xgboost: 1
use_lightgbm: 1
use_ensemble: 1
dynamic_components: True
min_components: 3
max_components: 15
learning_rate: 0.002
lr_scheduler_type: one_cycle
lr_warmup_epochs: 8
lr_min: 5e-06
lr_max: 0.01
lr_cycle_epochs: 30
output: results
labeled_anomaly_data: None
augmentation_ratio: 0.05
similarity_threshold: 0.7
show_viz_info: False
visualize_clusters: False
scaling_method: standard
apply_power_transform: False
pre_cluster_dim_reduction: False
max_anomaly_samples: 2000
anomaly_timeout: 600

================================================================================

## DATASET INFORMATION
Dataset: data\processed\borg_traces_cleaned.csv
Number of rows: 2950
Number of columns: 113

## ANOMALY DETECTION METRICS
### Confusion Matrix
True Negatives (TN): 386
False Positives (FP): 175
False Negatives (FN): 0
True Positives (TP): 29

### Performance Metrics
Accuracy:    0.7034
Precision:   0.1422
Recall:      1.0000
F1 Score:    0.2489
Specificity: 0.6881

### Additional Metrics
roc_auc: 0.7953
pr_auc: 0.3437

================================================================================

## FEATURE IMPORTANCE
Rank   Feature                        Importance
--------------------------------------------------
1      <USER>                  <GROUP>.017590
2      start_time_second_sin          0.015533
3      end_time_second_tan            0.015379
4      alloc_collection_id            0.015327
5      start_time_second_tan          0.014919
6      end_time_second_sin            0.014711
7      collection_type                0.014337
8      end_time_hour_tan              0.014157
9      duration_category              0.013888
10     end_time_hour_tan_outlier      0.013799

================================================================================

## COMPONENT METRICS: ANOMALY MODEL 1: ISOLATIONFOREST
Metrics for anomaly detection model 1 (IsolationForest)

### parameters
Metric                    Value          
----------------------------------------
bootstrap                 False
contamination             0.300000       
max_features              1.000000       
max_samples               auto
n_estimators              200
n_jobs                    None
random_state              42
verbose                   0
warm_start                False

### score_stats
Metric                    Value          
----------------------------------------
min                       -0.551435      
max                       -0.375370      
mean                      -0.441769      
std                       0.039518       
median                    -0.439305      

### n_estimators

### max_samples


================================================================================

## COMPONENT METRICS: ANOMALY MODEL 2: LOCALOUTLIERFACTOR
Metrics for anomaly detection model 2 (LocalOutlierFactor)

### parameters
Metric                    Value          
----------------------------------------
algorithm                 auto
contamination             0.300000       
leaf_size                 30
metric                    minkowski
metric_params             None
n_jobs                    None
n_neighbors               20
novelty                   True
p                         2

### score_stats
Metric                    Value          
----------------------------------------
min                       -5.231967      
max                       -0.973921      
mean                      -1.176618      
std                       0.461027       
median                    -1.073033      

### n_neighbors_effective

### n_samples


================================================================================

## COMPONENT METRICS: ANOMALY MODEL 3: ONECLASSSVM
Metrics for anomaly detection model 3 (OneClassSVM)

### parameters
Metric                    Value          
----------------------------------------
cache_size                200
coef0                     0.000000       
degree                    3
gamma                     scale
kernel                    rbf
max_iter                  -1
nu                        0.360000       
shrinking                 True
tol                       0.001000       
verbose                   False

### score_stats
Metric                    Value          
----------------------------------------
min                       1.516509       
max                       122.949910     
mean                      82.212982      
std                       23.820709      
median                    86.876108      

### n_support_vectors

### dual_coef_stats
Metric                    Value          
----------------------------------------
min                       0.115540       
max                       1.000000       
mean                      0.982265       


================================================================================

## EXECUTION TIME
Start time: 2025-05-08 20:59:36
End time: 2025-05-08 21:00:10
Total execution time: 33.74 seconds (0.56 minutes)

================================================================================

