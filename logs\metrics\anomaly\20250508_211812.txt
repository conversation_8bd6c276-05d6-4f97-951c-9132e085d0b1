# Metrics Log - ANOMALY
# Run ID: 20250508_211812
# Timestamp: 2025-05-08 21:18:12
================================================================================

## RUN CONFIGURATION
data: data\processed\borg_traces_cleaned.csv
task: anomaly
target_cols: ['average_usage_cpu', 'average_usage_memory']
batch_size: 1000
test_size: 0.2
use_gpu: False
use_xgboost: 1
use_lightgbm: 1
use_ensemble: 1
dynamic_components: True
min_components: 3
max_components: 15
learning_rate: 0.002
lr_scheduler_type: one_cycle
lr_warmup_epochs: 8
lr_min: 5e-06
lr_max: 0.01
lr_cycle_epochs: 30
output: results
labeled_anomaly_data: None
augmentation_ratio: 0.05
similarity_threshold: 0.7
show_viz_info: False
visualize_clusters: False
scaling_method: standard
apply_power_transform: False
pre_cluster_dim_reduction: False
max_anomaly_samples: 2000
anomaly_timeout: 600
anomaly_threshold: 0.7
optimize_threshold: True
optimization_metric: balanced

================================================================================

## DATASET INFORMATION
Dataset: data\processed\borg_traces_cleaned.csv
Number of rows: 405890
Number of columns: 113

