# Metrics Log - ANOMALY
# Run ID: 20250508_212039
# Timestamp: 2025-05-08 21:20:39
================================================================================

## RUN CONFIGURATION
data: data\processed\borg_traces_small.csv
task: anomaly
target_cols: ['average_usage_cpu', 'average_usage_memory']
batch_size: 10000
test_size: 0.2
use_gpu: False
use_xgboost: 1
use_lightgbm: 1
use_ensemble: 1
dynamic_components: True
min_components: 3
max_components: 15
learning_rate: 0.002
lr_scheduler_type: one_cycle
lr_warmup_epochs: 8
lr_min: 5e-06
lr_max: 0.01
lr_cycle_epochs: 30
output: results
labeled_anomaly_data: None
augmentation_ratio: 0.05
similarity_threshold: 0.7
show_viz_info: False
visualize_clusters: False
scaling_method: standard
apply_power_transform: False
pre_cluster_dim_reduction: False
max_anomaly_samples: 2000
anomaly_timeout: 600
anomaly_threshold: 0.7
optimize_threshold: True
optimization_metric: balanced

================================================================================

## DATASET INFORMATION
Dataset: data\processed\borg_traces_small.csv
Number of rows: 3000
Number of columns: 113

## ANOMALY DETECTION METRICS
### Confusion Matrix
True Negatives (TN): 405
False Positives (FP): 165
False Negatives (FN): 0
True Positives (TP): 30

### Performance Metrics
Accuracy:    0.7250
Precision:   0.1538
Recall:      1.0000
F1 Score:    0.2667
Specificity: 0.7105

### Additional Metrics
roc_auc: 0.8874
pr_auc: 0.4540

================================================================================

## FEATURE IMPORTANCE
Rank   Feature                        Importance
--------------------------------------------------
1      <USER>                 <GROUP>.016380
2      instance_index_outlier         0.015540
3      collection_type                0.015164
4      start_time_second_sin          0.015101
5      end_time_second_tan            0.015089
6      start_time_second_tan          0.014468
7      time_hour_tan                  0.014143
8      end_time_second_sin            0.014060
9      resource_request_cpu_outlier   0.014047
10     end_time_hour_tan_outlier      0.013842

================================================================================

## COMPONENT METRICS: ANOMALY MODEL 1: ISOLATIONFOREST
Metrics for anomaly detection model 1 (IsolationForest)

### parameters
Metric                    Value          
----------------------------------------
bootstrap                 False
contamination             0.300000       
max_features              1.000000       
max_samples               auto
n_estimators              200
n_jobs                    None
random_state              42
verbose                   0
warm_start                False

### score_stats
Metric                    Value          
----------------------------------------
min                       -0.577676      
max                       -0.388338      
mean                      -0.450118      
std                       0.041946       
median                    -0.443609      

### n_estimators

### max_samples


================================================================================

## COMPONENT METRICS: ANOMALY MODEL 2: LOCALOUTLIERFACTOR
Metrics for anomaly detection model 2 (LocalOutlierFactor)

### parameters
Metric                    Value          
----------------------------------------
algorithm                 auto
contamination             0.300000       
leaf_size                 30
metric                    minkowski
metric_params             None
n_jobs                    None
n_neighbors               20
novelty                   True
p                         2

### score_stats
Metric                    Value          
----------------------------------------
min                       -3.437844      
max                       -0.986844      
mean                      -1.162583      
std                       0.338401       
median                    -1.050906      

### n_neighbors_effective

### n_samples


================================================================================

## COMPONENT METRICS: ANOMALY MODEL 3: ONECLASSSVM
Metrics for anomaly detection model 3 (OneClassSVM)

### parameters
Metric                    Value          
----------------------------------------
cache_size                200
coef0                     0.000000       
degree                    3
gamma                     scale
kernel                    rbf
max_iter                  -1
nu                        0.360000       
shrinking                 True
tol                       0.001000       
verbose                   False

### score_stats
Metric                    Value          
----------------------------------------
min                       6.507928       
max                       116.329927     
mean                      77.918969      
std                       24.865505      
median                    83.202748      

### n_support_vectors

### dual_coef_stats
Metric                    Value          
----------------------------------------
min                       0.085561       
max                       1.000000       
mean                      0.980926       


================================================================================

## EXECUTION TIME
Start time: 2025-05-08 21:20:39
End time: 2025-05-08 21:21:13
Total execution time: 34.21 seconds (0.57 minutes)

================================================================================

