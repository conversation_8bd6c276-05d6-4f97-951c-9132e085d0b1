# Metrics Log - ANOMALY
# Run ID: 20250508_212205
# Timestamp: 2025-05-08 21:22:05
================================================================================

## RUN CONFIGURATION
data: data\processed\borg_traces_cleaned.csv
task: anomaly
target_cols: ['average_usage_cpu', 'average_usage_memory']
batch_size: 10000
test_size: 0.2
use_gpu: False
use_xgboost: 1
use_lightgbm: 1
use_ensemble: 1
dynamic_components: True
min_components: 3
max_components: 15
learning_rate: 0.002
lr_scheduler_type: one_cycle
lr_warmup_epochs: 8
lr_min: 5e-06
lr_max: 0.01
lr_cycle_epochs: 30
output: results
labeled_anomaly_data: None
augmentation_ratio: 0.05
similarity_threshold: 0.7
show_viz_info: False
visualize_clusters: False
scaling_method: standard
apply_power_transform: False
pre_cluster_dim_reduction: False
max_anomaly_samples: 5000
anomaly_timeout: 900
anomaly_threshold: 0.8
optimize_threshold: True
optimization_metric: balanced

================================================================================

## DATASET INFORMATION
Dataset: data\processed\borg_traces_cleaned.csv
Number of rows: 6000
Number of columns: 113

## ANOMALY DETECTION METRICS
### Confusion Matrix
True Negatives (TN): 580
False Positives (FP): 560
False Negatives (FN): 1
True Positives (TP): 59

### Performance Metrics
Accuracy:    0.5325
Precision:   0.0953
Recall:      0.9833
F1 Score:    0.1738
Specificity: 0.5088

### Additional Metrics
roc_auc: 0.8376
pr_auc: 0.3658

================================================================================

## FEATURE IMPORTANCE
Rank   Feature                        Importance
--------------------------------------------------
1      <USER>                  <GROUP>.016384
2      end_time_second_tan            0.015142
3      start_time_second_sin          0.014910
4      start_time_second_tan          0.014618
5      start_time_hour_tan_outlier    0.014454
6      end_time_second_sin            0.014040
7      start_time_minute_tan          0.013729
8      end_time_hour_tan_outlier      0.013720
9      end_time_hour_tan              0.013601
10     collection_type                0.013509

================================================================================

## COMPONENT METRICS: ANOMALY MODEL 1: ISOLATIONFOREST
Metrics for anomaly detection model 1 (IsolationForest)

### parameters
Metric                    Value          
----------------------------------------
bootstrap                 False
contamination             0.300000       
max_features              1.000000       
max_samples               auto
n_estimators              200
n_jobs                    None
random_state              42
verbose                   0
warm_start                False

### score_stats
Metric                    Value          
----------------------------------------
min                       -0.524648      
max                       -0.376738      
mean                      -0.441068      
std                       0.033990       
median                    -0.438276      

### n_estimators

### max_samples


================================================================================

## COMPONENT METRICS: ANOMALY MODEL 2: LOCALOUTLIERFACTOR
Metrics for anomaly detection model 2 (LocalOutlierFactor)

### parameters
Metric                    Value          
----------------------------------------
algorithm                 auto
contamination             0.300000       
leaf_size                 30
metric                    minkowski
metric_params             None
n_jobs                    None
n_neighbors               20
novelty                   True
p                         2

### score_stats
Metric                    Value          
----------------------------------------
min                       -2.233213      
max                       -0.978492      
mean                      -1.121257      
std                       0.193727       
median                    -1.045597      

### n_neighbors_effective

### n_samples


================================================================================

## COMPONENT METRICS: ANOMALY MODEL 3: ONECLASSSVM
Metrics for anomaly detection model 3 (OneClassSVM)

### parameters
Metric                    Value          
----------------------------------------
cache_size                200
coef0                     0.000000       
degree                    3
gamma                     scale
kernel                    rbf
max_iter                  -1
nu                        0.360000       
shrinking                 True
tol                       0.001000       
verbose                   False

### score_stats
Metric                    Value          
----------------------------------------
min                       36.166590      
max                       294.545417     
mean                      202.850778     
std                       43.536210      
median                    207.361608     

### n_support_vectors

### dual_coef_stats
Metric                    Value          
----------------------------------------
min                       0.007725       
max                       1.000000       
mean                      0.991394       


================================================================================

## EXECUTION TIME
Start time: 2025-05-08 21:22:05
End time: 2025-05-08 21:23:00
Total execution time: 55.15 seconds (0.92 minutes)

================================================================================

