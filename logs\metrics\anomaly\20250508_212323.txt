# Metrics Log - ANOMALY
# Run ID: 20250508_212323
# Timestamp: 2025-05-08 21:23:23
================================================================================

## RUN CONFIGURATION
data: data\processed\borg_traces_cleaned.csv
task: anomaly
target_cols: ['average_usage_cpu', 'average_usage_memory']
batch_size: 10000
test_size: 0.2
use_gpu: False
use_xgboost: 1
use_lightgbm: 1
use_ensemble: 1
dynamic_components: True
min_components: 3
max_components: 15
learning_rate: 0.002
lr_scheduler_type: one_cycle
lr_warmup_epochs: 8
lr_min: 5e-06
lr_max: 0.01
lr_cycle_epochs: 30
output: results
labeled_anomaly_data: None
augmentation_ratio: 0.05
similarity_threshold: 0.7
show_viz_info: False
visualize_clusters: False
scaling_method: standard
apply_power_transform: False
pre_cluster_dim_reduction: False
max_anomaly_samples: 5000
anomaly_timeout: 900
anomaly_threshold: 0.9
optimize_threshold: True
optimization_metric: precision

================================================================================

## DATASET INFORMATION
Dataset: data\processed\borg_traces_cleaned.csv
Number of rows: 6000
Number of columns: 113

## ANOMALY DETECTION METRICS
### Confusion Matrix
True Negatives (TN): 1047
False Positives (FP): 93
False Negatives (FN): 14
True Positives (TP): 46

### Performance Metrics
Accuracy:    0.9108
Precision:   0.3309
Recall:      0.7667
F1 Score:    0.4623
Specificity: 0.9184

### Additional Metrics
roc_auc: 0.8555
pr_auc: 0.4042

================================================================================

## FEATURE IMPORTANCE
Rank   Feature                        Importance
--------------------------------------------------
1      <USER>                 <GROUP>.016200
2      memory_accesses_per_instruction_outlier 0.015155
3      end_time_second_tan            0.014675
4      start_time_second_sin          0.014576
5      cycles_per_instruction         0.014352
6      collection_type                0.014112
7      start_time_second_tan          0.014088
8      end_time_second_sin            0.014056
9      instance_index_outlier         0.014002
10     page_cache_memory_outlier      0.013998

================================================================================

## COMPONENT METRICS: ANOMALY MODEL 1: ISOLATIONFOREST
Metrics for anomaly detection model 1 (IsolationForest)

### parameters
Metric                    Value          
----------------------------------------
bootstrap                 False
contamination             0.300000       
max_features              1.000000       
max_samples               auto
n_estimators              200
n_jobs                    None
random_state              42
verbose                   0
warm_start                False

### score_stats
Metric                    Value          
----------------------------------------
min                       -0.532604      
max                       -0.386368      
mean                      -0.444627      
std                       0.033278       
median                    -0.440809      

### n_estimators

### max_samples


================================================================================

## COMPONENT METRICS: ANOMALY MODEL 2: LOCALOUTLIERFACTOR
Metrics for anomaly detection model 2 (LocalOutlierFactor)

### parameters
Metric                    Value          
----------------------------------------
algorithm                 auto
contamination             0.300000       
leaf_size                 30
metric                    minkowski
metric_params             None
n_jobs                    None
n_neighbors               20
novelty                   True
p                         2

### score_stats
Metric                    Value          
----------------------------------------
min                       -2.077535      
max                       -0.976338      
mean                      -1.090847      
std                       0.177035       
median                    -1.023952      

### n_neighbors_effective

### n_samples


================================================================================

## COMPONENT METRICS: ANOMALY MODEL 3: ONECLASSSVM
Metrics for anomaly detection model 3 (OneClassSVM)

### parameters
Metric                    Value          
----------------------------------------
cache_size                200
coef0                     0.000000       
degree                    3
gamma                     scale
kernel                    rbf
max_iter                  -1
nu                        0.360000       
shrinking                 True
tol                       0.001000       
verbose                   False

### score_stats
Metric                    Value          
----------------------------------------
min                       27.404581      
max                       285.913254     
mean                      207.090553     
std                       47.019574      
median                    212.962443     

### n_support_vectors

### dual_coef_stats
Metric                    Value          
----------------------------------------
min                       0.052132       
max                       1.000000       
mean                      0.993103       


================================================================================

## EXECUTION TIME
Start time: 2025-05-08 21:23:23
End time: 2025-05-08 21:24:20
Total execution time: 57.09 seconds (0.95 minutes)

================================================================================

