# Metrics Log - ANOMALY
# Run ID: 20250508_212628
# Timestamp: 2025-05-08 21:26:28
================================================================================

## RUN CONFIGURATION
data: data\processed\borg_traces_cleaned.csv
task: anomaly
target_cols: ['average_usage_cpu', 'average_usage_memory']
batch_size: 10000
test_size: 0.2
use_gpu: False
use_xgboost: 1
use_lightgbm: 1
use_ensemble: 1
dynamic_components: True
min_components: 3
max_components: 15
learning_rate: 0.002
lr_scheduler_type: one_cycle
lr_warmup_epochs: 8
lr_min: 5e-06
lr_max: 0.01
lr_cycle_epochs: 30
output: results
labeled_anomaly_data: None
augmentation_ratio: 0.05
similarity_threshold: 0.7
show_viz_info: False
visualize_clusters: False
scaling_method: standard
apply_power_transform: False
pre_cluster_dim_reduction: False
max_anomaly_samples: 5000
anomaly_timeout: 900
anomaly_threshold: 0.7
optimize_threshold: False
optimization_metric: f1
advanced_ensemble: True

================================================================================

## DATASET INFORMATION
Dataset: data\processed\borg_traces_cleaned.csv
Number of rows: 6000
Number of columns: 113

## ANOMALY DETECTION METRICS
### Confusion Matrix
True Negatives (TN): 785
False Positives (FP): 355
False Negatives (FN): 1
True Positives (TP): 59

### Performance Metrics
Accuracy:    0.7033
Precision:   0.1425
Recall:      0.9833
F1 Score:    0.2489
Specificity: 0.6886

### Additional Metrics
roc_auc: 0.8835
pr_auc: 0.5147

================================================================================

## FEATURE IMPORTANCE
Rank   Feature                        Importance
--------------------------------------------------
1      <USER>                <GROUP>.014844
2      end_time_second_sin            0.014786
3      end_time_second_tan            0.014782
4      duration_category              0.014434
5      memory_accesses_per_instruction_outlier 0.014346
6      time_hour_tan                  0.014036
7      cycles_per_instruction         0.013944
8      start_time_second_sin          0.013917
9      instance_index                 0.013731
10     start_time_second_tan          0.013650

================================================================================

## COMPONENT METRICS: ANOMALY MODEL 1: ISOLATIONFOREST
Metrics for anomaly detection model 1 (IsolationForest)

### parameters
Metric                    Value          
----------------------------------------
bootstrap                 False
contamination             0.300000       
max_features              1.000000       
max_samples               auto
n_estimators              200
n_jobs                    None
random_state              42
verbose                   0
warm_start                False

### score_stats
Metric                    Value          
----------------------------------------
min                       -0.594342      
max                       -0.376213      
mean                      -0.435754      
std                       0.039009       
median                    -0.432189      

### n_estimators

### max_samples


================================================================================

## COMPONENT METRICS: ANOMALY MODEL 2: LOCALOUTLIERFACTOR
Metrics for anomaly detection model 2 (LocalOutlierFactor)

### parameters
Metric                    Value          
----------------------------------------
algorithm                 auto
contamination             0.300000       
leaf_size                 30
metric                    minkowski
metric_params             None
n_jobs                    None
n_neighbors               20
novelty                   True
p                         2

### score_stats
Metric                    Value          
----------------------------------------
min                       -2.671487      
max                       -0.967145      
mean                      -1.120160      
std                       0.242229       
median                    -1.042052      

### n_neighbors_effective

### n_samples


================================================================================

## COMPONENT METRICS: ANOMALY MODEL 3: ONECLASSSVM
Metrics for anomaly detection model 3 (OneClassSVM)

### parameters
Metric                    Value          
----------------------------------------
cache_size                200
coef0                     0.000000       
degree                    3
gamma                     scale
kernel                    rbf
max_iter                  -1
nu                        0.360000       
shrinking                 True
tol                       0.001000       
verbose                   False

### score_stats
Metric                    Value          
----------------------------------------
min                       10.009760      
max                       272.114578     
mean                      203.877545     
std                       50.861514      
median                    213.591641     

### n_support_vectors

### dual_coef_stats
Metric                    Value          
----------------------------------------
min                       0.009044       
max                       1.000000       
mean                      0.990826       


================================================================================

## EXECUTION TIME
Start time: 2025-05-08 21:26:28
End time: 2025-05-08 21:27:24
Total execution time: 55.99 seconds (0.93 minutes)

================================================================================

