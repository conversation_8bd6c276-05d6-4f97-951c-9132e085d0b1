# Metrics Log - ANOMALY
# Run ID: 20250508_214420
# Timestamp: 2025-05-08 21:44:20
================================================================================

## RUN CONFIGURATION
data: data\processed\borg_traces_cleaned.csv
task: anomaly
target_cols: ['average_usage_cpu', 'average_usage_memory']
batch_size: 1000
test_size: 0.2
use_gpu: False
use_xgboost: 1
use_lightgbm: 1
use_ensemble: 1
dynamic_components: True
min_components: 2
max_components: 15
learning_rate: 0.002
lr_scheduler_type: one_cycle
lr_warmup_epochs: 8
lr_min: 5e-06
lr_max: 0.01
lr_cycle_epochs: 30
output: results
labeled_anomaly_data: None
augmentation_ratio: 0.5
similarity_threshold: 0.0
show_viz_info: False
visualize_clusters: True
scaling_method: standard
apply_power_transform: False
pre_cluster_dim_reduction: False

================================================================================

## DATASET INFORMATION
Dataset: data\processed\borg_traces_cleaned.csv
Number of rows: 6000
Number of columns: 113

## ANOMALY DETECTION METRICS
### Confusion Matrix
True Negatives (TN): 596
False Positives (FP): 4
False Negatives (FN): 194
True Positives (TP): 406

### Performance Metrics
Accuracy:    0.8350
Precision:   0.9902
Recall:      0.6767
F1 Score:    0.8040
Specificity: 0.9933

### Additional Metrics
roc_auc: 0.9319
pr_auc: 0.9438

================================================================================

## FEATURE IMPORTANCE
Rank   Feature                        Importance
--------------------------------------------------
1      <USER>                  <GROUP>.013660
2      time_minute_tan_outlier        0.013501
3      end_time_minute_tan_outlier    0.013206
4      collection_type                0.013164
5      resource_request_cpu_outlier   0.012892
6      instance_index_outlier         0.012728
7      start_time_hour_tan_outlier    0.012546
8      cpu_usage_distribution_max_outlier 0.012279
9      memory_accesses_per_instruction_outlier 0.011896
10     end_time_second_tan            0.011765

================================================================================

## COMPONENT METRICS: ANOMALY MODEL 1: ISOLATIONFOREST
Metrics for anomaly detection model 1 (IsolationForest)

### parameters
Metric                    Value          
----------------------------------------
bootstrap                 False
contamination             0.300000       
max_features              1.000000       
max_samples               auto
n_estimators              200
n_jobs                    None
random_state              42
verbose                   0
warm_start                False

### score_stats
Metric                    Value          
----------------------------------------
min                       -0.500500      
max                       -0.338058      
mean                      -0.393409      
std                       0.043225       
median                    -0.382906      

### n_estimators

### max_samples


================================================================================

## COMPONENT METRICS: ANOMALY MODEL 2: LOCALOUTLIERFACTOR
Metrics for anomaly detection model 2 (LocalOutlierFactor)

### parameters
Metric                    Value          
----------------------------------------
algorithm                 auto
contamination             0.300000       
leaf_size                 30
metric                    minkowski
metric_params             None
n_jobs                    None
n_neighbors               10
novelty                   True
p                         2

### score_stats
Metric                    Value          
----------------------------------------
min                       -3.135577      
max                       -0.961442      
mean                      -1.512142      
std                       0.624899       
median                    -1.156184      

### n_neighbors_effective

### n_samples


================================================================================

## COMPONENT METRICS: ANOMALY MODEL 3: ONECLASSSVM
Metrics for anomaly detection model 3 (OneClassSVM)

### parameters
Metric                    Value          
----------------------------------------
cache_size                200
coef0                     0.000000       
degree                    3
gamma                     auto
kernel                    rbf
max_iter                  -1
nu                        0.360000       
shrinking                 True
tol                       0.001000       
verbose                   False

### score_stats
Metric                    Value          
----------------------------------------
min                       0.235564       
max                       23.270186      
mean                      11.361250      
std                       6.735619       
median                    10.943432      

### n_support_vectors

### dual_coef_stats
Metric                    Value          
----------------------------------------
min                       0.025394       
max                       1.000000       
mean                      0.960000       


================================================================================

## EXECUTION TIME
Start time: 2025-05-08 21:44:20
End time: 2025-05-08 21:45:05
Total execution time: 45.77 seconds (0.76 minutes)

================================================================================

