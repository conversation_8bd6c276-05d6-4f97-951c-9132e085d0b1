# Metrics Log - ANOMALY
# Run ID: 20250508_220224
# Timestamp: 2025-05-08 22:02:24
================================================================================

## RUN CONFIGURATION
data: data\processed\borg_traces_cleaned.csv
task: anomaly
target_cols: ['average_usage_cpu', 'average_usage_memory']
batch_size: 1000
test_size: 0.2
use_gpu: False
use_xgboost: 1
use_lightgbm: 1
use_ensemble: 1
dynamic_components: True
min_components: 2
max_components: 15
learning_rate: 0.002
lr_scheduler_type: one_cycle
lr_warmup_epochs: 8
lr_min: 5e-06
lr_max: 0.01
lr_cycle_epochs: 30
output: results
labeled_anomaly_data: None
augmentation_ratio: 0.6
similarity_threshold: 0.0
show_viz_info: False
visualize_clusters: True
scaling_method: standard
apply_power_transform: False
pre_cluster_dim_reduction: False

================================================================================

## DATASET INFORMATION
Dataset: data\processed\borg_traces_cleaned.csv
Number of rows: 6000
Number of columns: 113

## ANOMALY DETECTION METRICS
### Confusion Matrix
True Negatives (TN): 442
False Positives (FP): 38
False Negatives (FN): 124
True Positives (TP): 596

### Performance Metrics
Accuracy:    0.8650
Precision:   0.9401
Recall:      0.8278
F1 Score:    0.8804
Specificity: 0.9208

### Additional Metrics
roc_auc: 0.9208
pr_auc: 0.9531

================================================================================

## FEATURE IMPORTANCE
Rank   Feature                        Importance
--------------------------------------------------
1      <USER>      <GROUP>.012827
2      start_time_minute_tan_outlier  0.012555
3      time_hour_tan                  0.012481
4      start_time_second_sin          0.012169
5      start_time_minute_tan          0.012131
6      start_time_second_tan          0.012119
7      time_hour_tan_outlier          0.011941
8      instance_index                 0.011938
9      collection_type                0.011768
10     end_time_second_sin            0.011714

================================================================================

## COMPONENT METRICS: ANOMALY MODEL 1: ISOLATIONFOREST
Metrics for anomaly detection model 1 (IsolationForest)

### parameters
Metric                    Value          
----------------------------------------
bootstrap                 False
contamination             0.400000       
max_features              1.000000       
max_samples               auto
n_estimators              200
n_jobs                    None
random_state              42
verbose                   0
warm_start                False

### score_stats
Metric                    Value          
----------------------------------------
min                       -0.511256      
max                       -0.344357      
mean                      -0.402724      
std                       0.042635       
median                    -0.397594      

### n_estimators

### max_samples


================================================================================

## COMPONENT METRICS: ANOMALY MODEL 2: LOCALOUTLIERFACTOR
Metrics for anomaly detection model 2 (LocalOutlierFactor)

### parameters
Metric                    Value          
----------------------------------------
algorithm                 auto
contamination             0.400000       
leaf_size                 30
metric                    minkowski
metric_params             None
n_jobs                    None
n_neighbors               10
novelty                   True
p                         2

### score_stats
Metric                    Value          
----------------------------------------
min                       -16.350643     
max                       -0.980149      
mean                      -1.744864      
std                       1.589625       
median                    -1.333479      

### n_neighbors_effective

### n_samples


================================================================================

## COMPONENT METRICS: ANOMALY MODEL 3: ONECLASSSVM
Metrics for anomaly detection model 3 (OneClassSVM)

### parameters
Metric                    Value          
----------------------------------------
cache_size                200
coef0                     0.000000       
degree                    3
gamma                     0.004277       
kernel                    rbf
max_iter                  1000
nu                        0.500000       
shrinking                 True
tol                       0.000100       
verbose                   False

### score_stats
Metric                    Value          
----------------------------------------
min                       25.188862      
max                       133.549471     
mean                      81.837627      
std                       30.667542      
median                    83.289921      

### n_support_vectors

### dual_coef_stats
Metric                    Value          
----------------------------------------
min                       0.166615       
max                       1.000000       
mean                      0.994036       


================================================================================

## EXECUTION TIME
Start time: 2025-05-08 22:02:24
End time: 2025-05-08 22:03:12
Total execution time: 48.44 seconds (0.81 minutes)

================================================================================

