# Metrics Log - ANOMALY
# Run ID: 20250508_220354
# Timestamp: 2025-05-08 22:03:54
================================================================================

## RUN CONFIGURATION
data: data\processed\borg_traces_cleaned.csv
task: anomaly
target_cols: ['average_usage_cpu', 'average_usage_memory']
batch_size: 1000
test_size: 0.2
use_gpu: False
use_xgboost: 1
use_lightgbm: 1
use_ensemble: 1
dynamic_components: True
min_components: 2
max_components: 15
learning_rate: 0.002
lr_scheduler_type: one_cycle
lr_warmup_epochs: 8
lr_min: 5e-06
lr_max: 0.01
lr_cycle_epochs: 30
output: results
labeled_anomaly_data: None
augmentation_ratio: 0.7
similarity_threshold: 0.0
show_viz_info: False
visualize_clusters: True
scaling_method: standard
apply_power_transform: False
pre_cluster_dim_reduction: False

================================================================================

## DATASET INFORMATION
Dataset: data\processed\borg_traces_cleaned.csv
Number of rows: 6000
Number of columns: 113

## ANOMALY DETECTION METRICS
### Confusion Matrix
True Negatives (TN): 345
False Positives (FP): 15
False Negatives (FN): 195
True Positives (TP): 645

### Performance Metrics
Accuracy:    0.8250
Precision:   0.9773
Recall:      0.7679
F1 Score:    0.8600
Specificity: 0.9583

### Additional Metrics
roc_auc: 0.9262
pr_auc: 0.9698

================================================================================

## FEATURE IMPORTANCE
Rank   Feature                        Importance
--------------------------------------------------
1      <USER>              <GROUP>.012727
2      machine_id                     0.012477
3      start_time_hour_tan_outlier    0.012419
4      maximum_usage_cpu_outlier      0.012204
5      page_cache_memory_outlier      0.012201
6      page_cache_memory              0.012068
7      cpu_usage_distribution_max_outlier 0.012018
8      time_hour_tan                  0.012006
9      alloc_collection_id            0.011834
10     assigned_memory_outlier        0.011827

================================================================================

## COMPONENT METRICS: ANOMALY MODEL 1: ISOLATIONFOREST
Metrics for anomaly detection model 1 (IsolationForest)

### parameters
Metric                    Value          
----------------------------------------
bootstrap                 False
contamination             0.450000       
max_features              1.000000       
max_samples               auto
n_estimators              200
n_jobs                    None
random_state              42
verbose                   0
warm_start                False

### score_stats
Metric                    Value          
----------------------------------------
min                       -0.506652      
max                       -0.340816      
mean                      -0.401340      
std                       0.042299       
median                    -0.390692      

### n_estimators

### max_samples


================================================================================

## COMPONENT METRICS: ANOMALY MODEL 2: LOCALOUTLIERFACTOR
Metrics for anomaly detection model 2 (LocalOutlierFactor)

### parameters
Metric                    Value          
----------------------------------------
algorithm                 auto
contamination             0.450000       
leaf_size                 30
metric                    minkowski
metric_params             None
n_jobs                    None
n_neighbors               10
novelty                   True
p                         2

### score_stats
Metric                    Value          
----------------------------------------
min                       -7.580494      
max                       -0.967639      
mean                      -1.639389      
std                       0.795154       
median                    -1.487684      

### n_neighbors_effective

### n_samples


================================================================================

## COMPONENT METRICS: ANOMALY MODEL 3: ONECLASSSVM
Metrics for anomaly detection model 3 (OneClassSVM)

### parameters
Metric                    Value          
----------------------------------------
cache_size                200
coef0                     0.000000       
degree                    3
gamma                     0.003831       
kernel                    rbf
max_iter                  1000
nu                        0.500000       
shrinking                 True
tol                       0.000100       
verbose                   False

### score_stats
Metric                    Value          
----------------------------------------
min                       27.186350      
max                       140.251159     
mean                      86.747819      
std                       31.118987      
median                    91.088320      

### n_support_vectors

### dual_coef_stats
Metric                    Value          
----------------------------------------
min                       0.205319       
max                       1.000000       
mean                      0.994036       


================================================================================

## EXECUTION TIME
Start time: 2025-05-08 22:03:54
End time: 2025-05-08 22:04:45
Total execution time: 51.36 seconds (0.86 minutes)

================================================================================

