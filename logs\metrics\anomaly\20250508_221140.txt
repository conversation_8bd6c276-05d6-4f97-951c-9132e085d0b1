# Metrics Log - ANOMALY
# Run ID: 20250508_221140
# Timestamp: 2025-05-08 22:11:40
================================================================================

## RUN CONFIGURATION
data: data\processed\borg_traces_cleaned.csv
task: anomaly
target_cols: ['average_usage_cpu', 'average_usage_memory']
batch_size: 1000
test_size: 0.2
use_gpu: False
use_xgboost: 1
use_lightgbm: 1
use_ensemble: 1
dynamic_components: True
min_components: 2
max_components: 15
learning_rate: 0.002
lr_scheduler_type: one_cycle
lr_warmup_epochs: 8
lr_min: 5e-06
lr_max: 0.01
lr_cycle_epochs: 30
output: results
labeled_anomaly_data: None
augmentation_ratio: 0.8
similarity_threshold: 0.0
show_viz_info: False
visualize_clusters: True
scaling_method: standard
apply_power_transform: False
pre_cluster_dim_reduction: False

================================================================================

## DATASET INFORMATION
Dataset: data\processed\borg_traces_cleaned.csv
Number of rows: 6000
Number of columns: 113

## ANOMALY DETECTION METRICS
### Confusion Matrix
True Negatives (TN): 234
False Positives (FP): 6
False Negatives (FN): 226
True Positives (TP): 734

### Performance Metrics
Accuracy:    0.8067
Precision:   0.9919
Recall:      0.7646
F1 Score:    0.8635
Specificity: 0.9750

### Additional Metrics
roc_auc: 0.9264
pr_auc: 0.9817

================================================================================

## FEATURE IMPORTANCE
Rank   Feature                        Importance
--------------------------------------------------
1      <USER>                  <GROUP>.013503
2      memory_accesses_per_instruction_outlier 0.012899
3      instance_index                 0.012400
4      maximum_usage_memory_outlier   0.012323
5      start_time_minute_tan_outlier  0.012091
6      average_usage_memory_outlier   0.011971
7      cpu_usage_distribution_max_outlier 0.011830
8      start_time_hour_tan            0.011830
9      end_time_hour_tan              0.011634
10     page_cache_memory_outlier      0.011630

================================================================================

## COMPONENT METRICS: ANOMALY MODEL 1: ISOLATIONFOREST
Metrics for anomaly detection model 1 (IsolationForest)

### parameters
Metric                    Value          
----------------------------------------
bootstrap                 False
contamination             0.500000       
max_features              1.000000       
max_samples               auto
n_estimators              200
n_jobs                    None
random_state              42
verbose                   0
warm_start                False

### score_stats
Metric                    Value          
----------------------------------------
min                       -0.509988      
max                       -0.343883      
mean                      -0.405074      
std                       0.038355       
median                    -0.401497      

### n_estimators

### max_samples


================================================================================

## COMPONENT METRICS: ANOMALY MODEL 2: LOCALOUTLIERFACTOR
Metrics for anomaly detection model 2 (LocalOutlierFactor)

### parameters
Metric                    Value          
----------------------------------------
algorithm                 auto
contamination             0.500000       
leaf_size                 30
metric                    minkowski
metric_params             None
n_jobs                    None
n_neighbors               10
novelty                   True
p                         2

### score_stats
Metric                    Value          
----------------------------------------
min                       -3.927275      
max                       -0.980152      
mean                      -1.628273      
std                       0.573926       
median                    -1.487960      

### n_neighbors_effective

### n_samples


================================================================================

## COMPONENT METRICS: ANOMALY MODEL 3: ONECLASSSVM
Metrics for anomaly detection model 3 (OneClassSVM)

### parameters
Metric                    Value          
----------------------------------------
cache_size                500
coef0                     0.000000       
degree                    3
gamma                     0.002285       
kernel                    rbf
max_iter                  2000
nu                        0.500000       
shrinking                 True
tol                       0.000010       
verbose                   False

### score_stats
Metric                    Value          
----------------------------------------
min                       72.164040      
max                       221.021042     
mean                      160.029844     
std                       34.750620      
median                    159.106140     

### n_support_vectors

### dual_coef_stats
Metric                    Value          
----------------------------------------
min                       0.332973       
max                       1.000000       
mean                      0.996016       


================================================================================

## EXECUTION TIME
Start time: 2025-05-08 22:11:40
End time: 2025-05-08 22:12:38
Total execution time: 58.28 seconds (0.97 minutes)

================================================================================

