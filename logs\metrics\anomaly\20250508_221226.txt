# Metrics Log - ANOMALY
# Run ID: 20250508_221226
# Timestamp: 2025-05-08 22:12:26
================================================================================

## RUN CONFIGURATION
data: data\processed\borg_traces_cleaned.csv
task: anomaly
target_cols: ['average_usage_cpu', 'average_usage_memory']
batch_size: 1000
test_size: 0.2
use_gpu: False
use_xgboost: 1
use_lightgbm: 1
use_ensemble: 1
dynamic_components: True
min_components: 2
max_components: 15
learning_rate: 0.002
lr_scheduler_type: one_cycle
lr_warmup_epochs: 8
lr_min: 5e-06
lr_max: 0.01
lr_cycle_epochs: 30
output: results
labeled_anomaly_data: None
augmentation_ratio: 0.9
similarity_threshold: 0.0
show_viz_info: False
visualize_clusters: True
scaling_method: standard
apply_power_transform: False
pre_cluster_dim_reduction: False

================================================================================

## DATASET INFORMATION
Dataset: data\processed\borg_traces_cleaned.csv
Number of rows: 6000
Number of columns: 113

## ANOMALY DETECTION METRICS
### Confusion Matrix
True Negatives (TN): 116
False Positives (FP): 4
False Negatives (FN): 329
True Positives (TP): 751

### Performance Metrics
Accuracy:    0.7225
Precision:   0.9947
Recall:      0.6954
F1 Score:    0.8185
Specificity: 0.9667

### Additional Metrics
roc_auc: 0.9456
pr_auc: 0.9936

================================================================================

## FEATURE IMPORTANCE
Rank   Feature                        Importance
--------------------------------------------------
1      <USER>          <GROUP>.012311
2      memory_accesses_per_instruction_outlier 0.012290
3      end_time_hour_tan_outlier      0.011964
4      start_time_hour_tan            0.011854
5      start_time_second_sin          0.011843
6      instance_index_outlier         0.011823
7      page_cache_memory_outlier      0.011811
8      end_time_minute_tan_outlier    0.011791
9      time_second_tan_outlier        0.011767
10     start_time_hour_tan_outlier    0.011755

================================================================================

## COMPONENT METRICS: ANOMALY MODEL 1: ISOLATIONFOREST
Metrics for anomaly detection model 1 (IsolationForest)

### parameters
Metric                    Value          
----------------------------------------
bootstrap                 False
contamination             0.500000       
max_features              1.000000       
max_samples               auto
n_estimators              200
n_jobs                    None
random_state              42
verbose                   0
warm_start                False

### score_stats
Metric                    Value          
----------------------------------------
min                       -0.483484      
max                       -0.344700      
mean                      -0.411069      
std                       0.034293       
median                    -0.407276      

### n_estimators

### max_samples


================================================================================

## COMPONENT METRICS: ANOMALY MODEL 2: LOCALOUTLIERFACTOR
Metrics for anomaly detection model 2 (LocalOutlierFactor)

### parameters
Metric                    Value          
----------------------------------------
algorithm                 auto
contamination             0.500000       
leaf_size                 30
metric                    minkowski
metric_params             None
n_jobs                    None
n_neighbors               10
novelty                   True
p                         2

### score_stats
Metric                    Value          
----------------------------------------
min                       -4.241432      
max                       -0.983788      
mean                      -1.635039      
std                       0.517229       
median                    -1.561669      

### n_neighbors_effective

### n_samples


================================================================================

## COMPONENT METRICS: ANOMALY MODEL 3: ONECLASSSVM
Metrics for anomaly detection model 3 (OneClassSVM)

### parameters
Metric                    Value          
----------------------------------------
cache_size                500
coef0                     0.000000       
degree                    3
gamma                     0.002162       
kernel                    rbf
max_iter                  2000
nu                        0.500000       
shrinking                 True
tol                       0.000010       
verbose                   False

### score_stats
Metric                    Value          
----------------------------------------
min                       98.673297      
max                       222.444566     
mean                      158.673968     
std                       31.042108      
median                    154.969715     

### n_support_vectors

### dual_coef_stats
Metric                    Value          
----------------------------------------
min                       0.219426       
max                       1.000000       
mean                      0.992063       


================================================================================

## EXECUTION TIME
Start time: 2025-05-08 22:12:26
End time: 2025-05-08 22:13:24
Total execution time: 57.65 seconds (0.96 minutes)

================================================================================

