# Metrics Log - ANOMALY
# Run ID: 20250509_085712
# Timestamp: 2025-05-09 08:57:12
================================================================================

## RUN CONFIGURATION
data: data\processed\borg_traces_cleaned.csv
task: anomaly
target_cols: ['average_usage_cpu', 'average_usage_memory']
batch_size: 1000
test_size: 0.2
use_gpu: False
use_xgboost: 1
use_lightgbm: 1
use_ensemble: 1
dynamic_components: True
min_components: 2
max_components: 15
learning_rate: 0.002
lr_scheduler_type: one_cycle
lr_warmup_epochs: 8
lr_min: 5e-06
lr_max: 0.01
lr_cycle_epochs: 30
output: results
labeled_anomaly_data: None
augmentation_ratio: 0.5
similarity_threshold: 0.0
show_viz_info: False
visualize_clusters: True
scaling_method: standard
apply_power_transform: False
pre_cluster_dim_reduction: False

================================================================================

## DATASET INFORMATION
Dataset: data\processed\borg_traces_cleaned.csv
Number of rows: 6000
Number of columns: 113

## ANOMALY DETECTION METRICS
### Confusion Matrix
True Negatives (TN): 430
False Positives (FP): 170
False Negatives (FN): 21
True Positives (TP): 579

### Performance Metrics
Accuracy:    0.8408
Precision:   0.7730
Recall:      0.9650
F1 Score:    0.8584
Specificity: 0.7167

### Additional Metrics
roc_auc: 0.9412
pr_auc: 0.9503

================================================================================

## FEATURE IMPORTANCE
Rank   Feature                        Importance
--------------------------------------------------
1      <USER>                     <GROUP>.013783
2      start_time_minute_tan_outlier  0.013677
3      memory_accesses_per_instruction_outlier 0.013081
4      time_hour_tan                  0.013076
5      start_time_hour_tan_outlier    0.012782
6      end_time_minute_tan_outlier    0.012768
7      end_time_second_tan            0.012586
8      time_second_tan                0.012578
9      start_time_second_tan          0.012561
10     page_cache_memory_outlier      0.012547

================================================================================

## COMPONENT METRICS: ANOMALY MODEL 1: ISOLATIONFOREST
Metrics for anomaly detection model 1 (IsolationForest)

### parameters
Metric                    Value          
----------------------------------------
bootstrap                 False
contamination             0.500000       
max_features              1.000000       
max_samples               auto
n_estimators              200
n_jobs                    None
random_state              42
verbose                   0
warm_start                False

### score_stats
Metric                    Value          
----------------------------------------
min                       -0.538620      
max                       -0.339975      
mean                      -0.396404      
std                       0.045793       
median                    -0.380964      

### n_estimators

### max_samples


================================================================================

## COMPONENT METRICS: ANOMALY MODEL 2: LOCALOUTLIERFACTOR
Metrics for anomaly detection model 2 (LocalOutlierFactor)

### parameters
Metric                    Value          
----------------------------------------
algorithm                 auto
contamination             0.500000       
leaf_size                 30
metric                    minkowski
metric_params             None
n_jobs                    None
n_neighbors               10
novelty                   True
p                         2

### score_stats
Metric                    Value          
----------------------------------------
min                       -15.867459     
max                       -0.970174      
mean                      -1.735117      
std                       1.668011       
median                    -1.210847      

### n_neighbors_effective

### n_samples


================================================================================

## COMPONENT METRICS: ANOMALY MODEL 3: ONECLASSSVM
Metrics for anomaly detection model 3 (OneClassSVM)

### parameters
Metric                    Value          
----------------------------------------
cache_size                500
coef0                     0.000000       
degree                    3
gamma                     0.002943       
kernel                    rbf
max_iter                  2000
nu                        0.500000       
shrinking                 True
tol                       0.000010       
verbose                   False

### score_stats
Metric                    Value          
----------------------------------------
min                       1.036840       
max                       215.935341     
mean                      155.050005     
std                       42.737112      
median                    166.606034     

### n_support_vectors

### dual_coef_stats
Metric                    Value          
----------------------------------------
min                       0.295280       
max                       1.000000       
mean                      0.992063       


================================================================================

## EXECUTION TIME
Start time: 2025-05-09 08:57:12
End time: 2025-05-09 08:57:55
Total execution time: 42.76 seconds (0.71 minutes)

================================================================================

