# Metrics Log - ANOMALY
# Run ID: 20250509_090613
# Timestamp: 2025-05-09 09:06:13
================================================================================

## RUN CONFIGURATION
data: data\processed\borg_traces_cleaned.csv
task: anomaly
target_cols: ['average_usage_cpu', 'average_usage_memory']
batch_size: 1000
test_size: 0.2
use_gpu: False
use_xgboost: 1
use_lightgbm: 1
use_ensemble: 1
dynamic_components: True
min_components: 2
max_components: 15
learning_rate: 0.002
lr_scheduler_type: one_cycle
lr_warmup_epochs: 8
lr_min: 5e-06
lr_max: 0.01
lr_cycle_epochs: 30
output: results
labeled_anomaly_data: None
augmentation_ratio: 0.75
similarity_threshold: 0.0
show_viz_info: False
visualize_clusters: True
scaling_method: standard
apply_power_transform: False
pre_cluster_dim_reduction: False

================================================================================

## DATASET INFORMATION
Dataset: data\processed\borg_traces_cleaned.csv
Number of rows: 6000
Number of columns: 113

## ANOMALY DETECTION METRICS
### Confusion Matrix
True Negatives (TN): 281
False Positives (FP): 19
False Negatives (FN): 162
True Positives (TP): 738

### Performance Metrics
Accuracy:    0.8492
Precision:   0.9749
Recall:      0.8200
F1 Score:    0.8908
Specificity: 0.9367

### Additional Metrics
roc_auc: 0.9297
pr_auc: 0.9768

================================================================================

## FEATURE IMPORTANCE
Rank   Feature                        Importance
--------------------------------------------------
1      <USER>  <GROUP>.012993
2      instance_index                 0.012912
3      end_time_minute_tan_outlier    0.012498
4      time_hour_tan                  0.012433
5      end_time_hour_tan              0.012353
6      duration_category              0.012136
7      resource_request_cpu_outlier   0.012069
8      start_time_hour_tan_outlier    0.011965
9      page_cache_memory_outlier      0.011946
10     time_second_tan                0.011904

================================================================================

## COMPONENT METRICS: ANOMALY MODEL 1: ISOLATIONFOREST
Metrics for anomaly detection model 1 (IsolationForest)

### parameters
Metric                    Value          
----------------------------------------
bootstrap                 False
contamination             0.500000       
max_features              1.000000       
max_samples               auto
n_estimators              200
n_jobs                    None
random_state              42
verbose                   0
warm_start                False

### score_stats
Metric                    Value          
----------------------------------------
min                       -0.486537      
max                       -0.340075      
mean                      -0.406759      
std                       0.038381       
median                    -0.407504      

### n_estimators

### max_samples


================================================================================

## COMPONENT METRICS: ANOMALY MODEL 2: LOCALOUTLIERFACTOR
Metrics for anomaly detection model 2 (LocalOutlierFactor)

### parameters
Metric                    Value          
----------------------------------------
algorithm                 auto
contamination             0.500000       
leaf_size                 30
metric                    minkowski
metric_params             None
n_jobs                    None
n_neighbors               10
novelty                   True
p                         2

### score_stats
Metric                    Value          
----------------------------------------
min                       -13.701811     
max                       -0.976512      
mean                      -1.794531      
std                       1.334128       
median                    -1.558946      

### n_neighbors_effective

### n_samples


================================================================================

## COMPONENT METRICS: ANOMALY MODEL 3: ONECLASSSVM
Metrics for anomaly detection model 3 (OneClassSVM)

### parameters
Metric                    Value          
----------------------------------------
cache_size                500
coef0                     0.000000       
degree                    3
gamma                     0.002885       
kernel                    rbf
max_iter                  2500
nu                        0.500000       
shrinking                 True
tol                       0.000010       
verbose                   False

### score_stats
Metric                    Value          
----------------------------------------
min                       62.510375      
max                       191.031053     
mean                      122.747343     
std                       34.036895      
median                    119.259085     

### n_support_vectors

### dual_coef_stats
Metric                    Value          
----------------------------------------
min                       0.320937       
max                       1.000000       
mean                      0.996016       


================================================================================

## EXECUTION TIME
Start time: 2025-05-09 09:06:13
End time: 2025-05-09 09:06:56
Total execution time: 43.16 seconds (0.72 minutes)

================================================================================

