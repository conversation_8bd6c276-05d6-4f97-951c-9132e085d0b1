# Metrics Log - ANOMALY
# Run ID: 20250509_091534
# Timestamp: 2025-05-09 09:15:34
================================================================================

## RUN CONFIGURATION
data: data\processed\borg_traces_cleaned.csv
task: anomaly
target_cols: ['average_usage_cpu', 'average_usage_memory']
batch_size: 1000
test_size: 0.2
use_gpu: False
use_xgboost: 1
use_lightgbm: 1
use_ensemble: 1
dynamic_components: True
min_components: 2
max_components: 15
learning_rate: 0.002
lr_scheduler_type: one_cycle
lr_warmup_epochs: 8
lr_min: 5e-06
lr_max: 0.01
lr_cycle_epochs: 30
output: results
labeled_anomaly_data: None
augmentation_ratio: 0.75
similarity_threshold: 0.0
show_viz_info: False
visualize_clusters: True
scaling_method: standard
apply_power_transform: False
pre_cluster_dim_reduction: False

================================================================================

## DATASET INFORMATION
Dataset: data\processed\borg_traces_cleaned.csv
Number of rows: 6000
Number of columns: 113

## ANOMALY DETECTION METRICS
### Confusion Matrix
True Negatives (TN): 289
False Positives (FP): 11
False Negatives (FN): 189
True Positives (TP): 711

### Performance Metrics
Accuracy:    0.8333
Precision:   0.9848
Recall:      0.7900
F1 Score:    0.8767
Specificity: 0.9633

### Additional Metrics
roc_auc: 0.9410
pr_auc: 0.9809

================================================================================

## FEATURE IMPORTANCE
Rank   Feature                        Importance
--------------------------------------------------
1      <USER>  <GROUP>.012932
2      start_time_minute_tan          0.012908
3      page_cache_memory_outlier      0.012318
4      time_second_tan_outlier        0.012231
5      time_weekday_sin               0.012180
6      end_time_hour_tan              0.012177
7      alloc_collection_id            0.012093
8      time_hour_tan_outlier          0.012021
9      start_time_second_sin          0.011997
10     end_time_minute_tan            0.011858

================================================================================

## COMPONENT METRICS: ANOMALY MODEL 1: ISOLATIONFOREST
Metrics for anomaly detection model 1 (IsolationForest)

### parameters
Metric                    Value          
----------------------------------------
bootstrap                 False
contamination             0.500000       
max_features              1.000000       
max_samples               auto
n_estimators              200
n_jobs                    None
random_state              42
verbose                   0
warm_start                False

### score_stats
Metric                    Value          
----------------------------------------
min                       -0.477000      
max                       -0.340444      
mean                      -0.399533      
std                       0.039766       
median                    -0.394369      

### n_estimators

### max_samples


================================================================================

## COMPONENT METRICS: ANOMALY MODEL 2: LOCALOUTLIERFACTOR
Metrics for anomaly detection model 2 (LocalOutlierFactor)

### parameters
Metric                    Value          
----------------------------------------
algorithm                 auto
contamination             0.500000       
leaf_size                 30
metric                    minkowski
metric_params             None
n_jobs                    None
n_neighbors               10
novelty                   True
p                         2

### score_stats
Metric                    Value          
----------------------------------------
min                       -5.064878      
max                       -0.978049      
mean                      -1.617469      
std                       0.612122       
median                    -1.449213      

### n_neighbors_effective

### n_samples


================================================================================

## COMPONENT METRICS: ANOMALY MODEL 3: ONECLASSSVM
Metrics for anomaly detection model 3 (OneClassSVM)

### parameters
Metric                    Value          
----------------------------------------
cache_size                500
coef0                     0.000000       
degree                    3
gamma                     0.004754       
kernel                    rbf
max_iter                  2500
nu                        0.500000       
shrinking                 True
tol                       0.000010       
verbose                   False

### score_stats
Metric                    Value          
----------------------------------------
min                       18.224738      
max                       104.739805     
mean                      57.937268      
std                       24.830536      
median                    58.796978      

### n_support_vectors

### dual_coef_stats
Metric                    Value          
----------------------------------------
min                       0.146990       
max                       1.000000       
mean                      0.990099       


================================================================================

## EXECUTION TIME
Start time: 2025-05-09 09:15:34
End time: 2025-05-09 09:16:20
Total execution time: 45.55 seconds (0.76 minutes)

================================================================================

