# Metrics Log - ANOMALY
# Run ID: 20250509_092315
# Timestamp: 2025-05-09 09:23:15
================================================================================

## RUN CONFIGURATION
data: data\processed\borg_traces_cleaned.csv
task: anomaly
target_cols: ['average_usage_cpu', 'average_usage_memory']
batch_size: 1000
test_size: 0.2
use_gpu: False
use_xgboost: 1
use_lightgbm: 1
use_ensemble: 1
dynamic_components: True
min_components: 2
max_components: 15
learning_rate: 0.002
lr_scheduler_type: one_cycle
lr_warmup_epochs: 8
lr_min: 5e-06
lr_max: 0.01
lr_cycle_epochs: 30
output: results
labeled_anomaly_data: None
augmentation_ratio: 0.75
similarity_threshold: 0.0
show_viz_info: False
visualize_clusters: True
scaling_method: standard
apply_power_transform: False
pre_cluster_dim_reduction: False

================================================================================

## DATASET INFORMATION
Dataset: data\processed\borg_traces_cleaned.csv
Number of rows: 6000
Number of columns: 113

## ANOMALY DETECTION METRICS
### Confusion Matrix
True Negatives (TN): 292
False Positives (FP): 8
False Negatives (FN): 302
True Positives (TP): 598

### Performance Metrics
Accuracy:    0.7417
Precision:   0.9868
Recall:      0.6644
F1 Score:    0.7942
Specificity: 0.9733

### Additional Metrics
roc_auc: 0.9331
pr_auc: 0.9661

================================================================================

## FEATURE IMPORTANCE
Rank   Feature                        Importance
--------------------------------------------------
1      <USER>  <GROUP>.012991
2      end_time_minute_tan_outlier    0.012419
3      time_hour_tan                  0.012401
4      end_time_second_sin            0.012278
5      duration_category              0.012227
6      machine_id                     0.012188
7      page_cache_memory_outlier      0.012178
8      end_time_second                0.012159
9      time_second_tan_outlier        0.012026
10     instance_index_outlier         0.011709

================================================================================

## COMPONENT METRICS: ANOMALY MODEL 3: ONECLASSSVM
Metrics for anomaly detection model 3 (OneClassSVM)

### parameters
Metric                    Value          
----------------------------------------
cache_size                500
coef0                     0.000000       
degree                    3
gamma                     0.004792       
kernel                    rbf
max_iter                  2500
nu                        0.500000       
shrinking                 True
tol                       0.000010       
verbose                   False

### score_stats
Metric                    Value          
----------------------------------------
min                       0.703744       
max                       102.287729     
mean                      55.195438      
std                       25.356613      
median                    52.261277      

### n_support_vectors

### dual_coef_stats
Metric                    Value          
----------------------------------------
min                       0.004119       
max                       1.000000       
mean                      0.984252       


================================================================================

## EXECUTION TIME
Start time: 2025-05-09 09:23:15
End time: 2025-05-09 09:23:56
Total execution time: 41.55 seconds (0.69 minutes)

================================================================================

