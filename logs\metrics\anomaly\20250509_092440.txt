# Metrics Log - ANOMALY
# Run ID: 20250509_092440
# Timestamp: 2025-05-09 09:24:40
================================================================================

## RUN CONFIGURATION
data: data\processed\borg_traces_cleaned.csv
task: anomaly
target_cols: ['average_usage_cpu', 'average_usage_memory']
batch_size: 1000
test_size: 0.2
use_gpu: False
use_xgboost: 1
use_lightgbm: 1
use_ensemble: 1
dynamic_components: True
min_components: 2
max_components: 15
learning_rate: 0.002
lr_scheduler_type: one_cycle
lr_warmup_epochs: 8
lr_min: 5e-06
lr_max: 0.01
lr_cycle_epochs: 30
output: results
labeled_anomaly_data: None
augmentation_ratio: 0.75
similarity_threshold: 0.0
show_viz_info: False
visualize_clusters: True
scaling_method: standard
apply_power_transform: False
pre_cluster_dim_reduction: False

================================================================================

## DATASET INFORMATION
Dataset: data\processed\borg_traces_cleaned.csv
Number of rows: 6000
Number of columns: 113

## ANOMALY DETECTION METRICS
### Confusion Matrix
True Negatives (TN): 287
False Positives (FP): 13
False Negatives (FN): 197
True Positives (TP): 703

### Performance Metrics
Accuracy:    0.8250
Precision:   0.9818
Recall:      0.7811
F1 Score:    0.8700
Specificity: 0.9567

### Additional Metrics
roc_auc: 0.9257
pr_auc: 0.9753

================================================================================

## FEATURE IMPORTANCE
Rank   Feature                        Importance
--------------------------------------------------
1      <USER>                 <GROUP>.013754
2      time_second_tan_outlier        0.013620
3      instance_index_outlier         0.013463
4      time_hour_tan                  0.013292
5      machine_id                     0.012902
6      end_time_second_tan            0.012642
7      page_cache_memory_outlier      0.012435
8      collection_type                0.012121
9      end_time_second_sin            0.011895
10     start_time_hour_tan            0.011817

================================================================================

## COMPONENT METRICS: ANOMALY MODEL 1: ISOLATIONFOREST
Metrics for anomaly detection model 1 (IsolationForest)

### parameters
Metric                    Value          
----------------------------------------
bootstrap                 False
contamination             0.500000       
max_features              1.000000       
max_samples               auto
n_estimators              200
n_jobs                    None
random_state              42
verbose                   0
warm_start                False

### score_stats
Metric                    Value          
----------------------------------------
min                       -0.508578      
max                       -0.338272      
mean                      -0.406861      
std                       0.044262       
median                    -0.395108      

### n_estimators

### max_samples


================================================================================

## COMPONENT METRICS: ANOMALY MODEL 2: LOCALOUTLIERFACTOR
Metrics for anomaly detection model 2 (LocalOutlierFactor)

### parameters
Metric                    Value          
----------------------------------------
algorithm                 auto
contamination             0.500000       
leaf_size                 30
metric                    minkowski
metric_params             None
n_jobs                    None
n_neighbors               5
novelty                   True
p                         2

### score_stats
Metric                    Value          
----------------------------------------
min                       -6.276394      
max                       -0.958787      
mean                      -1.864220      
std                       0.876198       
median                    -1.696714      

### n_neighbors_effective

### n_samples


================================================================================

## COMPONENT METRICS: ANOMALY MODEL 3: ONECLASSSVM
Metrics for anomaly detection model 3 (OneClassSVM)

### parameters
Metric                    Value          
----------------------------------------
cache_size                500
coef0                     0.000000       
degree                    3
gamma                     0.004820       
kernel                    rbf
max_iter                  2500
nu                        0.500000       
shrinking                 True
tol                       0.000010       
verbose                   False

### score_stats
Metric                    Value          
----------------------------------------
min                       13.940750      
max                       107.378973     
mean                      53.781827      
std                       25.358556      
median                    53.828575      

### n_support_vectors

### dual_coef_stats
Metric                    Value          
----------------------------------------
min                       0.027359       
max                       1.000000       
mean                      0.986193       


================================================================================

## EXECUTION TIME
Start time: 2025-05-09 09:24:40
End time: 2025-05-09 09:25:32
Total execution time: 51.95 seconds (0.87 minutes)

================================================================================

