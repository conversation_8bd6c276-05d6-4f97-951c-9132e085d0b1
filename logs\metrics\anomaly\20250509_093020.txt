# Metrics Log - ANOMALY
# Run ID: 20250509_093020
# Timestamp: 2025-05-09 09:30:20
================================================================================

## RUN CONFIGURATION
data: data\processed\borg_traces_cleaned.csv
task: anomaly
target_cols: ['average_usage_cpu', 'average_usage_memory']
batch_size: 1000
test_size: 0.2
use_gpu: False
use_xgboost: 1
use_lightgbm: 1
use_ensemble: 1
dynamic_components: True
min_components: 2
max_components: 15
learning_rate: 0.002
lr_scheduler_type: one_cycle
lr_warmup_epochs: 8
lr_min: 5e-06
lr_max: 0.01
lr_cycle_epochs: 30
output: results
labeled_anomaly_data: None
augmentation_ratio: 0.5
similarity_threshold: 0.0
show_viz_info: False
visualize_clusters: True
scaling_method: standard
apply_power_transform: False
pre_cluster_dim_reduction: False

================================================================================

## DATASET INFORMATION
Dataset: data\processed\borg_traces_cleaned.csv
Number of rows: 6000
Number of columns: 113

## ANOMALY DETECTION METRICS
### Confusion Matrix
True Negatives (TN): 451
False Positives (FP): 149
False Negatives (FN): 20
True Positives (TP): 580

### Performance Metrics
Accuracy:    0.8592
Precision:   0.7956
Recall:      0.9667
F1 Score:    0.8728
Specificity: 0.7517

### Additional Metrics
roc_auc: 0.9411
pr_auc: 0.9509

================================================================================

## FEATURE IMPORTANCE
Rank   Feature                        Importance
--------------------------------------------------
1      <USER>            <GROUP>.013755
2      end_time_hour_tan              0.013400
3      memory_accesses_per_instruction_outlier 0.013175
4      start_time_second_sin          0.012973
5      end_time_hour_tan_outlier      0.012728
6      start_time_second_tan          0.012725
7      end_time_minute_tan_outlier    0.012659
8      duration_category              0.012369
9      instance_index                 0.012188
10     instance_index_outlier         0.012168

================================================================================

## COMPONENT METRICS: ANOMALY MODEL 1: ISOLATIONFOREST
Metrics for anomaly detection model 1 (IsolationForest)

### parameters
Metric                    Value          
----------------------------------------
bootstrap                 False
contamination             0.500000       
max_features              1.000000       
max_samples               auto
n_estimators              200
n_jobs                    None
random_state              42
verbose                   0
warm_start                False

### score_stats
Metric                    Value          
----------------------------------------
min                       -0.503815      
max                       -0.338427      
mean                      -0.398190      
std                       0.045278       
median                    -0.381867      

### n_estimators

### max_samples


================================================================================

## COMPONENT METRICS: ANOMALY MODEL 2: LOCALOUTLIERFACTOR
Metrics for anomaly detection model 2 (LocalOutlierFactor)

### parameters
Metric                    Value          
----------------------------------------
algorithm                 auto
contamination             0.500000       
leaf_size                 30
metric                    minkowski
metric_params             None
n_jobs                    None
n_neighbors               5
novelty                   True
p                         2

### score_stats
Metric                    Value          
----------------------------------------
min                       -14.735134     
max                       -0.939209      
mean                      -1.830587      
std                       1.569084       
median                    -1.225414      

### n_neighbors_effective

### n_samples


================================================================================

## COMPONENT METRICS: ANOMALY MODEL 3: ONECLASSSVM
Metrics for anomaly detection model 3 (OneClassSVM)

### parameters
Metric                    Value          
----------------------------------------
cache_size                500
coef0                     0.000000       
degree                    3
gamma                     0.005641       
kernel                    rbf
max_iter                  2500
nu                        0.500000       
shrinking                 True
tol                       0.000010       
verbose                   False

### score_stats
Metric                    Value          
----------------------------------------
min                       0.540110       
max                       96.130395      
mean                      57.042161      
std                       25.929858      
median                    64.090533      

### n_support_vectors

### dual_coef_stats
Metric                    Value          
----------------------------------------
min                       0.059830       
max                       1.000000       
mean                      0.990099       


================================================================================

## EXECUTION TIME
Start time: 2025-05-09 09:30:20
End time: 2025-05-09 09:31:01
Total execution time: 41.62 seconds (0.69 minutes)

================================================================================

