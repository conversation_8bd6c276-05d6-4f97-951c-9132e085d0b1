# Metrics Log - ANOMALY
# Run ID: 20250509_093430
# Timestamp: 2025-05-09 09:34:30
================================================================================

## RUN CONFIGURATION
data: data\processed\borg_traces_cleaned.csv
task: anomaly
target_cols: ['average_usage_cpu', 'average_usage_memory']
batch_size: 1000
test_size: 0.2
use_gpu: False
use_xgboost: 1
use_lightgbm: 1
use_ensemble: 1
dynamic_components: True
min_components: 2
max_components: 15
learning_rate: 0.002
lr_scheduler_type: one_cycle
lr_warmup_epochs: 8
lr_min: 5e-06
lr_max: 0.01
lr_cycle_epochs: 30
output: results
labeled_anomaly_data: None
augmentation_ratio: 0.75
similarity_threshold: 0.0
show_viz_info: False
visualize_clusters: True
scaling_method: standard
apply_power_transform: False
pre_cluster_dim_reduction: False

================================================================================

## DATASET INFORMATION
Dataset: data\processed\borg_traces_cleaned.csv
Number of rows: 6000
Number of columns: 113

## ANOMALY DETECTION METRICS
### Confusion Matrix
True Negatives (TN): 300
False Positives (FP): 0
False Negatives (FN): 701
True Positives (TP): 199

### Performance Metrics
Accuracy:    0.4158
Precision:   1.0000
Recall:      0.2211
F1 Score:    0.3621
Specificity: 1.0000

### Additional Metrics
roc_auc: 0.9321
pr_auc: 0.9782

================================================================================

## FEATURE IMPORTANCE
Rank   Feature                        Importance
--------------------------------------------------
1      <USER>                 <GROUP>.013368
2      time_hour_tan                  0.013325
3      resource_request_cpu_outlier   0.012188
4      instance_index_outlier         0.012135
5      start_time_minute_tan_outlier  0.012043
6      start_time_second              0.011935
7      time_hour_tan_outlier          0.011725
8      resource_request_memory_outlier 0.011515
9      start_time_minute_tan          0.011377
10     end_time_minute_tan_outlier    0.011374

================================================================================

## COMPONENT METRICS: ANOMALY MODEL 1: ISOLATIONFOREST
Metrics for anomaly detection model 1 (IsolationForest)

### parameters
Metric                    Value          
----------------------------------------
bootstrap                 False
contamination             0.100000       
max_features              1.000000       
max_samples               auto
n_estimators              200
n_jobs                    None
random_state              42
verbose                   0
warm_start                False

### score_stats
Metric                    Value          
----------------------------------------
min                       -0.513404      
max                       -0.342554      
mean                      -0.404261      
std                       0.041108       
median                    -0.396198      

### n_estimators

### max_samples


================================================================================

## COMPONENT METRICS: ANOMALY MODEL 2: LOCALOUTLIERFACTOR
Metrics for anomaly detection model 2 (LocalOutlierFactor)

### parameters
Metric                    Value          
----------------------------------------
algorithm                 auto
contamination             0.100000       
leaf_size                 30
metric                    minkowski
metric_params             None
n_jobs                    None
n_neighbors               20
novelty                   True
p                         2

### score_stats
Metric                    Value          
----------------------------------------
min                       -2.689869      
max                       -0.976017      
mean                      -1.590987      
std                       0.518327       
median                    -1.443856      

### n_neighbors_effective

### n_samples


================================================================================

## COMPONENT METRICS: ANOMALY MODEL 3: ONECLASSSVM
Metrics for anomaly detection model 3 (OneClassSVM)

### parameters
Metric                    Value          
----------------------------------------
cache_size                500
coef0                     0.000000       
degree                    3
gamma                     0.009454       
kernel                    rbf
max_iter                  2500
nu                        0.500000       
shrinking                 True
tol                       0.000010       
verbose                   False

### score_stats
Metric                    Value          
----------------------------------------
min                       0.006436       
max                       25.646108      
mean                      9.162743       
std                       6.877710       
median                    7.758993       

### n_support_vectors

### dual_coef_stats
Metric                    Value          
----------------------------------------
min                       0.022097       
max                       1.000000       
mean                      0.956023       


================================================================================

## EXECUTION TIME
Start time: 2025-05-09 09:34:30
End time: 2025-05-09 09:35:13
Total execution time: 43.68 seconds (0.73 minutes)

================================================================================

