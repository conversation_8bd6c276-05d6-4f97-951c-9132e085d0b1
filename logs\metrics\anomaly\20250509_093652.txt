# Metrics Log - ANOMALY
# Run ID: 20250509_093652
# Timestamp: 2025-05-09 09:36:52
================================================================================

## RUN CONFIGURATION
data: data\processed\borg_traces_cleaned.csv
task: anomaly
target_cols: ['average_usage_cpu', 'average_usage_memory']
batch_size: 1000
test_size: 0.2
use_gpu: False
use_xgboost: 1
use_lightgbm: 1
use_ensemble: 1
dynamic_components: True
min_components: 2
max_components: 15
learning_rate: 0.002
lr_scheduler_type: one_cycle
lr_warmup_epochs: 8
lr_min: 5e-06
lr_max: 0.01
lr_cycle_epochs: 30
output: results
labeled_anomaly_data: None
augmentation_ratio: 0.75
similarity_threshold: 0.0
show_viz_info: False
visualize_clusters: True
scaling_method: standard
apply_power_transform: False
pre_cluster_dim_reduction: False

================================================================================

## DATASET INFORMATION
Dataset: data\processed\borg_traces_cleaned.csv
Number of rows: 6000
Number of columns: 113

## ANOMALY DETECTION METRICS
### Confusion Matrix
True Negatives (TN): 292
False Positives (FP): 8
False Negatives (FN): 278
True Positives (TP): 622

### Performance Metrics
Accuracy:    0.7617
Precision:   0.9873
Recall:      0.6911
F1 Score:    0.8131
Specificity: 0.9733

### Additional Metrics
roc_auc: 0.9345
pr_auc: 0.9783

================================================================================

## FEATURE IMPORTANCE
Rank   Feature                        Importance
--------------------------------------------------
1      <USER> <GROUP>.012483
2      start_time_hour_tan_outlier    0.012178
3      start_time_minute_tan_outlier  0.012151
4      time_hour_tan                  0.012147
5      page_cache_memory_outlier      0.012121
6      end_time_minute_tan_outlier    0.011986
7      resource_request_cpu           0.011840
8      alloc_collection_id            0.011686
9      cpu_usage_distribution_median_outlier 0.011611
10     maximum_usage_memory_outlier   0.011565

================================================================================

## COMPONENT METRICS: ANOMALY MODEL 1: ISOLATIONFOREST
Metrics for anomaly detection model 1 (IsolationForest)

### parameters
Metric                    Value          
----------------------------------------
bootstrap                 False
contamination             0.300000       
max_features              1.000000       
max_samples               auto
n_estimators              200
n_jobs                    None
random_state              42
verbose                   0
warm_start                False

### score_stats
Metric                    Value          
----------------------------------------
min                       -0.491917      
max                       -0.344283      
mean                      -0.404156      
std                       0.040193       
median                    -0.393864      

### n_estimators

### max_samples


================================================================================

## COMPONENT METRICS: ANOMALY MODEL 2: LOCALOUTLIERFACTOR
Metrics for anomaly detection model 2 (LocalOutlierFactor)

### parameters
Metric                    Value          
----------------------------------------
algorithm                 auto
contamination             0.300000       
leaf_size                 30
metric                    minkowski
metric_params             None
n_jobs                    None
n_neighbors               10
novelty                   True
p                         2

### score_stats
Metric                    Value          
----------------------------------------
min                       -3.257193      
max                       -0.963774      
mean                      -1.627195      
std                       0.559027       
median                    -1.523507      

### n_neighbors_effective

### n_samples


================================================================================

## COMPONENT METRICS: ANOMALY MODEL 3: ONECLASSSVM
Metrics for anomaly detection model 3 (OneClassSVM)

### parameters
Metric                    Value          
----------------------------------------
cache_size                500
coef0                     0.000000       
degree                    3
gamma                     0.003800       
kernel                    rbf
max_iter                  2500
nu                        0.500000       
shrinking                 True
tol                       0.000010       
verbose                   False

### score_stats
Metric                    Value          
----------------------------------------
min                       33.471718      
max                       137.155299     
mean                      83.524328      
std                       28.787765      
median                    87.373350      

### n_support_vectors

### dual_coef_stats
Metric                    Value          
----------------------------------------
min                       0.084208       
max                       1.000000       
mean                      0.986193       


================================================================================

## EXECUTION TIME
Start time: 2025-05-09 09:36:52
End time: 2025-05-09 09:37:38
Total execution time: 45.57 seconds (0.76 minutes)

================================================================================

