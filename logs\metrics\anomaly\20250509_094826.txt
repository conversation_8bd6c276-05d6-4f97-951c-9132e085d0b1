# Metrics Log - ANOMALY
# Run ID: 20250509_094826
# Timestamp: 2025-05-09 09:48:26
================================================================================

## RUN CONFIGURATION
data: data\processed\borg_traces_cleaned.csv
task: anomaly
target_cols: ['average_usage_cpu', 'average_usage_memory']
batch_size: 1000
test_size: 0.2
use_gpu: False
use_xgboost: 1
use_lightgbm: 1
use_ensemble: 1
dynamic_components: True
min_components: 2
max_components: 15
learning_rate: 0.002
lr_scheduler_type: one_cycle
lr_warmup_epochs: 8
lr_min: 5e-06
lr_max: 0.01
lr_cycle_epochs: 30
output: results
labeled_anomaly_data: None
augmentation_ratio: 0.75
similarity_threshold: 0.0
show_viz_info: False
visualize_clusters: True
scaling_method: standard
apply_power_transform: False
pre_cluster_dim_reduction: False

================================================================================

## DATASET INFORMATION
Dataset: data\processed\borg_traces_cleaned.csv
Number of rows: 6000
Number of columns: 113

## ANOMALY DETECTION METRICS
### Confusion Matrix
True Negatives (TN): 560
False Positives (FP): 340
False Negatives (FN): 4
True Positives (TP): 296

### Performance Metrics
Accuracy:    0.7133
Precision:   0.4654
Recall:      0.9867
F1 Score:    0.6325
Specificity: 0.6222

### Additional Metrics
roc_auc: 0.9396
pr_auc: 0.8808

================================================================================

## FEATURE IMPORTANCE
Rank   Feature                        Importance
--------------------------------------------------
1      <USER>  <GROUP>.015507
2      instance_index                 0.014168
3      start_time_minute_tan          0.013725
4      time_hour_tan_outlier          0.013352
5      start_time_second_sin          0.013300
6      memory_accesses_per_instruction_outlier 0.013155
7      duration_category              0.013040
8      time_weekday_tan               0.012829
9      resource_request_cpu_outlier   0.012569
10     collection_type                0.012539

================================================================================

## COMPONENT METRICS: ANOMALY MODEL 1: ISOLATIONFOREST
Metrics for anomaly detection model 1 (IsolationForest)

### parameters
Metric                    Value          
----------------------------------------
bootstrap                 False
contamination             0.300000       
max_features              1.000000       
max_samples               auto
n_estimators              200
n_jobs                    None
random_state              42
verbose                   0
warm_start                False

### score_stats
Metric                    Value          
----------------------------------------
min                       -0.522099      
max                       -0.344698      
mean                      -0.390215      
std                       0.044864       
median                    -0.375593      

### n_estimators

### max_samples


================================================================================

## COMPONENT METRICS: ANOMALY MODEL 2: LOCALOUTLIERFACTOR
Metrics for anomaly detection model 2 (LocalOutlierFactor)

### parameters
Metric                    Value          
----------------------------------------
algorithm                 auto
contamination             0.300000       
leaf_size                 30
metric                    minkowski
metric_params             None
n_jobs                    None
n_neighbors               10
novelty                   True
p                         2

### score_stats
Metric                    Value          
----------------------------------------
min                       -14.265358     
max                       -0.969246      
mean                      -1.471823      
std                       1.391585       
median                    -1.105172      

### n_neighbors_effective

### n_samples


================================================================================

## COMPONENT METRICS: ANOMALY MODEL 3: ONECLASSSVM
Metrics for anomaly detection model 3 (OneClassSVM)

### parameters
Metric                    Value          
----------------------------------------
cache_size                500
coef0                     0.000000       
degree                    3
gamma                     0.005822       
kernel                    rbf
max_iter                  2500
nu                        0.500000       
shrinking                 True
tol                       0.000010       
verbose                   False

### score_stats
Metric                    Value          
----------------------------------------
min                       0.327542       
max                       129.389740     
mean                      92.743332      
std                       31.203048      
median                    103.470436     

### n_support_vectors

### dual_coef_stats
Metric                    Value          
----------------------------------------
min                       0.104826       
max                       1.000000       
mean                      0.990099       


================================================================================

## EXECUTION TIME
Start time: 2025-05-09 09:48:26
End time: 2025-05-09 09:49:07
Total execution time: 41.15 seconds (0.69 minutes)

================================================================================

