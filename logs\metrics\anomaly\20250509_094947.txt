# Metrics Log - ANOMALY
# Run ID: 20250509_094947
# Timestamp: 2025-05-09 09:49:47
================================================================================

## RUN CONFIGURATION
data: data\processed\borg_traces_cleaned.csv
task: anomaly
target_cols: ['average_usage_cpu', 'average_usage_memory']
batch_size: 1000
test_size: 0.2
use_gpu: False
use_xgboost: 1
use_lightgbm: 1
use_ensemble: 1
dynamic_components: True
min_components: 2
max_components: 15
learning_rate: 0.002
lr_scheduler_type: one_cycle
lr_warmup_epochs: 8
lr_min: 5e-06
lr_max: 0.01
lr_cycle_epochs: 30
output: results
labeled_anomaly_data: None
augmentation_ratio: 0.75
similarity_threshold: 0.0
show_viz_info: False
visualize_clusters: True
scaling_method: standard
apply_power_transform: False
pre_cluster_dim_reduction: False

================================================================================

## DATASET INFORMATION
Dataset: data\processed\borg_traces_cleaned.csv
Number of rows: 6000
Number of columns: 113

## ANOMALY DETECTION METRICS
### Confusion Matrix
True Negatives (TN): 600
False Positives (FP): 300
False Negatives (FN): 11
True Positives (TP): 289

### Performance Metrics
Accuracy:    0.7408
Precision:   0.4907
Recall:      0.9633
F1 Score:    0.6502
Specificity: 0.6667

### Additional Metrics
roc_auc: 0.9376
pr_auc: 0.8964

================================================================================

## FEATURE IMPORTANCE
Rank   Feature                        Importance
--------------------------------------------------
1      <USER>    <GROUP>.016230
2      start_time_second_tan          0.013697
3      start_time_minute_tan          0.013649
4      machine_id                     0.013456
5      start_time_hour_tan            0.013353
6      page_cache_memory_outlier      0.013343
7      instance_index                 0.013311
8      end_time_second_sin            0.013309
9      end_time_minute_tan            0.013229
10     start_time_hour_tan_outlier    0.013098

================================================================================

## COMPONENT METRICS: ANOMALY MODEL 1: ISOLATIONFOREST
Metrics for anomaly detection model 1 (IsolationForest)

### parameters
Metric                    Value          
----------------------------------------
bootstrap                 False
contamination             0.100000       
max_features              1.000000       
max_samples               auto
n_estimators              200
n_jobs                    None
random_state              42
verbose                   0
warm_start                False

### score_stats
Metric                    Value          
----------------------------------------
min                       -0.527177      
max                       -0.345278      
mean                      -0.393339      
std                       0.044801       
median                    -0.377225      

### n_estimators

### max_samples


================================================================================

## COMPONENT METRICS: ANOMALY MODEL 2: LOCALOUTLIERFACTOR
Metrics for anomaly detection model 2 (LocalOutlierFactor)

### parameters
Metric                    Value          
----------------------------------------
algorithm                 auto
contamination             0.100000       
leaf_size                 30
metric                    minkowski
metric_params             None
n_jobs                    None
n_neighbors               10
novelty                   True
p                         2

### score_stats
Metric                    Value          
----------------------------------------
min                       -3.275812      
max                       -0.968036      
mean                      -1.308140      
std                       0.532037       
median                    -1.053354      

### n_neighbors_effective

### n_samples


================================================================================

## COMPONENT METRICS: ANOMALY MODEL 3: ONECLASSSVM
Metrics for anomaly detection model 3 (OneClassSVM)

### parameters
Metric                    Value          
----------------------------------------
cache_size                500
coef0                     0.000000       
degree                    3
gamma                     0.005807       
kernel                    rbf
max_iter                  2500
nu                        0.500000       
shrinking                 True
tol                       0.000010       
verbose                   False

### score_stats
Metric                    Value          
----------------------------------------
min                       0.849461       
max                       137.307212     
mean                      96.823143      
std                       33.910217      
median                    107.769982     

### n_support_vectors

### dual_coef_stats
Metric                    Value          
----------------------------------------
min                       0.099003       
max                       1.000000       
mean                      0.992063       


================================================================================

## EXECUTION TIME
Start time: 2025-05-09 09:49:47
End time: 2025-05-09 09:50:29
Total execution time: 41.79 seconds (0.70 minutes)

================================================================================

