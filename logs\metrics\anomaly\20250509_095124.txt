# Metrics Log - ANOMALY
# Run ID: 20250509_095124
# Timestamp: 2025-05-09 09:51:24
================================================================================

## RUN CONFIGURATION
data: data\processed\borg_traces_cleaned.csv
task: anomaly
target_cols: ['average_usage_cpu', 'average_usage_memory']
batch_size: 1000
test_size: 0.2
use_gpu: False
use_xgboost: 1
use_lightgbm: 1
use_ensemble: 1
dynamic_components: True
min_components: 2
max_components: 15
learning_rate: 0.002
lr_scheduler_type: one_cycle
lr_warmup_epochs: 8
lr_min: 5e-06
lr_max: 0.01
lr_cycle_epochs: 30
output: results
labeled_anomaly_data: None
augmentation_ratio: 0.75
similarity_threshold: 0.0
show_viz_info: False
visualize_clusters: True
scaling_method: standard
apply_power_transform: False
pre_cluster_dim_reduction: False

================================================================================

## DATASET INFORMATION
Dataset: data\processed\borg_traces_cleaned.csv
Number of rows: 6000
Number of columns: 113

## ANOMALY DETECTION METRICS
### Confusion Matrix
True Negatives (TN): 897
False Positives (FP): 3
False Negatives (FN): 128
True Positives (TP): 172

### Performance Metrics
Accuracy:    0.8908
Precision:   0.9829
Recall:      0.5733
F1 Score:    0.7242
Specificity: 0.9967

### Additional Metrics
roc_auc: 0.9152
pr_auc: 0.8622

================================================================================

## FEATURE IMPORTANCE
Rank   Feature                        Importance
--------------------------------------------------
1      <USER>        <GROUP>.016880
2      instance_index_outlier         0.014691
3      duration_category              0.014185
4      instance_index                 0.014027
5      start_time_second_sin          0.013485
6      start_time_hour_tan_outlier    0.013377
7      time_hour_tan                  0.013322
8      start_time_second_tan          0.013131
9      end_time_hour_tan              0.013075
10     time_minute_tan_outlier        0.013053

================================================================================

## COMPONENT METRICS: ANOMALY MODEL 1: ISOLATIONFOREST
Metrics for anomaly detection model 1 (IsolationForest)

### parameters
Metric                    Value          
----------------------------------------
bootstrap                 False
contamination             0.100000       
max_features              1.000000       
max_samples               auto
n_estimators              200
n_jobs                    None
random_state              42
verbose                   0
warm_start                False

### score_stats
Metric                    Value          
----------------------------------------
min                       -0.561373      
max                       -0.344722      
mean                      -0.393621      
std                       0.044549       
median                    -0.378615      

### n_estimators

### max_samples


================================================================================

## COMPONENT METRICS: ANOMALY MODEL 2: LOCALOUTLIERFACTOR
Metrics for anomaly detection model 2 (LocalOutlierFactor)

### parameters
Metric                    Value          
----------------------------------------
algorithm                 auto
contamination             0.100000       
leaf_size                 30
metric                    minkowski
metric_params             None
n_jobs                    None
n_neighbors               10
novelty                   True
p                         2

### score_stats
Metric                    Value          
----------------------------------------
min                       -8.982379      
max                       -0.969662      
mean                      -1.398197      
std                       0.953864       
median                    -1.063878      

### n_neighbors_effective

### n_samples


================================================================================

## COMPONENT METRICS: ANOMALY MODEL 3: ONECLASSSVM
Metrics for anomaly detection model 3 (OneClassSVM)

### parameters
Metric                    Value          
----------------------------------------
cache_size                500
coef0                     0.000000       
degree                    3
gamma                     0.005911       
kernel                    rbf
max_iter                  2500
nu                        0.500000       
shrinking                 True
tol                       0.000010       
verbose                   False

### score_stats
Metric                    Value          
----------------------------------------
min                       10.977576      
max                       129.870066     
mean                      93.668994      
std                       30.614346      
median                    103.896747     

### n_support_vectors

### dual_coef_stats
Metric                    Value          
----------------------------------------
min                       0.150472       
max                       1.000000       
mean                      0.994036       


================================================================================

## EXECUTION TIME
Start time: 2025-05-09 09:51:24
End time: 2025-05-09 09:52:05
Total execution time: 41.17 seconds (0.69 minutes)

================================================================================

