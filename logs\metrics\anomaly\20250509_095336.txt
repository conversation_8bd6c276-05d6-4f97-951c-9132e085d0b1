# Metrics Log - ANOMALY
# Run ID: 20250509_095336
# Timestamp: 2025-05-09 09:53:36
================================================================================

## RUN CONFIGURATION
data: data\processed\borg_traces_cleaned.csv
task: anomaly
target_cols: ['average_usage_cpu', 'average_usage_memory']
batch_size: 1000
test_size: 0.2
use_gpu: False
use_xgboost: 1
use_lightgbm: 1
use_ensemble: 1
dynamic_components: True
min_components: 2
max_components: 15
learning_rate: 0.002
lr_scheduler_type: one_cycle
lr_warmup_epochs: 8
lr_min: 5e-06
lr_max: 0.01
lr_cycle_epochs: 30
output: results
labeled_anomaly_data: None
augmentation_ratio: 0.5
similarity_threshold: 0.0
show_viz_info: False
visualize_clusters: True
scaling_method: standard
apply_power_transform: False
pre_cluster_dim_reduction: False

================================================================================

## DATASET INFORMATION
Dataset: data\processed\borg_traces_cleaned.csv
Number of rows: 6000
Number of columns: 113

## ANOMALY DETECTION METRICS
### Confusion Matrix
True Negatives (TN): 897
False Positives (FP): 3
False Negatives (FN): 145
True Positives (TP): 155

### Performance Metrics
Accuracy:    0.8767
Precision:   0.9810
Recall:      0.5167
F1 Score:    0.6769
Specificity: 0.9967

### Additional Metrics
roc_auc: 0.9169
pr_auc: 0.8603

================================================================================

## FEATURE IMPORTANCE
Rank   Feature                        Importance
--------------------------------------------------
1      <USER>                  <GROUP>.015881
2      duration_category              0.013848
3      end_time_second_sin            0.013815
4      time_hour_tan_outlier          0.012965
5      start_time_second_tan          0.012879
6      instance_index                 0.012837
7      collection_type                0.012835
8      start_time_hour_tan_outlier    0.012818
9      start_time_hour_tan            0.012665
10     start_time_second_sin          0.012603

================================================================================

## COMPONENT METRICS: ANOMALY MODEL 1: ISOLATIONFOREST
Metrics for anomaly detection model 1 (IsolationForest)

### parameters
Metric                    Value          
----------------------------------------
bootstrap                 False
contamination             0.100000       
max_features              1.000000       
max_samples               auto
n_estimators              200
n_jobs                    None
random_state              42
verbose                   0
warm_start                False

### score_stats
Metric                    Value          
----------------------------------------
min                       -0.528816      
max                       -0.344307      
mean                      -0.392188      
std                       0.042946       
median                    -0.377885      

### n_estimators

### max_samples


================================================================================

## COMPONENT METRICS: ANOMALY MODEL 2: LOCALOUTLIERFACTOR
Metrics for anomaly detection model 2 (LocalOutlierFactor)

### parameters
Metric                    Value          
----------------------------------------
algorithm                 auto
contamination             0.100000       
leaf_size                 30
metric                    minkowski
metric_params             None
n_jobs                    None
n_neighbors               10
novelty                   True
p                         2

### score_stats
Metric                    Value          
----------------------------------------
min                       -3.358233      
max                       -0.980969      
mean                      -1.346791      
std                       0.543573       
median                    -1.089866      

### n_neighbors_effective

### n_samples


================================================================================

## COMPONENT METRICS: ANOMALY MODEL 3: ONECLASSSVM
Metrics for anomaly detection model 3 (OneClassSVM)

### parameters
Metric                    Value          
----------------------------------------
cache_size                500
coef0                     0.000000       
degree                    3
gamma                     0.005778       
kernel                    rbf
max_iter                  2500
nu                        0.500000       
shrinking                 True
tol                       0.000010       
verbose                   False

### score_stats
Metric                    Value          
----------------------------------------
min                       3.477949       
max                       138.985267     
mean                      96.946000      
std                       32.594951      
median                    107.990788     

### n_support_vectors

### dual_coef_stats
Metric                    Value          
----------------------------------------
min                       0.122730       
max                       1.000000       
mean                      0.994036       


================================================================================

## EXECUTION TIME
Start time: 2025-05-09 09:53:36
End time: 2025-05-09 09:54:16
Total execution time: 40.15 seconds (0.67 minutes)

================================================================================

