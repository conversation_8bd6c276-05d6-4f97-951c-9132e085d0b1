# Metrics Log - ANOMALY
# Run ID: 20250509_095836
# Timestamp: 2025-05-09 09:58:36
================================================================================

## RUN CONFIGURATION
data: data\processed\borg_traces_cleaned.csv
task: anomaly
target_cols: ['average_usage_cpu', 'average_usage_memory']
batch_size: 1000
test_size: 0.2
use_gpu: False
use_xgboost: 1
use_lightgbm: 1
use_ensemble: 1
dynamic_components: True
min_components: 2
max_components: 15
learning_rate: 0.002
lr_scheduler_type: one_cycle
lr_warmup_epochs: 8
lr_min: 5e-06
lr_max: 0.01
lr_cycle_epochs: 30
output: results
labeled_anomaly_data: None
augmentation_ratio: 0.75
similarity_threshold: 0.0
show_viz_info: False
visualize_clusters: True
scaling_method: standard
apply_power_transform: False
pre_cluster_dim_reduction: False

================================================================================

## DATASET INFORMATION
Dataset: data\processed\borg_traces_cleaned.csv
Number of rows: 6000
Number of columns: 113

## ANOMALY DETECTION METRICS
### Confusion Matrix
True Negatives (TN): 560
False Positives (FP): 340
False Negatives (FN): 10
True Positives (TP): 290

### Performance Metrics
Accuracy:    0.7083
Precision:   0.4603
Recall:      0.9667
F1 Score:    0.6237
Specificity: 0.6222

### Additional Metrics
roc_auc: 0.9207
pr_auc: 0.8625

================================================================================

## FEATURE IMPORTANCE
Rank   Feature                        Importance
--------------------------------------------------
1      <USER>                 <GROUP>.017940
2      start_time_second_tan          0.014013
3      collection_type                0.014003
4      start_time_minute_tan          0.013980
5      time_hour_tan                  0.013978
6      start_time_minute_tan_outlier  0.013896
7      page_cache_memory_outlier      0.013423
8      end_time_minute_tan_outlier    0.013345
9      instance_index_outlier         0.013076
10     duration_category              0.013064

================================================================================

## COMPONENT METRICS: ANOMALY MODEL 1: ISOLATIONFOREST
Metrics for anomaly detection model 1 (IsolationForest)

### parameters
Metric                    Value          
----------------------------------------
bootstrap                 False
contamination             0.200000       
max_features              1.000000       
max_samples               auto
n_estimators              200
n_jobs                    None
random_state              42
verbose                   0
warm_start                False

### score_stats
Metric                    Value          
----------------------------------------
min                       -0.506179      
max                       -0.342761      
mean                      -0.393173      
std                       0.037360       
median                    -0.382833      

### n_estimators

### max_samples


================================================================================

## COMPONENT METRICS: ANOMALY MODEL 2: LOCALOUTLIERFACTOR
Metrics for anomaly detection model 2 (LocalOutlierFactor)

### parameters
Metric                    Value          
----------------------------------------
algorithm                 auto
contamination             0.200000       
leaf_size                 30
metric                    minkowski
metric_params             None
n_jobs                    None
n_neighbors               10
novelty                   True
p                         2

### score_stats
Metric                    Value          
----------------------------------------
min                       -3.457733      
max                       -0.952081      
mean                      -1.324549      
std                       0.564819       
median                    -1.080561      

### n_neighbors_effective

### n_samples


================================================================================

## COMPONENT METRICS: ANOMALY MODEL 3: ONECLASSSVM
Metrics for anomaly detection model 3 (OneClassSVM)

### parameters
Metric                    Value          
----------------------------------------
cache_size                500
coef0                     0.000000       
degree                    3
gamma                     0.003682       
kernel                    rbf
max_iter                  2500
nu                        0.500000       
shrinking                 True
tol                       0.000010       
verbose                   False

### score_stats
Metric                    Value          
----------------------------------------
min                       67.902597      
max                       218.245496     
mean                      169.509145     
std                       35.491420      
median                    180.809199     

### n_support_vectors

### dual_coef_stats
Metric                    Value          
----------------------------------------
min                       0.296197       
max                       1.000000       
mean                      0.996016       


================================================================================

## EXECUTION TIME
Start time: 2025-05-09 09:58:36
End time: 2025-05-09 09:59:17
Total execution time: 41.56 seconds (0.69 minutes)

================================================================================

