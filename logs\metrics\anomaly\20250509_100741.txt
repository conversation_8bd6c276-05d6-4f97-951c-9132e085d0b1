# Metrics Log - ANOMALY
# Run ID: 20250509_100741
# Timestamp: 2025-05-09 10:07:41
================================================================================

## RUN CONFIGURATION
data: data\processed\borg_traces_cleaned.csv
task: anomaly
target_cols: ['average_usage_cpu', 'average_usage_memory']
batch_size: 1000
test_size: 0.2
use_gpu: False
use_xgboost: 1
use_lightgbm: 1
use_ensemble: 1
dynamic_components: True
min_components: 2
max_components: 15
learning_rate: 0.002
lr_scheduler_type: one_cycle
lr_warmup_epochs: 8
lr_min: 5e-06
lr_max: 0.01
lr_cycle_epochs: 30
output: results
labeled_anomaly_data: None
augmentation_ratio: 0.75
similarity_threshold: 0.0
show_viz_info: False
visualize_clusters: True
scaling_method: standard
apply_power_transform: False
pre_cluster_dim_reduction: False

================================================================================

## DATASET INFORMATION
Dataset: data\processed\borg_traces_cleaned.csv
Number of rows: 6000
Number of columns: 113

## ANOMALY DETECTION METRICS
### Confusion Matrix
True Negatives (TN): 556
False Positives (FP): 344
False Negatives (FN): 9
True Positives (TP): 291

### Performance Metrics
Accuracy:    0.7058
Precision:   0.4583
Recall:      0.9700
F1 Score:    0.6225
Specificity: 0.6178

### Additional Metrics
roc_auc: 0.9439
pr_auc: 0.8943

================================================================================

## FEATURE IMPORTANCE
Rank   Feature                        Importance
--------------------------------------------------
1      <USER>    <GROUP>.016151
2      instance_index                 0.015676
3      time_hour_tan                  0.014829
4      end_time_second_tan            0.014516
5      start_time_second_tan          0.014397
6      end_time_second_sin            0.013435
7      start_time_hour_tan_outlier    0.013221
8      collection_type                0.012741
9      assigned_memory_outlier        0.012670
10     duration_category              0.012546

================================================================================

## COMPONENT METRICS: ANOMALY MODEL 1: ISOLATIONFOREST
Metrics for anomaly detection model 1 (IsolationForest)

### parameters
Metric                    Value          
----------------------------------------
bootstrap                 False
contamination             0.150000       
max_features              1.000000       
max_samples               auto
n_estimators              250
n_jobs                    None
random_state              42
verbose                   0
warm_start                False

### score_stats
Metric                    Value          
----------------------------------------
min                       -0.511423      
max                       -0.342506      
mean                      -0.387672      
std                       0.040927       
median                    -0.375577      

### n_estimators

### max_samples


================================================================================

## COMPONENT METRICS: ANOMALY MODEL 2: LOCALOUTLIERFACTOR
Metrics for anomaly detection model 2 (LocalOutlierFactor)

### parameters
Metric                    Value          
----------------------------------------
algorithm                 auto
contamination             0.150000       
leaf_size                 30
metric                    minkowski
metric_params             None
n_jobs                    None
n_neighbors               15
novelty                   True
p                         2

### score_stats
Metric                    Value          
----------------------------------------
min                       -4.040075      
max                       -0.968377      
mean                      -1.283431      
std                       0.536525       
median                    -1.083738      

### n_neighbors_effective

### n_samples


================================================================================

## COMPONENT METRICS: ANOMALY MODEL 3: ONECLASSSVM
Metrics for anomaly detection model 3 (OneClassSVM)

### parameters
Metric                    Value          
----------------------------------------
cache_size                500
coef0                     0.000000       
degree                    3
gamma                     0.005237       
kernel                    rbf
max_iter                  2500
nu                        0.500000       
shrinking                 True
tol                       0.000010       
verbose                   False

### score_stats
Metric                    Value          
----------------------------------------
min                       0.159900       
max                       160.257464     
mean                      112.226085     
std                       34.695798      
median                    121.920111     

### n_support_vectors

### dual_coef_stats
Metric                    Value          
----------------------------------------
min                       0.201955       
max                       1.000000       
mean                      0.996016       


================================================================================

## EXECUTION TIME
Start time: 2025-05-09 10:07:41
End time: 2025-05-09 10:08:23
Total execution time: 41.64 seconds (0.69 minutes)

================================================================================

