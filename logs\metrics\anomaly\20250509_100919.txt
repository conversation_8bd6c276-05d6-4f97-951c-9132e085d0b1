# Metrics Log - ANOMALY
# Run ID: 20250509_100919
# Timestamp: 2025-05-09 10:09:19
================================================================================

## RUN CONFIGURATION
data: data\processed\borg_traces_cleaned.csv
task: anomaly
target_cols: ['average_usage_cpu', 'average_usage_memory']
batch_size: 1000
test_size: 0.2
use_gpu: False
use_xgboost: 1
use_lightgbm: 1
use_ensemble: 1
dynamic_components: True
min_components: 2
max_components: 15
learning_rate: 0.002
lr_scheduler_type: one_cycle
lr_warmup_epochs: 8
lr_min: 5e-06
lr_max: 0.01
lr_cycle_epochs: 30
output: results
labeled_anomaly_data: None
augmentation_ratio: 0.75
similarity_threshold: 0.0
show_viz_info: False
visualize_clusters: True
scaling_method: standard
apply_power_transform: False
pre_cluster_dim_reduction: False

================================================================================

## DATASET INFORMATION
Dataset: data\processed\borg_traces_cleaned.csv
Number of rows: 6000
Number of columns: 113

## ANOMALY DETECTION METRICS
### Confusion Matrix
True Negatives (TN): 889
False Positives (FP): 11
False Negatives (FN): 92
True Positives (TP): 208

### Performance Metrics
Accuracy:    0.9142
Precision:   0.9498
Recall:      0.6933
F1 Score:    0.8015
Specificity: 0.9878

### Additional Metrics
roc_auc: 0.9075
pr_auc: 0.8451

================================================================================

## FEATURE IMPORTANCE
Rank   Feature                        Importance
--------------------------------------------------
1      <USER>                  <GROUP>.014397
2      start_time_hour_tan            0.014112
3      instance_index_outlier         0.013941
4      start_time_second_tan          0.013879
5      start_time_second_sin          0.013710
6      end_time_second_sin            0.013388
7      end_time_hour_tan              0.013240
8      end_time_second_tan            0.013062
9      time_weekday_tan               0.013045
10     duration_category              0.012928

================================================================================

## COMPONENT METRICS: ANOMALY MODEL 1: ISOLATIONFOREST
Metrics for anomaly detection model 1 (IsolationForest)

### parameters
Metric                    Value          
----------------------------------------
bootstrap                 False
contamination             0.150000       
max_features              1.000000       
max_samples               auto
n_estimators              250
n_jobs                    None
random_state              42
verbose                   0
warm_start                False

### score_stats
Metric                    Value          
----------------------------------------
min                       -0.506585      
max                       -0.343358      
mean                      -0.390260      
std                       0.040774       
median                    -0.377693      

### n_estimators

### max_samples


================================================================================

## COMPONENT METRICS: ANOMALY MODEL 2: LOCALOUTLIERFACTOR
Metrics for anomaly detection model 2 (LocalOutlierFactor)

### parameters
Metric                    Value          
----------------------------------------
algorithm                 auto
contamination             0.150000       
leaf_size                 30
metric                    minkowski
metric_params             None
n_jobs                    None
n_neighbors               15
novelty                   True
p                         2

### score_stats
Metric                    Value          
----------------------------------------
min                       -3.253691      
max                       -0.952305      
mean                      -1.265704      
std                       0.484253       
median                    -1.060557      

### n_neighbors_effective

### n_samples


================================================================================

## COMPONENT METRICS: ANOMALY MODEL 3: ONECLASSSVM
Metrics for anomaly detection model 3 (OneClassSVM)

### parameters
Metric                    Value          
----------------------------------------
cache_size                500
coef0                     0.000000       
degree                    3
gamma                     0.005022       
kernel                    rbf
max_iter                  2500
nu                        0.500000       
shrinking                 True
tol                       0.000010       
verbose                   False

### score_stats
Metric                    Value          
----------------------------------------
min                       27.494176      
max                       153.170461     
mean                      115.084212     
std                       32.816884      
median                    124.824242     

### n_support_vectors

### dual_coef_stats
Metric                    Value          
----------------------------------------
min                       0.057454       
max                       1.000000       
mean                      0.990099       


================================================================================

## EXECUTION TIME
Start time: 2025-05-09 10:09:19
End time: 2025-05-09 10:10:01
Total execution time: 42.58 seconds (0.71 minutes)

================================================================================

