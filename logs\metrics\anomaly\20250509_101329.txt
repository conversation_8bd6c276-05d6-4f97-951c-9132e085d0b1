# Metrics Log - ANOMALY
# Run ID: 20250509_101329
# Timestamp: 2025-05-09 10:13:29
================================================================================

## RUN CONFIGURATION
data: data\processed\borg_traces_cleaned.csv
task: anomaly
target_cols: ['average_usage_cpu', 'average_usage_memory']
batch_size: 1000
test_size: 0.2
use_gpu: False
use_xgboost: 1
use_lightgbm: 1
use_ensemble: 1
dynamic_components: True
min_components: 2
max_components: 15
learning_rate: 0.002
lr_scheduler_type: one_cycle
lr_warmup_epochs: 8
lr_min: 5e-06
lr_max: 0.01
lr_cycle_epochs: 30
output: results
labeled_anomaly_data: None
augmentation_ratio: 0.75
similarity_threshold: 0.0
show_viz_info: False
visualize_clusters: True
scaling_method: standard
apply_power_transform: False
pre_cluster_dim_reduction: False

================================================================================

## DATASET INFORMATION
Dataset: data\processed\borg_traces_cleaned.csv
Number of rows: 6000
Number of columns: 113

## ANOMALY DETECTION METRICS
### Confusion Matrix
True Negatives (TN): 556
False Positives (FP): 344
False Negatives (FN): 18
True Positives (TP): 282

### Performance Metrics
Accuracy:    0.6983
Precision:   0.4505
Recall:      0.9400
F1 Score:    0.6091
Specificity: 0.6178

### Additional Metrics
roc_auc: 0.9080
pr_auc: 0.8440

================================================================================

## FEATURE IMPORTANCE
Rank   Feature                        Importance
--------------------------------------------------
1      <USER>                  <GROUP>.016894
2      end_time_minute_tan_outlier    0.014812
3      duration_category              0.014185
4      time_weekday_sin               0.013883
5      instance_index                 0.013705
6      time_weekday_tan               0.013648
7      machine_id                     0.013524
8      end_time_hour_tan              0.013454
9      end_time_second_tan            0.013358
10     end_time_second_sin            0.013352

================================================================================

## COMPONENT METRICS: ANOMALY MODEL 1: ISOLATIONFOREST
Metrics for anomaly detection model 1 (IsolationForest)

### parameters
Metric                    Value          
----------------------------------------
bootstrap                 False
contamination             0.150000       
max_features              1.000000       
max_samples               auto
n_estimators              250
n_jobs                    None
random_state              42
verbose                   0
warm_start                False

### score_stats
Metric                    Value          
----------------------------------------
min                       -0.545446      
max                       -0.344805      
mean                      -0.394980      
std                       0.040953       
median                    -0.383644      

### n_estimators

### max_samples


================================================================================

## COMPONENT METRICS: ANOMALY MODEL 2: LOCALOUTLIERFACTOR
Metrics for anomaly detection model 2 (LocalOutlierFactor)

### parameters
Metric                    Value          
----------------------------------------
algorithm                 auto
contamination             0.150000       
leaf_size                 30
metric                    minkowski
metric_params             None
n_jobs                    None
n_neighbors               15
novelty                   True
p                         2

### score_stats
Metric                    Value          
----------------------------------------
min                       -10.617754     
max                       -0.978750      
mean                      -1.354871      
std                       0.997438       
median                    -1.101793      

### n_neighbors_effective

### n_samples


================================================================================

## COMPONENT METRICS: ANOMALY MODEL 3: ONECLASSSVM
Metrics for anomaly detection model 3 (OneClassSVM)

### parameters
Metric                    Value          
----------------------------------------
cache_size                500
coef0                     0.000000       
degree                    3
gamma                     0.005216       
kernel                    rbf
max_iter                  2500
nu                        0.500000       
shrinking                 True
tol                       0.000010       
verbose                   False

### score_stats
Metric                    Value          
----------------------------------------
min                       18.592604      
max                       157.468978     
mean                      112.035574     
std                       31.868830      
median                    120.878904     

### n_support_vectors

### dual_coef_stats
Metric                    Value          
----------------------------------------
min                       0.091051       
max                       1.000000       
mean                      0.992063       


================================================================================

## EXECUTION TIME
Start time: 2025-05-09 10:13:29
End time: 2025-05-09 10:14:10
Total execution time: 41.60 seconds (0.69 minutes)

================================================================================

