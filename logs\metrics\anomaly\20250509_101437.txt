# Metrics Log - ANOMALY
# Run ID: 20250509_101437
# Timestamp: 2025-05-09 10:14:37
================================================================================

## RUN CONFIGURATION
data: data\processed\borg_traces_cleaned.csv
task: anomaly
target_cols: ['average_usage_cpu', 'average_usage_memory']
batch_size: 1000
test_size: 0.2
use_gpu: False
use_xgboost: 1
use_lightgbm: 1
use_ensemble: 1
dynamic_components: True
min_components: 2
max_components: 15
learning_rate: 0.002
lr_scheduler_type: one_cycle
lr_warmup_epochs: 8
lr_min: 5e-06
lr_max: 0.01
lr_cycle_epochs: 30
output: results
labeled_anomaly_data: None
augmentation_ratio: 0.75
similarity_threshold: 0.0
show_viz_info: False
visualize_clusters: True
scaling_method: standard
apply_power_transform: False
pre_cluster_dim_reduction: False

================================================================================

## DATASET INFORMATION
Dataset: data\processed\borg_traces_cleaned.csv
Number of rows: 6000
Number of columns: 113

## ANOMALY DETECTION METRICS
### Confusion Matrix
True Negatives (TN): 568
False Positives (FP): 332
False Negatives (FN): 12
True Positives (TP): 288

### Performance Metrics
Accuracy:    0.7133
Precision:   0.4645
Recall:      0.9600
F1 Score:    0.6261
Specificity: 0.6311

### Additional Metrics
roc_auc: 0.9186
pr_auc: 0.8663

================================================================================

## FEATURE IMPORTANCE
Rank   Feature                        Importance
--------------------------------------------------
1      <USER> <GROUP>.015314
2      start_time_hour_tan_outlier    0.014218
3      start_time_minute_tan          0.014153
4      time_hour_tan                  0.013872
5      instance_index_outlier         0.013863
6      start_time_minute_tan_outlier  0.013478
7      end_time_second_tan            0.013380
8      instance_index                 0.013353
9      end_time_hour_tan_outlier      0.013339
10     start_time_second_tan          0.012920

================================================================================

## COMPONENT METRICS: ANOMALY MODEL 1: ISOLATIONFOREST
Metrics for anomaly detection model 1 (IsolationForest)

### parameters
Metric                    Value          
----------------------------------------
bootstrap                 False
contamination             0.150000       
max_features              1.000000       
max_samples               auto
n_estimators              250
n_jobs                    None
random_state              42
verbose                   0
warm_start                False

### score_stats
Metric                    Value          
----------------------------------------
min                       -0.536470      
max                       -0.343065      
mean                      -0.393094      
std                       0.045508       
median                    -0.378372      

### n_estimators

### max_samples


================================================================================

## COMPONENT METRICS: ANOMALY MODEL 2: LOCALOUTLIERFACTOR
Metrics for anomaly detection model 2 (LocalOutlierFactor)

### parameters
Metric                    Value          
----------------------------------------
algorithm                 auto
contamination             0.150000       
leaf_size                 30
metric                    minkowski
metric_params             None
n_jobs                    None
n_neighbors               15
novelty                   True
p                         2

### score_stats
Metric                    Value          
----------------------------------------
min                       -3.131227      
max                       -0.976100      
mean                      -1.298683      
std                       0.542885       
median                    -1.051467      

### n_neighbors_effective

### n_samples


================================================================================

## COMPONENT METRICS: ANOMALY MODEL 3: ONECLASSSVM
Metrics for anomaly detection model 3 (OneClassSVM)

### parameters
Metric                    Value          
----------------------------------------
cache_size                500
coef0                     0.000000       
degree                    3
gamma                     0.005139       
kernel                    rbf
max_iter                  2500
nu                        0.500000       
shrinking                 True
tol                       0.000010       
verbose                   False

### score_stats
Metric                    Value          
----------------------------------------
min                       1.843456       
max                       154.107379     
mean                      111.874553     
std                       37.187088      
median                    125.832431     

### n_support_vectors

### dual_coef_stats
Metric                    Value          
----------------------------------------
min                       0.293251       
max                       1.000000       
mean                      0.996016       


================================================================================

## EXECUTION TIME
Start time: 2025-05-09 10:14:37
End time: 2025-05-09 10:15:19
Total execution time: 42.13 seconds (0.70 minutes)

================================================================================

