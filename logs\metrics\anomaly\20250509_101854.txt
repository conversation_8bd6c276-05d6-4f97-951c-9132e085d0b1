# Metrics Log - ANOMALY
# Run ID: 20250509_101854
# Timestamp: 2025-05-09 10:18:54
================================================================================

## RUN CONFIGURATION
data: data\processed\borg_traces_cleaned.csv
task: anomaly
target_cols: ['average_usage_cpu', 'average_usage_memory']
batch_size: 1000
test_size: 0.2
use_gpu: False
use_xgboost: 1
use_lightgbm: 1
use_ensemble: 1
dynamic_components: True
min_components: 2
max_components: 15
learning_rate: 0.002
lr_scheduler_type: one_cycle
lr_warmup_epochs: 8
lr_min: 5e-06
lr_max: 0.01
lr_cycle_epochs: 30
output: results
labeled_anomaly_data: None
augmentation_ratio: 0.75
similarity_threshold: 0.0
show_viz_info: False
visualize_clusters: True
scaling_method: standard
apply_power_transform: False
pre_cluster_dim_reduction: False

================================================================================

## DATASET INFORMATION
Dataset: data\processed\borg_traces_cleaned.csv
Number of rows: 6000
Number of columns: 113

## ANOMALY DETECTION METRICS
### Confusion Matrix
True Negatives (TN): 890
False Positives (FP): 10
False Negatives (FN): 122
True Positives (TP): 178

### Performance Metrics
Accuracy:    0.8900
Precision:   0.9468
Recall:      0.5933
F1 Score:    0.7295
Specificity: 0.9889

### Additional Metrics
roc_auc: 0.9154
pr_auc: 0.8454

================================================================================

## FEATURE IMPORTANCE
Rank   Feature                        Importance
--------------------------------------------------
1      <USER>          <GROUP>.014288
2      end_time_second_sin            0.014287
3      time_hour_tan                  0.013620
4      instance_index_outlier         0.013575
5      page_cache_memory_outlier      0.013416
6      duration_category              0.013317
7      end_time_hour_tan_outlier      0.013103
8      start_time_second_tan          0.012881
9      end_time_minute_tan            0.012807
10     start_time_hour_tan_outlier    0.012749

================================================================================

## COMPONENT METRICS: ANOMALY MODEL 1: ISOLATIONFOREST
Metrics for anomaly detection model 1 (IsolationForest)

### parameters
Metric                    Value          
----------------------------------------
bootstrap                 False
contamination             0.120000       
max_features              1.000000       
max_samples               auto
n_estimators              300
n_jobs                    None
random_state              42
verbose                   0
warm_start                False

### score_stats
Metric                    Value          
----------------------------------------
min                       -0.516728      
max                       -0.341753      
mean                      -0.389965      
std                       0.040731       
median                    -0.379417      

### n_estimators

### max_samples


================================================================================

## COMPONENT METRICS: ANOMALY MODEL 2: LOCALOUTLIERFACTOR
Metrics for anomaly detection model 2 (LocalOutlierFactor)

### parameters
Metric                    Value          
----------------------------------------
algorithm                 auto
contamination             0.120000       
leaf_size                 30
metric                    minkowski
metric_params             None
n_jobs                    None
n_neighbors               20
novelty                   True
p                         2

### score_stats
Metric                    Value          
----------------------------------------
min                       -9.689304      
max                       -0.978454      
mean                      -1.425939      
std                       0.999296       
median                    -1.094546      

### n_neighbors_effective

### n_samples


================================================================================

## COMPONENT METRICS: ANOMALY MODEL 3: ONECLASSSVM
Metrics for anomaly detection model 3 (OneClassSVM)

### parameters
Metric                    Value          
----------------------------------------
cache_size                500
coef0                     0.000000       
degree                    3
gamma                     0.006670       
kernel                    rbf
max_iter                  2500
nu                        0.500000       
shrinking                 True
tol                       0.000010       
verbose                   False

### score_stats
Metric                    Value          
----------------------------------------
min                       0.930192       
max                       113.387358     
mean                      76.764906      
std                       27.563025      
median                    84.098588      

### n_support_vectors

### dual_coef_stats
Metric                    Value          
----------------------------------------
min                       0.008141       
max                       1.000000       
mean                      0.988142       


================================================================================

## EXECUTION TIME
Start time: 2025-05-09 10:18:54
End time: 2025-05-09 10:19:37
Total execution time: 43.30 seconds (0.72 minutes)

================================================================================

