# Metrics Log - ANOMALY
# Run ID: 20250509_102957
# Timestamp: 2025-05-09 10:29:57
================================================================================

## RUN CONFIGURATION
data: data\processed\borg_traces_cleaned.csv
task: anomaly
target_cols: ['average_usage_cpu', 'average_usage_memory']
batch_size: 1000
test_size: 0.2
use_gpu: False
use_xgboost: 1
use_lightgbm: 1
use_ensemble: 1
dynamic_components: True
min_components: 2
max_components: 15
learning_rate: 0.002
lr_scheduler_type: one_cycle
lr_warmup_epochs: 8
lr_min: 5e-06
lr_max: 0.01
lr_cycle_epochs: 30
output: results
labeled_anomaly_data: None
augmentation_ratio: 0.75
similarity_threshold: 0.0
show_viz_info: False
visualize_clusters: True
scaling_method: standard
apply_power_transform: False
pre_cluster_dim_reduction: False

================================================================================

## DATASET INFORMATION
Dataset: data\processed\borg_traces_cleaned.csv
Number of rows: 6000
Number of columns: 113

## ANOMALY DETECTION METRICS
### Confusion Matrix
True Negatives (TN): 602
False Positives (FP): 298
False Negatives (FN): 18
True Positives (TP): 282

### Performance Metrics
Accuracy:    0.7367
Precision:   0.4862
Recall:      0.9400
F1 Score:    0.6409
Specificity: 0.6689

### Additional Metrics
roc_auc: 0.9132
pr_auc: 0.8616

================================================================================

## FEATURE IMPORTANCE
Rank   Feature                        Importance
--------------------------------------------------
1      <USER> <GROUP>.015496
2      start_time_hour_tan            0.014409
3      end_time_minute_tan_outlier    0.014098
4      time_hour_tan                  0.013895
5      end_time_hour_tan              0.013465
6      start_time_second_tan          0.013451
7      time_weekday_sin               0.013387
8      end_time_second_tan            0.013267
9      end_time_hour_tan_outlier      0.012900
10     page_cache_memory_outlier      0.012785

================================================================================

## COMPONENT METRICS: ANOMALY MODEL 1: ISOLATIONFOREST
Metrics for anomaly detection model 1 (IsolationForest)

### parameters
Metric                    Value          
----------------------------------------
bootstrap                 False
contamination             0.200000       
max_features              1.000000       
max_samples               auto
n_estimators              250
n_jobs                    None
random_state              42
verbose                   0
warm_start                False

### score_stats
Metric                    Value          
----------------------------------------
min                       -0.541076      
max                       -0.344578      
mean                      -0.387334      
std                       0.038830       
median                    -0.373357      

### n_estimators

### max_samples


================================================================================

## COMPONENT METRICS: ANOMALY MODEL 2: LOCALOUTLIERFACTOR
Metrics for anomaly detection model 2 (LocalOutlierFactor)

### parameters
Metric                    Value          
----------------------------------------
algorithm                 auto
contamination             0.200000       
leaf_size                 30
metric                    minkowski
metric_params             None
n_jobs                    None
n_neighbors               15
novelty                   True
p                         2

### score_stats
Metric                    Value          
----------------------------------------
min                       -18.659963     
max                       -0.963083      
mean                      -1.429869      
std                       1.789721       
median                    -1.072840      

### n_neighbors_effective

### n_samples


================================================================================

## COMPONENT METRICS: ANOMALY MODEL 3: ONECLASSSVM
Metrics for anomaly detection model 3 (OneClassSVM)

### parameters
Metric                    Value          
----------------------------------------
cache_size                500
coef0                     0.000000       
degree                    3
gamma                     0.004420       
kernel                    rbf
max_iter                  2500
nu                        0.500000       
shrinking                 True
tol                       0.000010       
verbose                   False

### score_stats
Metric                    Value          
----------------------------------------
min                       34.988065      
max                       180.333483     
mean                      142.084234     
std                       34.111254      
median                    155.996174     

### n_support_vectors

### dual_coef_stats
Metric                    Value          
----------------------------------------
min                       0.029538       
max                       1.000000       
mean                      0.990099       


================================================================================

## EXECUTION TIME
Start time: 2025-05-09 10:29:57
End time: 2025-05-09 10:30:39
Total execution time: 42.01 seconds (0.70 minutes)

================================================================================

