# Metrics Log - ANOMALY
# Run ID: 20250509_103134
# Timestamp: 2025-05-09 10:31:34
================================================================================

## RUN CONFIGURATION
data: data\processed\borg_traces_cleaned.csv
task: anomaly
target_cols: ['average_usage_cpu', 'average_usage_memory']
batch_size: 1000
test_size: 0.2
use_gpu: False
use_xgboost: 1
use_lightgbm: 1
use_ensemble: 1
dynamic_components: True
min_components: 2
max_components: 15
learning_rate: 0.002
lr_scheduler_type: one_cycle
lr_warmup_epochs: 8
lr_min: 5e-06
lr_max: 0.01
lr_cycle_epochs: 30
output: results
labeled_anomaly_data: None
augmentation_ratio: 0.75
similarity_threshold: 0.0
show_viz_info: False
visualize_clusters: True
scaling_method: standard
apply_power_transform: False
pre_cluster_dim_reduction: False

================================================================================

## DATASET INFORMATION
Dataset: data\processed\borg_traces_cleaned.csv
Number of rows: 6000
Number of columns: 113

## ANOMALY DETECTION METRICS
### Confusion Matrix
True Negatives (TN): 576
False Positives (FP): 324
False Negatives (FN): 15
True Positives (TP): 285

### Performance Metrics
Accuracy:    0.7175
Precision:   0.4680
Recall:      0.9500
F1 Score:    0.6271
Specificity: 0.6400

### Additional Metrics
roc_auc: 0.9228
pr_auc: 0.8697

================================================================================

## FEATURE IMPORTANCE
Rank   Feature                        Importance
--------------------------------------------------
1      <USER>                  <GROUP>.014692
2      instance_index                 0.013446
3      start_time_minute_tan_outlier  0.012981
4      instance_index_outlier         0.012961
5      start_time_hour_tan            0.012542
6      average_usage_memory_outlier   0.012466
7      page_cache_memory_outlier      0.012451
8      end_time_minute_tan            0.012393
9      duration_category              0.012316
10     assigned_memory_outlier        0.012270

================================================================================

## COMPONENT METRICS: ANOMALY MODEL 1: ISOLATIONFOREST
Metrics for anomaly detection model 1 (IsolationForest)

### parameters
Metric                    Value          
----------------------------------------
bootstrap                 False
contamination             0.200000       
max_features              1.000000       
max_samples               auto
n_estimators              250
n_jobs                    None
random_state              42
verbose                   0
warm_start                False

### score_stats
Metric                    Value          
----------------------------------------
min                       -0.529154      
max                       -0.343754      
mean                      -0.394624      
std                       0.047195       
median                    -0.376766      

### n_estimators

### max_samples


================================================================================

## COMPONENT METRICS: ANOMALY MODEL 2: LOCALOUTLIERFACTOR
Metrics for anomaly detection model 2 (LocalOutlierFactor)

### parameters
Metric                    Value          
----------------------------------------
algorithm                 auto
contamination             0.200000       
leaf_size                 30
metric                    minkowski
metric_params             None
n_jobs                    None
n_neighbors               15
novelty                   True
p                         2

### score_stats
Metric                    Value          
----------------------------------------
min                       -14.074880     
max                       -0.977085      
mean                      -1.518769      
std                       1.562480       
median                    -1.070742      

### n_neighbors_effective

### n_samples


================================================================================

## COMPONENT METRICS: ANOMALY MODEL 3: ONECLASSSVM
Metrics for anomaly detection model 3 (OneClassSVM)

### parameters
Metric                    Value          
----------------------------------------
cache_size                500
coef0                     0.000000       
degree                    3
gamma                     0.004437       
kernel                    rbf
max_iter                  2500
nu                        0.500000       
shrinking                 True
tol                       0.000010       
verbose                   False

### score_stats
Metric                    Value          
----------------------------------------
min                       32.145425      
max                       182.416433     
mean                      136.753643     
std                       40.713560      
median                    153.834374     

### n_support_vectors

### dual_coef_stats
Metric                    Value          
----------------------------------------
min                       0.011799       
max                       1.000000       
mean                      0.996016       


================================================================================

## EXECUTION TIME
Start time: 2025-05-09 10:31:34
End time: 2025-05-09 10:32:15
Total execution time: 41.53 seconds (0.69 minutes)

================================================================================

