# Metrics Log - ANOMALY
# Run ID: 20250509_103255
# Timestamp: 2025-05-09 10:32:55
================================================================================

## RUN CONFIGURATION
data: data\processed\borg_traces_cleaned.csv
task: anomaly
target_cols: ['average_usage_cpu', 'average_usage_memory']
batch_size: 1000
test_size: 0.2
use_gpu: False
use_xgboost: 1
use_lightgbm: 1
use_ensemble: 1
dynamic_components: True
min_components: 2
max_components: 15
learning_rate: 0.002
lr_scheduler_type: one_cycle
lr_warmup_epochs: 8
lr_min: 5e-06
lr_max: 0.01
lr_cycle_epochs: 30
output: results
labeled_anomaly_data: None
augmentation_ratio: 0.75
similarity_threshold: 0.0
show_viz_info: False
visualize_clusters: True
scaling_method: standard
apply_power_transform: False
pre_cluster_dim_reduction: False

================================================================================

## DATASET INFORMATION
Dataset: data\processed\borg_traces_cleaned.csv
Number of rows: 6000
Number of columns: 113

## ANOMALY DETECTION METRICS
### Confusion Matrix
True Negatives (TN): 610
False Positives (FP): 290
False Negatives (FN): 13
True Positives (TP): 287

### Performance Metrics
Accuracy:    0.7475
Precision:   0.4974
Recall:      0.9567
F1 Score:    0.6545
Specificity: 0.6778

### Additional Metrics
roc_auc: 0.9455
pr_auc: 0.9046

================================================================================

## FEATURE IMPORTANCE
Rank   Feature                        Importance
--------------------------------------------------
1      <USER>    <GROUP>.014855
2      collection_type                0.013702
3      memory_accesses_per_instruction_outlier 0.013629
4      start_time_second_sin          0.013250
5      instance_index                 0.013248
6      time_hour_tan                  0.012956
7      end_time_second_sin            0.012830
8      end_time_second_tan            0.012770
9      start_time_second_tan          0.012739
10     resource_request_cpu_outlier   0.012620

================================================================================

## COMPONENT METRICS: ANOMALY MODEL 1: ISOLATIONFOREST
Metrics for anomaly detection model 1 (IsolationForest)

### parameters
Metric                    Value          
----------------------------------------
bootstrap                 False
contamination             0.180000       
max_features              1.000000       
max_samples               auto
n_estimators              250
n_jobs                    None
random_state              42
verbose                   0
warm_start                False

### score_stats
Metric                    Value          
----------------------------------------
min                       -0.516866      
max                       -0.342614      
mean                      -0.388621      
std                       0.039164       
median                    -0.380350      

### n_estimators

### max_samples


================================================================================

## COMPONENT METRICS: ANOMALY MODEL 2: LOCALOUTLIERFACTOR
Metrics for anomaly detection model 2 (LocalOutlierFactor)

### parameters
Metric                    Value          
----------------------------------------
algorithm                 auto
contamination             0.180000       
leaf_size                 30
metric                    minkowski
metric_params             None
n_jobs                    None
n_neighbors               15
novelty                   True
p                         2

### score_stats
Metric                    Value          
----------------------------------------
min                       -3.108189      
max                       -0.957396      
mean                      -1.306916      
std                       0.489747       
median                    -1.071441      

### n_neighbors_effective

### n_samples


================================================================================

## COMPONENT METRICS: ANOMALY MODEL 3: ONECLASSSVM
Metrics for anomaly detection model 3 (OneClassSVM)

### parameters
Metric                    Value          
----------------------------------------
cache_size                500
coef0                     0.000000       
degree                    3
gamma                     0.004320       
kernel                    rbf
max_iter                  2500
nu                        0.500000       
shrinking                 True
tol                       0.000010       
verbose                   False

### score_stats
Metric                    Value          
----------------------------------------
min                       46.034104      
max                       186.608385     
mean                      141.088447     
std                       35.215522      
median                    151.105475     

### n_support_vectors

### dual_coef_stats
Metric                    Value          
----------------------------------------
min                       0.044233       
max                       1.000000       
mean                      0.990099       


================================================================================

## EXECUTION TIME
Start time: 2025-05-09 10:32:55
End time: 2025-05-09 10:33:37
Total execution time: 41.92 seconds (0.70 minutes)

================================================================================

