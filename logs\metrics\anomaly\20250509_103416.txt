# Metrics Log - ANOMALY
# Run ID: 20250509_103416
# Timestamp: 2025-05-09 10:34:16
================================================================================

## RUN CONFIGURATION
data: data\processed\borg_traces_cleaned.csv
task: anomaly
target_cols: ['average_usage_cpu', 'average_usage_memory']
batch_size: 1000
test_size: 0.2
use_gpu: False
use_xgboost: 1
use_lightgbm: 1
use_ensemble: 1
dynamic_components: True
min_components: 2
max_components: 15
learning_rate: 0.002
lr_scheduler_type: one_cycle
lr_warmup_epochs: 8
lr_min: 5e-06
lr_max: 0.01
lr_cycle_epochs: 30
output: results
labeled_anomaly_data: None
augmentation_ratio: 0.75
similarity_threshold: 0.0
show_viz_info: False
visualize_clusters: True
scaling_method: standard
apply_power_transform: False
pre_cluster_dim_reduction: False

================================================================================

## DATASET INFORMATION
Dataset: data\processed\borg_traces_cleaned.csv
Number of rows: 6000
Number of columns: 113

## ANOMALY DETECTION METRICS
### Confusion Matrix
True Negatives (TN): 879
False Positives (FP): 21
False Negatives (FN): 61
True Positives (TP): 239

### Performance Metrics
Accuracy:    0.9317
Precision:   0.9192
Recall:      0.7967
F1 Score:    0.8536
Specificity: 0.9767

### Additional Metrics
roc_auc: 0.9370
pr_auc: 0.8857

================================================================================

## FEATURE IMPORTANCE
Rank   Feature                        Importance
--------------------------------------------------
1      <USER>    <GROUP>.014727
2      end_time_minute_tan            0.014563
3      page_cache_memory_outlier      0.014173
4      instance_index_outlier         0.013810
5      start_time_minute_tan_outlier  0.013754
6      resource_request_cpu_outlier   0.013520
7      start_time_hour_tan            0.013391
8      time_hour_tan                  0.013313
9      collection_type                0.013165
10     instance_index                 0.012951

================================================================================

## COMPONENT METRICS: ANOMALY MODEL 1: ISOLATIONFOREST
Metrics for anomaly detection model 1 (IsolationForest)

### parameters
Metric                    Value          
----------------------------------------
bootstrap                 False
contamination             0.180000       
max_features              1.000000       
max_samples               auto
n_estimators              250
n_jobs                    None
random_state              42
verbose                   0
warm_start                False

### score_stats
Metric                    Value          
----------------------------------------
min                       -0.532314      
max                       -0.341213      
mean                      -0.393166      
std                       0.045227       
median                    -0.384666      

### n_estimators

### max_samples


================================================================================

## COMPONENT METRICS: ANOMALY MODEL 2: LOCALOUTLIERFACTOR
Metrics for anomaly detection model 2 (LocalOutlierFactor)

### parameters
Metric                    Value          
----------------------------------------
algorithm                 auto
contamination             0.180000       
leaf_size                 30
metric                    minkowski
metric_params             None
n_jobs                    None
n_neighbors               15
novelty                   True
p                         2

### score_stats
Metric                    Value          
----------------------------------------
min                       -12.738528     
max                       -0.981340      
mean                      -1.571250      
std                       1.624508       
median                    -1.061384      

### n_neighbors_effective

### n_samples


================================================================================

## COMPONENT METRICS: ANOMALY MODEL 3: ONECLASSSVM
Metrics for anomaly detection model 3 (OneClassSVM)

### parameters
Metric                    Value          
----------------------------------------
cache_size                500
coef0                     0.000000       
degree                    3
gamma                     0.004412       
kernel                    rbf
max_iter                  2500
nu                        0.500000       
shrinking                 True
tol                       0.000010       
verbose                   False

### score_stats
Metric                    Value          
----------------------------------------
min                       25.028396      
max                       184.937463     
mean                      136.186263     
std                       39.077015      
median                    146.623037     

### n_support_vectors

### dual_coef_stats
Metric                    Value          
----------------------------------------
min                       0.105091       
max                       1.000000       
mean                      0.994036       


================================================================================

## EXECUTION TIME
Start time: 2025-05-09 10:34:16
End time: 2025-05-09 10:35:00
Total execution time: 44.20 seconds (0.74 minutes)

================================================================================

