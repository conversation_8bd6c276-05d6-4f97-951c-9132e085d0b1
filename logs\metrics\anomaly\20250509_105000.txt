# Metrics Log - ANOMALY
# Run ID: 20250509_105000
# Timestamp: 2025-05-09 10:50:00
================================================================================

## RUN CONFIGURATION
data: data\processed\borg_traces_cleanedb.csv
task: anomaly
target_cols: ['average_usage_cpu', 'average_usage_memory']
batch_size: 5000
test_size: 0.2
use_gpu: False
use_xgboost: 1
use_lightgbm: 1
use_ensemble: 1
dynamic_components: True
min_components: 2
max_components: 15
learning_rate: 0.002
lr_scheduler_type: one_cycle
lr_warmup_epochs: 8
lr_min: 5e-06
lr_max: 0.01
lr_cycle_epochs: 30
output: results
labeled_anomaly_data: None
augmentation_ratio: 0.5
similarity_threshold: 0.0
show_viz_info: False
visualize_clusters: True
scaling_method: standard
apply_power_transform: False
pre_cluster_dim_reduction: False

================================================================================

## DATASET INFORMATION
Dataset: data\processed\borg_traces_cleanedb.csv
Number of rows: 405894
Number of columns: 113

## ANOMALY DETECTION METRICS
### Confusion Matrix
True Negatives (TN): 58980
False Positives (FP): 1904
False Negatives (FN): 3484
True Positives (TP): 16811

### Performance Metrics
Accuracy:    0.9336
Precision:   0.8983
Recall:      0.8283
F1 Score:    0.8619
Specificity: 0.9687

### Additional Metrics
roc_auc: 0.9237
pr_auc: 0.8681

================================================================================

## FEATURE IMPORTANCE
Rank   Feature                        Importance
--------------------------------------------------
1      <USER>                 <GROUP>.013954
2      time_hour_tan                  0.013471
3      end_time_second_tan            0.012852
4      page_cache_memory_outlier      0.012836
5      end_time_second_sin            0.012803
6      start_time_hour_tan_outlier    0.012748
7      start_time_second_sin          0.012688
8      start_time_second_tan          0.012675
9      instance_index_outlier         0.012647
10     collection_type                0.012602

================================================================================

## COMPONENT METRICS: ANOMALY MODEL 1: ISOLATIONFOREST
Metrics for anomaly detection model 1 (IsolationForest)

### parameters
Metric                    Value          
----------------------------------------
bootstrap                 False
contamination             0.180000       
max_features              1.000000       
max_samples               auto
n_estimators              250
n_jobs                    None
random_state              42
verbose                   0
warm_start                False

### score_stats
Metric                    Value          
----------------------------------------
min                       -0.517669      
max                       -0.338262      
mean                      -0.384274      
std                       0.039338       
median                    -0.374373      

### n_estimators

### max_samples


================================================================================

## COMPONENT METRICS: ANOMALY MODEL 2: LOCALOUTLIERFACTOR
Metrics for anomaly detection model 2 (LocalOutlierFactor)

### parameters
Metric                    Value          
----------------------------------------
algorithm                 auto
contamination             0.180000       
leaf_size                 30
metric                    minkowski
metric_params             None
n_jobs                    None
n_neighbors               15
novelty                   True
p                         2

### score_stats
Metric                    Value          
----------------------------------------
min                       -72.343753     
max                       -0.945969      
mean                      -3.592607      
std                       10.405341      
median                    -1.067384      

### n_neighbors_effective

### n_samples


================================================================================

## COMPONENT METRICS: ANOMALY MODEL 3: ONECLASSSVM
Metrics for anomaly detection model 3 (OneClassSVM)

### parameters
Metric                    Value          
----------------------------------------
cache_size                500
coef0                     0.000000       
degree                    3
gamma                     0.004327       
kernel                    rbf
max_iter                  2500
nu                        0.500000       
shrinking                 True
tol                       0.000010       
verbose                   False

### score_stats
Metric                    Value          
----------------------------------------
min                       248.679957     
max                       961.928237     
mean                      743.240712     
std                       170.017162     
median                    789.458805     

### n_support_vectors

### dual_coef_stats
Metric                    Value          
----------------------------------------
min                       0.343995       
max                       1.000000       
mean                      0.998801       


================================================================================

## EXECUTION TIME
Start time: 2025-05-09 10:50:00
End time: 2025-05-09 13:32:53
Total execution time: 9773.14 seconds (162.89 minutes)

================================================================================

