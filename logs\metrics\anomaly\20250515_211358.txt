# Metrics Log - ANOMALY
# Run ID: 20250515_211358
# Timestamp: 2025-05-15 21:13:58
================================================================================

## RUN CONFIGURATION
data: data\processed\borg_traces_cleaned.csv
task: anomaly
target_cols: ['average_usage_cpu', 'average_usage_memory']
batch_size: 5000
test_size: 0.2
use_gpu: False
use_xgboost: 1
use_lightgbm: 1
use_ensemble: 1
dynamic_components: True
min_components: 2
max_components: 15
learning_rate: 0.002
lr_scheduler_type: one_cycle
lr_warmup_epochs: 8
lr_min: 5e-06
lr_max: 0.01
lr_cycle_epochs: 30
output: results
labeled_anomaly_data: None
augmentation_ratio: 0.5
similarity_threshold: 0.0
show_viz_info: False
visualize_clusters: True
scaling_method: standard
apply_power_transform: False
pre_cluster_dim_reduction: False

================================================================================

## DATASET INFORMATION
Dataset: data\processed\borg_traces_cleaned.csv
Number of rows: 6000
Number of columns: 113

## ANOMALY DETECTION METRICS
### Confusion Matrix
True Negatives (TN): 855
False Positives (FP): 45
False Negatives (FN): 73
True Positives (TP): 227

### Performance Metrics
Accuracy:    0.9017
Precision:   0.8346
Recall:      0.7567
F1 Score:    0.7937
Specificity: 0.9500

### Additional Metrics
roc_auc: 0.9092
pr_auc: 0.8419

================================================================================

## FEATURE IMPORTANCE
Rank   Feature                        Importance
--------------------------------------------------
1      <USER> <GROUP>.014555
2      end_time_second_sin            0.014505
3      time_second_tan_outlier        0.013982
4      time_hour_tan                  0.013841
5      end_time_hour_tan              0.013726
6      resource_request_cpu_outlier   0.013089
7      start_time_second_tan          0.013081
8      end_time_minute_tan_outlier    0.012906
9      start_time_second_sin          0.012810
10     collection_type                0.012537

================================================================================

## COMPONENT METRICS: ANOMALY MODEL 1: ISOLATIONFOREST
Metrics for anomaly detection model 1 (IsolationForest)

### parameters
Metric                    Value          
----------------------------------------
bootstrap                 False
contamination             0.180000       
max_features              1.000000       
max_samples               auto
n_estimators              250
n_jobs                    None
random_state              42
verbose                   0
warm_start                False

### score_stats
Metric                    Value          
----------------------------------------
min                       -0.556085      
max                       -0.345954      
mean                      -0.396504      
std                       0.045653       
median                    -0.383924      

### n_estimators

### max_samples


================================================================================

## COMPONENT METRICS: ANOMALY MODEL 2: LOCALOUTLIERFACTOR
Metrics for anomaly detection model 2 (LocalOutlierFactor)

### parameters
Metric                    Value          
----------------------------------------
algorithm                 auto
contamination             0.180000       
leaf_size                 30
metric                    minkowski
metric_params             None
n_jobs                    None
n_neighbors               15
novelty                   True
p                         2

### score_stats
Metric                    Value          
----------------------------------------
min                       -3.426164      
max                       -0.978047      
mean                      -1.342814      
std                       0.583520       
median                    -1.096742      

### n_neighbors_effective

### n_samples


================================================================================

## COMPONENT METRICS: ANOMALY MODEL 3: ONECLASSSVM
Metrics for anomaly detection model 3 (OneClassSVM)

### parameters
Metric                    Value          
----------------------------------------
cache_size                500
coef0                     0.000000       
degree                    3
gamma                     0.004387       
kernel                    rbf
max_iter                  2500
nu                        0.500000       
shrinking                 True
tol                       0.000010       
verbose                   False

### score_stats
Metric                    Value          
----------------------------------------
min                       158.597657     
max                       870.204931     
mean                      655.143235     
std                       177.355922     
median                    702.326933     

### n_support_vectors

### dual_coef_stats
Metric                    Value          
----------------------------------------
min                       0.009358       
max                       1.000000       
mean                      0.998752       


================================================================================

## EXECUTION TIME
Start time: 2025-05-15 21:13:58
End time: 2025-05-15 21:15:26
Total execution time: 88.63 seconds (1.48 minutes)

================================================================================

