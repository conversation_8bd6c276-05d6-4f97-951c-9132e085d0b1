# Metrics Log - ANOMALY
# Run ID: 20250515_211912
# Timestamp: 2025-05-15 21:19:12
================================================================================

## RUN CONFIGURATION
data: data\processed\borg_traces_cleaned.csv
task: anomaly
target_cols: ['average_usage_cpu', 'average_usage_memory']
batch_size: 5000
test_size: 0.2
use_gpu: False
use_xgboost: 1
use_lightgbm: 1
use_ensemble: 1
dynamic_components: True
min_components: 2
max_components: 15
learning_rate: 0.002
lr_scheduler_type: one_cycle
lr_warmup_epochs: 8
lr_min: 5e-06
lr_max: 0.01
lr_cycle_epochs: 30
output: results
labeled_anomaly_data: None
augmentation_ratio: 0.5
similarity_threshold: 0.0
show_viz_info: False
visualize_clusters: True
scaling_method: standard
apply_power_transform: False
pre_cluster_dim_reduction: False

================================================================================

## DATASET INFORMATION
Dataset: data\processed\borg_traces_cleaned.csv
Number of rows: 6000
Number of columns: 113

## ANOMALY DETECTION METRICS
### Confusion Matrix
True Negatives (TN): 874
False Positives (FP): 26
False Negatives (FN): 63
True Positives (TP): 237

### Performance Metrics
Accuracy:    0.9258
Precision:   0.9011
Recall:      0.7900
F1 Score:    0.8419
Specificity: 0.9711

### Additional Metrics
roc_auc: 0.9368
pr_auc: 0.8848

================================================================================

## FEATURE IMPORTANCE
Rank   Feature                        Importance
--------------------------------------------------
1      <USER>    <GROUP>.014164
2      start_time_second_sin          0.013801
3      time_second_tan_outlier        0.013710
4      end_time_minute_tan_outlier    0.013663
5      duration_category              0.013339
6      end_time_second_sin            0.013085
7      instance_index_outlier         0.013069
8      start_time_second_tan          0.013049
9      start_time_minute_tan_outlier  0.012983
10     time_weekday_sin               0.012943

================================================================================

## COMPONENT METRICS: ANOMALY MODEL 1: ISOLATIONFOREST
Metrics for anomaly detection model 1 (IsolationForest)

### parameters
Metric                    Value          
----------------------------------------
bootstrap                 False
contamination             0.180000       
max_features              1.000000       
max_samples               auto
n_estimators              250
n_jobs                    None
random_state              42
verbose                   0
warm_start                False

### score_stats
Metric                    Value          
----------------------------------------
min                       -0.505565      
max                       -0.342419      
mean                      -0.387698      
std                       0.039662       
median                    -0.377996      

### n_estimators

### max_samples


================================================================================

## COMPONENT METRICS: ANOMALY MODEL 2: LOCALOUTLIERFACTOR
Metrics for anomaly detection model 2 (LocalOutlierFactor)

### parameters
Metric                    Value          
----------------------------------------
algorithm                 auto
contamination             0.180000       
leaf_size                 30
metric                    minkowski
metric_params             None
n_jobs                    None
n_neighbors               15
novelty                   True
p                         2

### score_stats
Metric                    Value          
----------------------------------------
min                       -7.183086      
max                       -0.970158      
mean                      -1.351549      
std                       0.758380       
median                    -1.060723      

### n_neighbors_effective

### n_samples


================================================================================

## COMPONENT METRICS: ANOMALY MODEL 3: ONECLASSSVM
Metrics for anomaly detection model 3 (OneClassSVM)

### parameters
Metric                    Value          
----------------------------------------
cache_size                500
coef0                     0.000000       
degree                    3
gamma                     0.004409       
kernel                    rbf
max_iter                  2500
nu                        0.500000       
shrinking                 True
tol                       0.000010       
verbose                   False

### score_stats
Metric                    Value          
----------------------------------------
min                       85.462899      
max                       844.695087     
mean                      657.495748     
std                       177.240696     
median                    709.986300     

### n_support_vectors

### dual_coef_stats
Metric                    Value          
----------------------------------------
min                       0.058713       
max                       1.000000       
mean                      0.998752       


================================================================================

## EXECUTION TIME
Start time: 2025-05-15 21:19:12
End time: 2025-05-15 21:20:32
Total execution time: 79.93 seconds (1.33 minutes)

================================================================================

