# Metrics Log - ANOMALY
# Run ID: 20250515_212241
# Timestamp: 2025-05-15 21:22:41
================================================================================

## RUN CONFIGURATION
data: data\processed\borg_traces_cleaned.csv
task: anomaly
target_cols: ['average_usage_cpu', 'average_usage_memory']
batch_size: 5000
test_size: 0.2
use_gpu: False
use_xgboost: 1
use_lightgbm: 1
use_ensemble: 1
dynamic_components: True
min_components: 2
max_components: 15
learning_rate: 0.002
lr_scheduler_type: one_cycle
lr_warmup_epochs: 8
lr_min: 5e-06
lr_max: 0.01
lr_cycle_epochs: 30
output: results
labeled_anomaly_data: None
augmentation_ratio: 0.5
similarity_threshold: 0.0
show_viz_info: False
visualize_clusters: True
scaling_method: standard
apply_power_transform: False
pre_cluster_dim_reduction: False

================================================================================

## DATASET INFORMATION
Dataset: data\processed\borg_traces_cleaned.csv
Number of rows: 6000
Number of columns: 113

## ANOMALY DETECTION METRICS
### Confusion Matrix
True Negatives (TN): 866
False Positives (FP): 34
False Negatives (FN): 67
True Positives (TP): 233

### Performance Metrics
Accuracy:    0.9158
Precision:   0.8727
Recall:      0.7767
F1 Score:    0.8219
Specificity: 0.9622

### Additional Metrics
roc_auc: 0.9232
pr_auc: 0.8591

================================================================================

## FEATURE IMPORTANCE
Rank   Feature                        Importance
--------------------------------------------------
1      <USER> <GROUP>.017577
2      start_time_second_tan          0.014749
3      time_hour_tan                  0.014345
4      instance_index                 0.014075
5      start_time_second_sin          0.014016
6      end_time_second_sin            0.013515
7      start_time_minute_tan          0.013509
8      end_time_second_tan            0.013448
9      duration_category              0.013441
10     instance_index_outlier         0.013193

================================================================================

## COMPONENT METRICS: ANOMALY MODEL 1: ISOLATIONFOREST
Metrics for anomaly detection model 1 (IsolationForest)

### parameters
Metric                    Value          
----------------------------------------
bootstrap                 False
contamination             0.180000       
max_features              1.000000       
max_samples               auto
n_estimators              250
n_jobs                    None
random_state              42
verbose                   0
warm_start                False

### score_stats
Metric                    Value          
----------------------------------------
min                       -0.496659      
max                       -0.341997      
mean                      -0.387499      
std                       0.041122       
median                    -0.370772      

### n_estimators

### max_samples


================================================================================

## COMPONENT METRICS: ANOMALY MODEL 2: LOCALOUTLIERFACTOR
Metrics for anomaly detection model 2 (LocalOutlierFactor)

### parameters
Metric                    Value          
----------------------------------------
algorithm                 auto
contamination             0.180000       
leaf_size                 30
metric                    minkowski
metric_params             None
n_jobs                    None
n_neighbors               15
novelty                   True
p                         2

### score_stats
Metric                    Value          
----------------------------------------
min                       -16.046375     
max                       -0.973259      
mean                      -1.475339      
std                       1.566314       
median                    -1.078395      

### n_neighbors_effective

### n_samples


================================================================================

## COMPONENT METRICS: ANOMALY MODEL 3: ONECLASSSVM
Metrics for anomaly detection model 3 (OneClassSVM)

### parameters
Metric                    Value          
----------------------------------------
cache_size                500
coef0                     0.000000       
degree                    3
gamma                     0.004401       
kernel                    rbf
max_iter                  2500
nu                        0.500000       
shrinking                 True
tol                       0.000010       
verbose                   False

### score_stats
Metric                    Value          
----------------------------------------
min                       2.459698       
max                       883.713366     
mean                      662.101474     
std                       189.966343     
median                    727.782596     

### n_support_vectors

### dual_coef_stats
Metric                    Value          
----------------------------------------
min                       0.283332       
max                       1.000000       
mean                      0.999167       


================================================================================

## EXECUTION TIME
Start time: 2025-05-15 21:22:41
End time: 2025-05-15 21:24:02
Total execution time: 81.13 seconds (1.35 minutes)

================================================================================

