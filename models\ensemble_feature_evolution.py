"""
Ensemble Feature Evolution Layer combining XGBoost and LightGBM approaches
"""
import torch
import torch.nn as nn
import torch.nn.functional as F
import numpy as np

# Import feature evolution layers
try:
    from models.xgb_component import XGBFeatureEvolutionLayer
    XGBOOST_AVAILABLE = True
except ImportError:
    XGBOOST_AVAILABLE = False

try:
    from models.lgb_component import LGBFeatureEvolutionLayer
    LIGHTGBM_AVAILABLE = True
except ImportError:
    LIGHTGBM_AVAILABLE = False

from models.gb_component import FeatureEvolutionLayer

class EnsembleFeatureEvolutionLayer(nn.Module):
    """Ensemble feature evolution layer combining XGBoost and LightGBM approaches"""

    def __init__(self, input_dim, hidden_dim, n_stages=2, dropout=0.1,
                 use_xgboost=True, use_lightgbm=True, ensemble_weights=None):
        """Initialize ensemble feature evolution layer

        Args:
            input_dim: Input dimension
            hidden_dim: Hidden dimension
            n_stages: Number of evolution stages
            dropout: Dropout rate
            use_xgboost: Whether to use XGBoost-inspired layer
            use_lightgbm: Whether to use LightGBM-inspired layer
            ensemble_weights: Weights for ensemble combination [xgb_weight, lgb_weight, gradboostnn_weight]
        """
        super(EnsembleFeatureEvolutionLayer, self).__init__()

        self.input_dim = input_dim
        self.hidden_dim = hidden_dim
        self.n_stages = n_stages
        self.dropout = dropout

        # Check availability and user preferences
        self.use_xgboost = use_xgboost and XGBOOST_AVAILABLE
        self.use_lightgbm = use_lightgbm and LIGHTGBM_AVAILABLE

        # Always include GradBoostNN feature evolution as fallback
        self.use_gradboostnn = True

        # Initialize ensemble weights (will be learned during training)
        if ensemble_weights is None:
            # Equal weights by default
            n_models = sum([self.use_xgboost, self.use_lightgbm, self.use_gradboostnn])
            weight = 1.0 / max(1, n_models)
            ensemble_weights = [weight] * 3

        self.ensemble_weights = nn.Parameter(torch.tensor(ensemble_weights, dtype=torch.float))

        # Initialize feature evolution layers
        if self.use_xgboost:
            self.xgb_layer = XGBFeatureEvolutionLayer(input_dim, hidden_dim, n_stages, dropout)

        if self.use_lightgbm:
            self.lgb_layer = LGBFeatureEvolutionLayer(input_dim, hidden_dim, n_stages, dropout)

        # Always include GradBoostNN feature evolution layer as fallback
        self.gradboostnn_layer = FeatureEvolutionLayer(input_dim, hidden_dim, n_stages, dropout)

        # Final integration layer
        self.integration_layer = nn.Sequential(
            nn.Linear(input_dim, hidden_dim),
            nn.ReLU(),
            nn.Dropout(dropout),
            nn.Linear(hidden_dim, input_dim)
        )

        # Initialize weights
        self._init_weights()

    def _init_weights(self):
        """Initialize weights with Xavier uniform"""
        for m in self.modules():
            if isinstance(m, nn.Linear):
                nn.init.xavier_uniform_(m.weight)
                if m.bias is not None:
                    nn.init.zeros_(m.bias)

    def forward(self, x):
        """Forward pass with ensemble feature evolution

        Args:
            x: Input tensor

        Returns:
            evolved_features: Evolved features
            stage_outputs: Dictionary of outputs from each model
        """
        # Normalize ensemble weights with softmax
        weights = F.softmax(self.ensemble_weights, dim=0)

        # Initialize outputs dictionary
        outputs = {}
        evolved_features = torch.zeros_like(x)

        # Apply XGBoost layer if available
        if self.use_xgboost:
            xgb_features, xgb_stages = self.xgb_layer(x)
            evolved_features = evolved_features + weights[0] * xgb_features
            outputs['xgb'] = xgb_stages

        # Apply LightGBM layer if available
        if self.use_lightgbm:
            lgb_features, lgb_stages = self.lgb_layer(x)
            evolved_features = evolved_features + weights[1] * lgb_features
            outputs['lgb'] = lgb_stages

        # Apply GradBoostNN layer
        gradboostnn_features, gradboostnn_stages = self.gradboostnn_layer(x)
        evolved_features = evolved_features + weights[2] * gradboostnn_features
        outputs['gradboostnn'] = gradboostnn_stages

        # Apply final integration layer
        evolved_features = self.integration_layer(evolved_features) + 0.1 * x

        return evolved_features, outputs
