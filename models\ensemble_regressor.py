import numpy as np
import torch
from sklearn.base import BaseEstimator, RegressorMixin
from sklearn.model_selection import train_test_split
import lightgbm as lgb
from lightgbm.callback import early_stopping
try:
    import xgboost as xgb
    XGBOOST_AVAILABLE = True
except ImportError:
    XGBOOST_AVAILABLE = False

class EnsembleRegressor(BaseEstimator, RegressorMixin):
    def __init__(self, n_estimators=100, learning_rate=0.1, max_depth=3,
                 random_state=42, use_gpu=torch.cuda.is_available(),
                 early_stopping_rounds=10, ensemble_weights=None):
        self.n_estimators = n_estimators
        self.learning_rate = learning_rate
        self.max_depth = max_depth
        self.random_state = random_state
        self.use_gpu = use_gpu
        self.early_stopping_rounds = early_stopping_rounds
        self.ensemble_weights = ensemble_weights if ensemble_weights is not None else [0.5, 0.5]
        self.xgb_models = self.lgb_models = self.xgb_model = self.lgb_model = None

        if not XGBOOST_AVAILABLE:
            self.ensemble_weights = [0.0, 1.0]

        self.xgb_params = {
            'objective': 'reg:squarederror', 'learning_rate': learning_rate,
            'max_depth': max_depth, 'random_state': random_state,
            'tree_method': 'auto', 'verbosity': 0, 'n_jobs': -1
        }

        if self.use_gpu and XGBOOST_AVAILABLE:
            self.xgb_params.update({'tree_method': 'gpu_hist', 'gpu_id': 0})

        self.lgb_params = {
            'objective': 'regression', 'boosting_type': 'gbdt',
            'learning_rate': learning_rate, 'max_depth': max_depth,
            'random_state': random_state, 'verbose': -1,
            'n_estimators': n_estimators
        }

        if self.use_gpu:
            self.lgb_params.update({
                'device': 'gpu', 'gpu_platform_id': 0, 'gpu_device_id': 0
            })

    def fit(self, X, y, X_val=None, y_val=None):
        if X_val is None or y_val is None:
            X_train, X_val, y_train, y_val = train_test_split(X, y, test_size=0.2, random_state=self.random_state)
        else:
            X_train, y_train = X, y

        if len(y.shape) > 1 and y.shape[1] > 1:
            self._fit_multi_output(X_train, y_train, X_val, y_val)
        else:
            self._fit_single_output(X_train, y_train, X_val, y_val)

        if self.ensemble_weights is None or sum(self.ensemble_weights) == 0:
            self._optimize_weights(X_val, y_val)

        return self

    def _fit_multi_output(self, X_train, y_train, X_val, y_val):
        self.xgb_models = []
        self.lgb_models = []

        for i in range(y_train.shape[1]):
            if XGBOOST_AVAILABLE:
                dtrain = xgb.DMatrix(X_train, y_train[:, i])
                dval = xgb.DMatrix(X_val, y_val[:, i])
                xgb_model = xgb.train(
                    self.xgb_params, dtrain, num_boost_round=self.n_estimators,
                    evals=[(dtrain, 'train'), (dval, 'eval')],
                    early_stopping_rounds=self.early_stopping_rounds, verbose_eval=False
                )
                self.xgb_models.append(xgb_model)

            lgb_train = lgb.Dataset(X_train, y_train[:, i])
            lgb_val = lgb.Dataset(X_val, y_val[:, i])
            lgb_model = lgb.train(
                self.lgb_params.copy(), lgb_train, valid_sets=[lgb_val],
                num_boost_round=self.n_estimators,
                callbacks=[early_stopping(stopping_rounds=self.early_stopping_rounds, verbose=False)]
            )
            self.lgb_models.append(lgb_model)

    def _fit_single_output(self, X_train, y_train, X_val, y_val):
        if len(y_train.shape) > 1: y_train = y_train.ravel()
        if len(y_val.shape) > 1: y_val = y_val.ravel()

        if XGBOOST_AVAILABLE:
            dtrain = xgb.DMatrix(X_train, y_train)
            dval = xgb.DMatrix(X_val, y_val)
            self.xgb_model = xgb.train(
                self.xgb_params, dtrain, num_boost_round=self.n_estimators,
                evals=[(dtrain, 'train'), (dval, 'eval')],
                early_stopping_rounds=self.early_stopping_rounds, verbose_eval=False
            )

        lgb_train = lgb.Dataset(X_train, y_train)
        lgb_val = lgb.Dataset(X_val, y_val)
        self.lgb_model = lgb.train(
            self.lgb_params.copy(), lgb_train, valid_sets=[lgb_val],
            num_boost_round=self.n_estimators,
            callbacks=[early_stopping(stopping_rounds=self.early_stopping_rounds, verbose=False)]
        )

    def _optimize_weights(self, X_val, y_val):
        if not XGBOOST_AVAILABLE:
            self.ensemble_weights = [0.0, 1.0]
            return

        xgb_preds = self._predict_xgboost(X_val)
        lgb_preds = self._predict_lightgbm(X_val)
        best_error = float('inf')
        best_weights = [0.5, 0.5]

        for xgb_weight in np.linspace(0, 1, 11):
            lgb_weight = 1 - xgb_weight
            weighted_preds = xgb_weight * xgb_preds + lgb_weight * lgb_preds
            mse = np.mean(np.mean((weighted_preds - y_val) ** 2, axis=0)) if len(y_val.shape) > 1 else np.mean((weighted_preds - y_val) ** 2)

            if mse < best_error:
                best_error = mse
                best_weights = [xgb_weight, lgb_weight]

        self.ensemble_weights = best_weights

    def predict(self, X):
        xgb_preds = self._predict_xgboost(X)
        lgb_preds = self._predict_lightgbm(X)
        return self.ensemble_weights[0] * xgb_preds + self.ensemble_weights[1] * lgb_preds if XGBOOST_AVAILABLE else lgb_preds

    def _predict_xgboost(self, X):
        if not XGBOOST_AVAILABLE: return 0
        dtest = xgb.DMatrix(X)

        if self.xgb_models is not None:
            return np.column_stack([model.predict(dtest) for model in self.xgb_models])
        elif self.xgb_model is not None:
            return self.xgb_model.predict(dtest)
        else:
            return np.zeros((X.shape[0], 1))

    def _predict_lightgbm(self, X):
        if self.lgb_models is not None:
            return np.column_stack([model.predict(X) for model in self.lgb_models])
        elif self.lgb_model is not None:
            return self.lgb_model.predict(X)
        else:
            return np.zeros((X.shape[0], 1))

    def get_feature_importance(self):
        return self._get_lightgbm_importance()

    def _get_xgboost_importance(self):
        if not XGBOOST_AVAILABLE: return None

        def process_importance(model):
            imp = model.get_score(importance_type='gain')
            max_idx = max([int(f.replace('f', '')) for f in imp.keys()])
            feat_imp = np.zeros(max_idx + 1)
            for f, s in imp.items(): feat_imp[int(f.replace('f', ''))] = s
            return feat_imp

        if self.xgb_models:
            mean_imp = np.mean([process_importance(m) for m in self.xgb_models], axis=0)
            return mean_imp / np.sum(mean_imp) if np.sum(mean_imp) > 0 else mean_imp
        elif self.xgb_model:
            feat_imp = process_importance(self.xgb_model)
            return feat_imp / np.sum(feat_imp) if np.sum(feat_imp) > 0 else feat_imp
        return None

    def _get_lightgbm_importance(self):
        if self.lgb_models:
            mean_imp = np.mean([m.feature_importance(importance_type='gain') for m in self.lgb_models], axis=0)
            return mean_imp / np.sum(mean_imp) if np.sum(mean_imp) > 0 else mean_imp
        elif self.lgb_model:
            imp = self.lgb_model.feature_importance(importance_type='gain')
            return imp / np.sum(imp) if np.sum(imp) > 0 else imp
        return None

    def visualize_feature_importance(self, feature_names=None, top_n=10, X=None):
        importance = self.get_feature_importance()
        if not importance or len(importance) == 0: return

        if not feature_names: feature_names = [f"F{i}" for i in range(len(importance))]
        if len(feature_names) != len(importance):
            feature_names = (feature_names[:len(importance)] if len(feature_names) > len(importance)
                            else feature_names + [f"F{i}" for i in range(len(feature_names), len(importance))])

        indices = np.argsort(importance)[::-1][:top_n]
        return importance[indices], [feature_names[i] for i in indices]
