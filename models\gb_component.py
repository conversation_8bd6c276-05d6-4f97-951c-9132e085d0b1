import numpy as np
import torch
import torch.nn as nn
import torch.nn.functional as F
from sklearn.ensemble import GradientBoostingRegressor, GradientBoostingClassifier
import lightgbm as lgb
try:
    import xgboost as xgb
    XGBOOST_AVAILABLE = True
except ImportError:
    XGBOOST_AVAILABLE = False

class GBComponent:
    def __init__(self, task='regression', n_estimators=100, learning_rate=0.1,
                 max_depth=3, random_state=42, use_lightgbm=False, use_xgboost=True,
                 use_gpu=torch.cuda.is_available(), early_stopping_rounds=10):
        self.task = task
        self.n_estimators = n_estimators
        self.learning_rate = learning_rate
        self.max_depth = max_depth
        self.random_state = random_state
        self.use_lightgbm = use_lightgbm and not use_xgboost
        self.use_xgboost = use_xgboost and XGBOOST_AVAILABLE
        self.use_gpu = use_gpu
        self.early_stopping_rounds = early_stopping_rounds
        self.model = None

        if self.use_xgboost:
            self.params = {
                'objective': 'reg:squarederror' if task == 'regression' else 'binary:logistic',
                'n_estimators': n_estimators, 'learning_rate': learning_rate,
                'max_depth': max_depth, 'random_state': random_state,
                'tree_method': 'auto', 'verbosity': 0, 'n_jobs': -1
            }
            if self.use_gpu:
                self.params.update({'tree_method': 'gpu_hist', 'gpu_id': 0})
        elif self.use_lightgbm:
            self.params = {
                'objective': 'regression' if task == 'regression' else 'binary',
                'boosting_type': 'gbdt', 'n_estimators': n_estimators,
                'learning_rate': learning_rate, 'max_depth': max_depth,
                'random_state': random_state, 'verbose': -1
            }
            if self.use_gpu:
                self.params.update({'device': 'gpu', 'gpu_platform_id': 0, 'gpu_device_id': 0})
        else:
            self.model = (GradientBoostingRegressor if task == 'regression' else GradientBoostingClassifier)(
                n_estimators=n_estimators, learning_rate=learning_rate,
                max_depth=max_depth, random_state=random_state
            )

    def fit(self, X, y, X_val=None, y_val=None):
        multi_output = len(y.shape) > 1 and y.shape[1] > 1
        has_val = X_val is not None and y_val is not None

        if self.use_xgboost:
            if multi_output:
                self.models = []
                for i in range(y.shape[1]):
                    dtrain = xgb.DMatrix(X, y[:, i])
                    evals = [(dtrain, 'train')]
                    if has_val:
                        dval = xgb.DMatrix(X_val, y_val[:, i])
                        evals.append((dval, 'eval'))

                    model = xgb.train(
                        self.params, dtrain, num_boost_round=self.n_estimators,
                        evals=evals if has_val else None,
                        early_stopping_rounds=self.early_stopping_rounds if has_val else None,
                        verbose_eval=False
                    )
                    self.models.append(model)
            else:
                if len(y.shape) > 1: y = y.ravel()
                dtrain = xgb.DMatrix(X, y)
                evals = [(dtrain, 'train')]

                if has_val:
                    if len(y_val.shape) > 1: y_val = y_val.ravel()
                    dval = xgb.DMatrix(X_val, y_val)
                    evals.append((dval, 'eval'))

                self.model = xgb.train(
                    self.params, dtrain, num_boost_round=self.n_estimators,
                    evals=evals if has_val else None,
                    early_stopping_rounds=self.early_stopping_rounds if has_val else None,
                    verbose_eval=False
                )
        elif self.use_lightgbm:
            if multi_output:
                self.models = []
                for i in range(y.shape[1]):
                    train_data = lgb.Dataset(X, y[:, i])
                    valid_sets = None
                    if has_val:
                        valid_sets = [lgb.Dataset(X_val, y_val[:, i])]

                    model = lgb.train(
                        self.params, train_data, valid_sets=valid_sets,
                        early_stopping_rounds=self.early_stopping_rounds if has_val else None,
                        verbose_eval=False
                    )
                    self.models.append(model)
            else:
                if len(y.shape) > 1: y = y.ravel()
                train_data = lgb.Dataset(X, y)
                valid_sets = None

                if has_val:
                    if len(y_val.shape) > 1: y_val = y_val.ravel()
                    valid_sets = [lgb.Dataset(X_val, y_val)]

                self.model = lgb.train(
                    self.params, train_data, valid_sets=valid_sets,
                    early_stopping_rounds=self.early_stopping_rounds if has_val else None,
                    verbose_eval=False
                )
        else:
            if multi_output and self.task == 'regression':
                self.models = []
                for i in range(y.shape[1]):
                    model = GradientBoostingRegressor(
                        n_estimators=self.n_estimators, learning_rate=self.learning_rate,
                        max_depth=self.max_depth, random_state=self.random_state
                    )
                    model.fit(X, y[:, i])
                    self.models.append(model)
            else:
                if len(y.shape) > 1 and self.task == 'regression': y = y.ravel()
                self.model.fit(X, y)
        return self

    def predict(self, X):
        if hasattr(self, 'models') and self.models:
            if self.use_xgboost:
                dtest = xgb.DMatrix(X)
                return np.column_stack([model.predict(dtest) for model in self.models])
            else:
                return np.column_stack([model.predict(X) for model in self.models])
        else:
            if self.use_xgboost:
                return self.model.predict(xgb.DMatrix(X))
            else:
                return self.model.predict(X)

    def get_feature_importance(self):
        def process_xgb_importance(model):
            imp = model.get_score(importance_type='gain')
            feat_imp = np.zeros(self.params.get('num_feature', len(imp)))
            for f, s in imp.items():
                feat_imp[int(f.replace('f', ''))] = s
            return feat_imp / np.sum(feat_imp) if np.sum(feat_imp) > 0 else feat_imp

        if hasattr(self, 'models') and self.models:
            if self.use_xgboost:
                return np.mean([process_xgb_importance(m) for m in self.models], axis=0)
            elif self.use_lightgbm:
                return np.mean([m.feature_importance(importance_type='gain') for m in self.models], axis=0)
            else:
                return np.mean([m.feature_importances_ for m in self.models], axis=0)
        else:
            if self.use_xgboost:
                return process_xgb_importance(self.model)
            elif self.use_lightgbm:
                return self.model.feature_importance(importance_type='gain')
            else:
                return self.model.feature_importances_

    def get_staged_predictions(self, X, n_stages=None):
        n_stages = n_stages or self.n_estimators
        has_models = hasattr(self, 'models') and self.models
        staged_preds = []

        if self.use_xgboost:
            dtest = xgb.DMatrix(X)
            for i in range(1, n_stages + 1):
                if has_models:
                    stage_preds = [m.predict(dtest, ntree_limit=i) for m in self.models]
                    staged_preds.append(np.column_stack(stage_preds))
                else:
                    staged_preds.append(self.model.predict(dtest, ntree_limit=i))
        elif self.use_lightgbm:
            for i in range(1, n_stages + 1):
                if has_models:
                    stage_preds = [m.predict(X, num_iteration=i) for m in self.models]
                    staged_preds.append(np.column_stack(stage_preds))
                else:
                    staged_preds.append(self.model.predict(X, num_iteration=i))
        else:
            if has_models:
                for i in range(min(n_stages, self.n_estimators)):
                    stage_preds = [list(m.staged_predict(X))[i] for m in self.models]
                    staged_preds.append(np.column_stack(stage_preds))
            else:
                staged_preds = list(self.model.staged_predict(X))[:n_stages]

        return staged_preds

# TorchGBLayer removed as it's not used in the main transformer

class FeatureEvolutionLayer(nn.Module):
    def __init__(self, input_dim, hidden_dim, n_stages=3, dropout=0.1):
        super(FeatureEvolutionLayer, self).__init__()
        self.input_dim, self.hidden_dim, self.n_stages = input_dim, hidden_dim, n_stages
        self.initial_proj = nn.Linear(input_dim, hidden_dim)
        self.evolution_stages = nn.ModuleList([
            nn.Sequential(
                nn.Linear(hidden_dim, hidden_dim), nn.ReLU(),
                nn.Dropout(dropout), nn.Linear(hidden_dim, hidden_dim)
            ) for _ in range(n_stages)
        ])
        self.stage_weights = nn.Parameter(torch.ones(n_stages))
        self.output_proj = nn.Linear(hidden_dim, input_dim)

    def forward(self, x):
        h = self.initial_proj(x)
        stage_outputs = []
        residual = h
        weights = F.softmax(self.stage_weights, dim=0)

        for i, stage in enumerate(self.evolution_stages):
            stage_output = stage(residual)
            accumulated = stage_output if i == 0 else accumulated + stage_output * weights[i]
            stage_outputs.append(accumulated)
            residual = accumulated

        return self.output_proj(accumulated) + 0.1 * x, stage_outputs
