import numpy as np
import torch
import torch.nn as nn
import torch.nn.functional as F

# GMMComponent removed as it's not used in the main transformer

# Implementation of GMMClusterer with dynamic component determination
class GMMClusterer:
    """GMM-based clusterer with support for dynamic component determination"""
    def __init__(self, n_components=5, dynamic_components=False, min_components=2, max_components=15):
        self.n_components = n_components
        self.dynamic_components = dynamic_components
        self.min_components = min_components
        self.max_components = max_components
        self.bic_scores = None
        self.aic_scores = None
        self.silhouette_scores = None

    def _find_optimal_components(self, X):
        """Find optimal number of components using BIC, AIC and silhouette scores"""
        from sklearn.mixture import GaussianMixture
        from sklearn.metrics import silhouette_score, calinski_harabasz_score, davies_bouldin_score
        from sklearn.preprocessing import StandardScaler, RobustScaler
        from sklearn.cluster import KMeans
        from sklearn.decomposition import PCA
        import warnings

        # Suppress specific warnings that might occur during clustering
        warnings.filterwarnings("ignore", category=RuntimeWarning)

        # Use RobustScaler for better handling of outliers
        scaler = RobustScaler()
        X_scaled = scaler.fit_transform(X)

        # Apply PCA to reduce noise and improve clustering
        n_components_pca = min(X.shape[1], 10)  # Use at most 10 components
        if X.shape[1] > 2:
            pca = PCA(n_components=n_components_pca, random_state=42)
            X_pca = pca.fit_transform(X_scaled)
            # Use PCA data for clustering if it explains at least 80% of variance
            if sum(pca.explained_variance_ratio_) >= 0.8:
                X_for_clustering = X_pca
                print(f"Using PCA with {n_components_pca} components explaining {sum(pca.explained_variance_ratio_):.2f} of variance")
            else:
                X_for_clustering = X_scaled
        else:
            X_for_clustering = X_scaled

        # CRITICAL FIX: Ensure min_components is at least 2 (can't calculate silhouette with 1 component)
        self.min_components = max(2, self.min_components)

        # Limit max_components to a reasonable number based on data size
        max_components = min(self.max_components, X.shape[0] // 10, 15)
        max_components = max(max_components, self.min_components + 3)  # Ensure we test at least 3 more than min

        # Initialize score arrays
        bic_scores = []
        aic_scores = []
        silhouette_scores = []
        silhouette_scores_kmeans = []
        ch_scores = []  # Calinski-Harabasz scores
        db_scores = []  # Davies-Bouldin scores (lower is better)

        # Try different numbers of components
        component_range = list(range(self.min_components, max_components + 1))

        # CRITICAL FIX: Ensure we're testing multiple components
        if len(component_range) < 3:
            # Force testing more components if range is too small
            component_range = list(range(2, 7))  # Test 2-6 components
            print(f"WARNING: Component range too small, expanded to {component_range}")

        print(f"Finding optimal number of GMM components...")
        print(f"Testing component range: {component_range}")

        # Store GMM models and labels for later use
        gmm_models = {}
        gmm_labels = {}
        kmeans_labels = {}

        for n in component_range:
            # Fit GMM with multiple initializations and different covariance types
            best_gmm = None
            best_bic = np.inf

            for cov_type in ['full', 'tied', 'diag']:
                try:
                    gmm = GaussianMixture(
                        n_components=n,
                        random_state=42,
                        n_init=10,
                        init_params='kmeans',
                        max_iter=300,
                        reg_covar=1e-5,
                        covariance_type=cov_type
                    )
                    gmm.fit(X_for_clustering)

                    # Keep the model with the best BIC
                    current_bic = gmm.bic(X_for_clustering)
                    if current_bic < best_bic:
                        best_bic = current_bic
                        best_gmm = gmm
                except Exception as e:
                    print(f"Error fitting GMM with {n} components and {cov_type} covariance: {e}")
                    continue

            if best_gmm is None:
                # If all covariance types failed, try with spherical
                try:
                    best_gmm = GaussianMixture(
                        n_components=n,
                        random_state=42,
                        n_init=10,
                        init_params='kmeans',
                        max_iter=300,
                        reg_covar=1e-4,
                        covariance_type='spherical'
                    )
                    best_gmm.fit(X_for_clustering)
                    best_bic = best_gmm.bic(X_for_clustering)
                except Exception as e:
                    print(f"Error fitting GMM with {n} components and spherical covariance: {e}")
                    # Add placeholder scores
                    bic_scores.append(np.inf)
                    aic_scores.append(np.inf)
                    silhouette_scores.append(0)
                    silhouette_scores_kmeans.append(0)
                    ch_scores.append(0)
                    db_scores.append(np.inf)
                    continue

            # Store the best model
            gmm_models[n] = best_gmm

            # Get BIC and AIC scores
            bic_scores.append(best_bic)
            aic_scores.append(best_gmm.aic(X_for_clustering))

            # Get cluster labels
            labels_gmm = best_gmm.predict(X_for_clustering)
            gmm_labels[n] = labels_gmm

            # Also fit KMeans for comparison
            kmeans = KMeans(n_clusters=n, random_state=42, n_init=20, max_iter=500)
            labels_kmeans = kmeans.fit_predict(X_for_clustering)
            kmeans_labels[n] = labels_kmeans

            # Calculate silhouette scores and other metrics
            if len(np.unique(labels_gmm)) > 1:
                try:
                    # Sample data if it's too large (for efficiency)
                    if X_for_clustering.shape[0] > 5000:
                        sample_idx = np.random.choice(X_for_clustering.shape[0], 5000, replace=False)
                        X_sample = X_for_clustering[sample_idx]
                        labels_gmm_sample = labels_gmm[sample_idx]
                        sil_score = silhouette_score(X_sample, labels_gmm_sample)
                        ch_score = calinski_harabasz_score(X_sample, labels_gmm_sample)
                        db_score = davies_bouldin_score(X_sample, labels_gmm_sample)
                    else:
                        sil_score = silhouette_score(X_for_clustering, labels_gmm)
                        ch_score = calinski_harabasz_score(X_for_clustering, labels_gmm)
                        db_score = davies_bouldin_score(X_for_clustering, labels_gmm)

                    silhouette_scores.append(sil_score)
                    ch_scores.append(ch_score)
                    db_scores.append(db_score)
                except Exception as e:
                    print(f"Error calculating metrics for GMM with {n} components: {e}")
                    silhouette_scores.append(0)
                    ch_scores.append(0)
                    db_scores.append(np.inf)
            else:
                silhouette_scores.append(0)
                ch_scores.append(0)
                db_scores.append(np.inf)

            # Calculate silhouette score for KMeans
            if len(np.unique(labels_kmeans)) > 1:
                try:
                    if X_for_clustering.shape[0] > 5000:
                        sample_idx = np.random.choice(X_for_clustering.shape[0], 5000, replace=False)
                        sil_score_kmeans = silhouette_score(X_for_clustering[sample_idx], labels_kmeans[sample_idx])
                    else:
                        sil_score_kmeans = silhouette_score(X_for_clustering, labels_kmeans)
                    silhouette_scores_kmeans.append(sil_score_kmeans)
                except Exception as e:
                    print(f"Error calculating silhouette score for KMeans with {n} components: {e}")
                    silhouette_scores_kmeans.append(0)
            else:
                silhouette_scores_kmeans.append(0)

        # Store scores
        self.bic_scores = bic_scores
        self.aic_scores = aic_scores
        self.silhouette_scores = silhouette_scores
        self.silhouette_scores_kmeans = silhouette_scores_kmeans
        self.ch_scores = ch_scores
        self.db_scores = db_scores
        self.component_range = component_range

        # Find optimal number of components using different metrics
        if len(bic_scores) > 0 and not all(np.isinf(bic_scores)):
            valid_indices = [i for i, score in enumerate(bic_scores) if not np.isinf(score)]
            if valid_indices:
                bic_optimal = component_range[valid_indices[np.argmin([bic_scores[i] for i in valid_indices])]]
            else:
                bic_optimal = component_range[0]
        else:
            bic_optimal = component_range[0]

        if len(aic_scores) > 0 and not all(np.isinf(aic_scores)):
            valid_indices = [i for i, score in enumerate(aic_scores) if not np.isinf(score)]
            if valid_indices:
                aic_optimal = component_range[valid_indices[np.argmin([aic_scores[i] for i in valid_indices])]]
            else:
                aic_optimal = component_range[0]
        else:
            aic_optimal = component_range[0]

        # For silhouette, higher is better
        if len(silhouette_scores) > 0 and max(silhouette_scores) > 0:
            silhouette_optimal = component_range[np.argmax(silhouette_scores)]
        elif len(silhouette_scores_kmeans) > 0 and max(silhouette_scores_kmeans) > 0:
            silhouette_optimal = component_range[np.argmax(silhouette_scores_kmeans)]
        else:
            silhouette_optimal = bic_optimal

        # For Calinski-Harabasz, higher is better
        if len(ch_scores) > 0 and max(ch_scores) > 0:
            ch_optimal = component_range[np.argmax(ch_scores)]
        else:
            ch_optimal = bic_optimal

        # For Davies-Bouldin, lower is better
        if len(db_scores) > 0 and min(db_scores) < np.inf:
            db_optimal = component_range[np.argmin(db_scores)]
        else:
            db_optimal = bic_optimal

        # Implement a comprehensive weighted decision strategy
        # Normalize all scores to 0-1 range (higher is better)
        if len(component_range) > 1:
            # For BIC and AIC (lower is better), invert the normalization
            if not all(np.isinf(bic_scores)):
                valid_bic = [score for score in bic_scores if not np.isinf(score)]
                if valid_bic:
                    bic_min, bic_max = min(valid_bic), max(valid_bic)
                    norm_bic = [1.0 - (score - bic_min) / (bic_max - bic_min + 1e-10) if not np.isinf(score)
                               else 0.0 for score in bic_scores]
                else:
                    norm_bic = [0.0] * len(bic_scores)
            else:
                norm_bic = [0.0] * len(bic_scores)

            if not all(np.isinf(aic_scores)):
                valid_aic = [score for score in aic_scores if not np.isinf(score)]
                if valid_aic:
                    aic_min, aic_max = min(valid_aic), max(valid_aic)
                    norm_aic = [1.0 - (score - aic_min) / (aic_max - aic_min + 1e-10) if not np.isinf(score)
                               else 0.0 for score in aic_scores]
                else:
                    norm_aic = [0.0] * len(aic_scores)
            else:
                norm_aic = [0.0] * len(aic_scores)

            # For silhouette and CH (higher is better), normalize directly
            if max(silhouette_scores) > 0:
                sil_min, sil_max = min(silhouette_scores), max(silhouette_scores)
                norm_sil = [(score - sil_min) / (sil_max - sil_min + 1e-10) for score in silhouette_scores]
            else:
                norm_sil = [0.0] * len(silhouette_scores)

            if max(ch_scores) > 0:
                ch_min, ch_max = min(ch_scores), max(ch_scores)
                norm_ch = [(score - ch_min) / (ch_max - ch_min + 1e-10) for score in ch_scores]
            else:
                norm_ch = [0.0] * len(ch_scores)

            # For DB (lower is better), invert the normalization
            if not all(np.isinf(db_scores)):
                valid_db = [score for score in db_scores if not np.isinf(score)]
                if valid_db:
                    db_min, db_max = min(valid_db), max(valid_db)
                    norm_db = [1.0 - (score - db_min) / (db_max - db_min + 1e-10) if not np.isinf(score)
                              else 0.0 for score in db_scores]
                else:
                    norm_db = [0.0] * len(db_scores)
            else:
                norm_db = [0.0] * len(db_scores)

            # Combine scores with weights
            # BIC: 0.25, AIC: 0.15, Silhouette: 0.25, CH: 0.2, DB: 0.15
            combined_scores = [0.25 * nb + 0.15 * na + 0.25 * ns + 0.2 * nc + 0.15 * nd
                              for nb, na, ns, nc, nd in zip(norm_bic, norm_aic, norm_sil, norm_ch, norm_db)]

            # Find the optimal component number based on combined score
            weighted_optimal = component_range[np.argmax(combined_scores)]

            # Check if the weighted optimal is reasonable
            if weighted_optimal in component_range:
                optimal_n = weighted_optimal
            else:
                # Fallback to BIC
                optimal_n = bic_optimal
        else:
            # If we only have one component in the range
            optimal_n = component_range[0]
            weighted_optimal = optimal_n

        # Analyze cluster quality for the optimal number
        if optimal_n in gmm_labels:
            labels = gmm_labels[optimal_n]
            cluster_sizes = [np.sum(labels == i) for i in range(optimal_n)]
            min_cluster_size = min(cluster_sizes)
            max_cluster_size = max(cluster_sizes)

            # If the smallest cluster is too small (less than 5% of data), try another component number
            if min_cluster_size < 0.05 * len(labels) and len(component_range) > 1:
                print(f"Warning: Smallest cluster has only {min_cluster_size} samples ({min_cluster_size/len(labels)*100:.1f}%)")

                # Try to find a better number of components with more balanced clusters
                balanced_scores = []
                for n in component_range:
                    if n in gmm_labels:
                        n_labels = gmm_labels[n]
                        n_sizes = [np.sum(n_labels == i) for i in range(n)]
                        n_min_size = min(n_sizes) if n_sizes else 0
                        balance_score = n_min_size / len(n_labels) if len(n_labels) > 0 else 0
                        balanced_scores.append(balance_score)
                    else:
                        balanced_scores.append(0)

                # Find component number with best balance and reasonable silhouette
                balance_sil_scores = [bs * (ss + 0.1) for bs, ss in zip(balanced_scores, silhouette_scores)]
                if max(balance_sil_scores) > 0:
                    balanced_optimal = component_range[np.argmax(balance_sil_scores)]

                    # Only use balanced optimal if it's significantly better balanced
                    if balanced_optimal != optimal_n and balanced_scores[component_range.index(balanced_optimal)] > 1.5 * balanced_scores[component_range.index(optimal_n)]:
                        print(f"Switching to more balanced component number: {balanced_optimal}")
                        optimal_n = balanced_optimal

        # Store the optimal model
        if optimal_n in gmm_models:
            self.gmm = gmm_models[optimal_n]

        # Print detailed information
        print(f"Optimal number of GMM components determined: {optimal_n}")
        print(f"  BIC suggested: {bic_optimal} (scores: {[f'{score:.2f}' if not np.isinf(score) else 'inf' for score in bic_scores]})")
        print(f"  AIC suggested: {aic_optimal} (scores: {[f'{score:.2f}' if not np.isinf(score) else 'inf' for score in aic_scores]})")
        print(f"  Silhouette suggested: {silhouette_optimal} (scores: {[f'{score:.4f}' for score in silhouette_scores]})")
        print(f"  Calinski-Harabasz suggested: {ch_optimal} (scores: {[f'{score:.2f}' for score in ch_scores]})")
        print(f"  Davies-Bouldin suggested: {db_optimal} (scores: {[f'{score:.4f}' if not np.isinf(score) else 'inf' for score in db_scores]})")
        print(f"  Weighted approach suggested: {weighted_optimal}")

        return optimal_n

    def fit_predict(self, X):
        """Fit GMM and predict cluster labels"""
        from sklearn.mixture import GaussianMixture

        # Determine optimal number of components if requested
        if self.dynamic_components:
            self.n_components = self._find_optimal_components(X)

        # Fit GMM
        self.gmm = GaussianMixture(n_components=self.n_components, random_state=42, n_init=5)
        self.gmm.fit(X)

        # Predict labels
        return self.gmm.predict(X)

    def get_component_info(self):
        """Get component information"""
        if not hasattr(self, 'gmm'):
            return None

        info = {
            'n_components': self.n_components,
            'weights': self.gmm.weights_,
            'means': self.gmm.means_,
            'covariances': self.gmm.covariances_,
            'bic_scores': self.bic_scores,
            'aic_scores': self.aic_scores,
            'silhouette_scores': self.silhouette_scores,
            'component_range': self.component_range if hasattr(self, 'component_range') else None
        }

        # Add additional metrics if available
        if hasattr(self, 'silhouette_scores_kmeans'):
            info['silhouette_scores_kmeans'] = self.silhouette_scores_kmeans

        if hasattr(self, 'ch_scores'):
            info['ch_scores'] = self.ch_scores

        if hasattr(self, 'db_scores'):
            info['db_scores'] = self.db_scores

        return info

class TorchGMMLayer(nn.Module):
    def __init__(self, n_components, input_dim, covariance_type='diagonal'):
        super(TorchGMMLayer, self).__init__()
        self.n_components = n_components
        self.input_dim = input_dim
        self.covariance_type = covariance_type

        means_init = torch.zeros(n_components, input_dim)
        for i in range(n_components):
            means_init[i] = torch.randn(input_dim) * (0.5 + i * 0.5 / n_components)

        self.weights = nn.Parameter(torch.ones(n_components) / n_components)
        self.means = nn.Parameter(means_init)
        self.covs = nn.Parameter(torch.ones(n_components, input_dim))

    def forward(self, X):
        weights = F.softmax(self.weights, dim=0)
        var = self.covs**2 + 1e-6
        diff = X.unsqueeze(1) - self.means.unsqueeze(0)

        log_probs = -0.5 * torch.sum(
            (diff**2) / var.unsqueeze(0) + torch.log(var).unsqueeze(0),
            dim=2
        )

        log_probs = log_probs + torch.log(weights + 1e-10)
        max_log_probs = torch.max(log_probs, dim=1, keepdim=True)[0]
        log_likelihood = max_log_probs.squeeze() + torch.log(
            torch.sum(torch.exp(log_probs - max_log_probs), dim=1)
        )

        return log_probs, log_likelihood

    def get_component_weights(self):
        return torch.softmax(self.weights, dim=0).detach().cpu().numpy()

    def get_component_means(self):
        return self.means.detach().cpu().numpy()

    def get_component_covariances(self):
        if self.covariance_type == 'full':
            covs = []
            for k in range(self.n_components):
                cov_k = self.covs[k].detach().cpu()
                cov_k = torch.matmul(cov_k, cov_k.t()) + torch.eye(self.input_dim) * 1e-6
                covs.append(cov_k.numpy())
            return np.array(covs)
        else:
            return self.covs.detach().cpu().numpy()**2
