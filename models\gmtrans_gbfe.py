import os,torch,numpy as np
from torch import nn
from torch.nn import functional as F
from sklearn.base import BaseEstimator,RegressorMixin
from models.gmm_component import TorchGMMLayer
from models.transformer_component import TransformerEncoder,ContrastiveLoss
from models.gb_component import FeatureEvolutionLayer

# Import feature evolution components
try: from models.xgb_component import XGBFeatureEvolutionLayer; XGBOOST_AVAILABLE=True
except ImportError: XGBOOST_AVAILABLE=False

try: from models.lgb_component import LGBFeatureEvolutionLayer; LIGHTGBM_AVAILABLE=True
except ImportError: LIGHTGBM_AVAILABLE=False

try: from models.ensemble_feature_evolution import EnsembleFeatureEvolutionLayer; ENSEMBLE_AVAILABLE=True
except ImportError: ENSEMBLE_AVAILABLE=False

class GMTransGBFE(nn.Module):
    def __init__(self,input_dim,output_dim,n_components=4,d_model=96,
                n_layers=2,n_heads=4,dim_feedforward=128,dropout=0.15,
                n_evolution_stages=2,max_seq_length=100,l2_reg=0.005,
                feature_selection_threshold=0.02,use_xgboost=True,
                use_lightgbm=True,use_ensemble=True,dynamic_components=False):
        super().__init__()
        self.input_dim,self.output_dim,self.n_components,self.d_model=input_dim,output_dim,n_components,d_model
        self.l2_reg,self.feature_selection_threshold=l2_reg,feature_selection_threshold
        self.dynamic_components = dynamic_components

        # Feature evolution options
        self.use_xgboost = use_xgboost and XGBOOST_AVAILABLE
        self.use_lightgbm = use_lightgbm and LIGHTGBM_AVAILABLE
        self.use_ensemble = use_ensemble and ENSEMBLE_AVAILABLE

        # If ensemble is requested but not available, fall back to individual models
        if self.use_ensemble and not ENSEMBLE_AVAILABLE:
            self.use_ensemble = False
            self.use_xgboost = XGBOOST_AVAILABLE
            self.use_lightgbm = LIGHTGBM_AVAILABLE
            print("Ensemble feature evolution not available, falling back to individual models")

        self.feature_selector=nn.Parameter(torch.ones(input_dim)*0.7)
        self.input_norm=nn.BatchNorm1d(input_dim,eps=1e-4,momentum=0.1)

        # If dynamic components is enabled, we'll determine the optimal number during initialization
        if dynamic_components:
            try:
                from models.gmm_component import GMMClusterer
                import numpy as np
                from sklearn.preprocessing import StandardScaler

                print(f"Determining optimal number of GMM components...")

                # Create a better random sample for component determination
                # Use a mixture of distributions to better simulate real data
                sample_size = min(3000, max(1000, 30 * input_dim))

                # Generate data from multiple distributions with different characteristics
                n_distributions = min(8, input_dim)  # More distributions for higher dimensions
                samples_per_dist = sample_size // n_distributions
                random_sample = []

                # Create a variety of distribution types
                for i in range(n_distributions):
                    # Vary the distribution characteristics
                    if i % 3 == 0:
                        # Spherical clusters
                        mean = np.random.uniform(-5, 5, input_dim)
                        var = np.random.uniform(0.5, 1.5)
                        cov = np.eye(input_dim) * var
                    elif i % 3 == 1:
                        # Elongated clusters
                        mean = np.random.uniform(-5, 5, input_dim)
                        variances = np.random.uniform(0.1, 3.0, input_dim)
                        cov = np.diag(variances)
                    else:
                        # Correlated features
                        mean = np.random.uniform(-5, 5, input_dim)
                        cov = np.random.randn(input_dim, input_dim)
                        cov = np.dot(cov, cov.T)  # Make positive semi-definite
                        # Scale down to reasonable values
                        cov = cov / (10 * np.max(np.abs(cov))) + np.diag(np.random.uniform(0.5, 1.5, input_dim))

                    # Generate samples from this distribution
                    try:
                        dist_samples = np.random.multivariate_normal(
                            mean, cov, samples_per_dist
                        )
                        random_sample.append(dist_samples)
                    except np.linalg.LinAlgError:
                        # Fallback if covariance matrix is not valid
                        fallback_cov = np.diag(np.random.uniform(0.5, 2.0, input_dim))
                        dist_samples = np.random.multivariate_normal(
                            mean, fallback_cov, samples_per_dist
                        )
                        random_sample.append(dist_samples)

                # Combine all samples
                random_sample = np.vstack(random_sample)

                # Add some outliers
                outlier_count = int(sample_size * 0.08)  # 8% outliers
                outliers = np.random.uniform(-15, 15, (outlier_count, input_dim))
                random_sample = np.vstack([random_sample, outliers])

                # Add some noise
                random_sample += np.random.normal(0, 0.2, random_sample.shape)

                # Standardize the sample
                from sklearn.preprocessing import RobustScaler
                scaler = RobustScaler()  # Better handling of outliers
                random_sample = scaler.fit_transform(random_sample)

                # Add some missing values (NaN) and replace with zeros
                nan_mask = np.random.random(random_sample.shape) < 0.05  # 5% NaN values
                random_sample[nan_mask] = np.nan
                random_sample = np.nan_to_num(random_sample)

                # Use GMMClusterer to find optimal components
                gmm_clusterer = GMMClusterer(
                    n_components=n_components,
                    dynamic_components=True,
                    min_components=min(max(2, input_dim // 4), 5),  # Ensure min_components is reasonable but at least 2
                    max_components=min(15, input_dim // 2, sample_size // 20)  # Ensure max_components is reasonable
                )
                gmm_clusterer.fit_predict(random_sample)

                # Get the optimal number of components
                component_info = gmm_clusterer.get_component_info()
                if component_info and 'n_components' in component_info:
                    self.n_components = component_info['n_components']
                    print(f"Optimal number of GMM components determined: {self.n_components}")
                else:
                    print(f"Could not determine optimal components, using default: {n_components}")
            except Exception as e:
                print(f"Error determining optimal components: {e}")
                print(f"Using default number of components: {n_components}")

        # Initialize GMM layer with the determined number of components
        self.gmm_layer=TorchGMMLayer(self.n_components, input_dim, covariance_type='diagonal')

        self.input_embedding=nn.Linear(input_dim,d_model)
        nn.init.xavier_uniform_(self.input_embedding.weight)
        nn.init.zeros_(self.input_embedding.bias)

        # Choose the appropriate feature evolution layer
        if self.use_ensemble:
            print("Using Ensemble Feature Evolution with XGBoost and LightGBM")
            self.feature_evolution = EnsembleFeatureEvolutionLayer(
                input_dim, d_model, n_evolution_stages, dropout,
                use_xgboost=self.use_xgboost, use_lightgbm=self.use_lightgbm
            )
        else:
            # Fallback to GradBoostNN feature evolution if ensemble is not requested
            print("Using GradBoostNN Feature Evolution")
            self.feature_evolution = FeatureEvolutionLayer(
                input_dim, d_model, n_evolution_stages, dropout
            )

        self.transformer=TransformerEncoder(d_model,n_components,n_layers,n_heads,
                                          dim_feedforward,dropout,max_seq_length)

        half_d=d_model//2
        layer_config=[nn.Linear(d_model,half_d),nn.ReLU(),nn.Dropout(dropout),
                     nn.BatchNorm1d(half_d,eps=1e-4),nn.Linear(half_d,output_dim)]

        self.output_layer=nn.Sequential(*layer_config)
        self.uncertainty_layer=nn.Sequential(*layer_config)

        for layer in [self.output_layer,self.uncertainty_layer]:
            nn.init.xavier_uniform_(layer[0].weight)
            nn.init.zeros_(layer[0].bias)
            nn.init.xavier_uniform_(layer[-1].weight)

        nn.init.constant_(self.output_layer[-1].bias,0.1)
        nn.init.constant_(self.uncertainty_layer[-1].bias,0.2)

        self.contrastive_loss=ContrastiveLoss(temperature=0.1)

    def forward(self,x,return_attention=False):
        mask=torch.sigmoid(self.feature_selector)
        norm_input=self.input_norm(x*mask)

        gmm_probs=torch.exp(self.gmm_layer(norm_input)[0])
        evolved_features=self.feature_evolution(norm_input)[0]
        trans_input=self.input_embedding(evolved_features).unsqueeze(1)

        trans_out,attn_w,comp_attn=self.transformer(trans_input,gmm_probs)
        features=trans_out[:,-1]

        out=self.output_layer(features)*(1.0+0.1*torch.sigmoid(out:=self.output_layer(features)*2))
        uncert=F.softplus(self.uncertainty_layer(features))

        self.l2_reg_loss=self.l2_reg*(torch.norm(self.input_embedding.weight)+torch.norm(self.output_layer[-1].weight))

        return (out,uncert,(attn_w,comp_attn)) if return_attention else (out,uncert)

    def get_contrastive_loss(self,x,labels=None):
        # Remove torch.no_grad() to allow gradients to flow
        mask=torch.sigmoid(self.feature_selector)
        norm_input=self.input_norm(x*mask)
        gmm_probs=torch.exp(self.gmm_layer(norm_input)[0])
        evolved=self.feature_evolution(norm_input)[0]
        trans_in=self.input_embedding(evolved).unsqueeze(1)
        features=self.transformer(trans_in,gmm_probs)[0][:,-1]

        # Create a simple data augmentation by adding small noise
        noise = torch.randn_like(features) * 0.05
        features_aug = features + noise

        # Combine original and augmented features for contrastive learning
        combined_features = torch.cat([features, features_aug], dim=0)

        # Create pseudo-labels for contrastive learning
        batch_size = features.size(0)
        pseudo_labels = torch.arange(batch_size, device=features.device).repeat(2)

        return self.contrastive_loss(combined_features, pseudo_labels) + 0.01*torch.mean(torch.relu(self.feature_selection_threshold-mask))

    def get_feature_importance(self,visualize=False,feature_names=None,top_n=10):
        raw_imp=torch.sigmoid(self.feature_selector)
        dev=self.feature_selector.device

        if self.input_dim<=10:
            imp=raw_imp.detach().cpu().numpy()
            imp=imp/np.sum(imp) if not np.any(np.isnan(imp)) else np.ones_like(imp)/len(imp)
        else:
            try:
                with torch.no_grad():
                    gmm_imp=torch.std(self.gmm_layer.means.detach(),dim=0)+1e-6
                    rand_in=torch.randn(min(10,100),self.input_dim).to(dev)
                    _,_,(attn_w,_)=self(rand_in,return_attention=True)
                    attn_imp=torch.mean(torch.cat([a.mean(dim=1) for a in attn_w]),dim=0).squeeze().detach()

                comb=raw_imp*(1+0.2*gmm_imp+0.1*attn_imp)
                imp=(comb/torch.sum(comb)).detach().cpu().numpy()

                if np.any(np.isnan(imp)):
                    raw_np=raw_imp.detach().cpu().numpy()
                    imp=raw_np/np.sum(raw_np)
            except Exception as e:
                print(f"Error in feature importance: {e}")
                imp=np.ones(self.input_dim)/self.input_dim

        if visualize:
            top_idx=np.argsort(imp)[-(min(top_n,len(imp))):][::-1]
            top_imp=imp[top_idx]
            return top_idx,top_imp

        return imp

    def visualize_feature_importance(self,feature_names=None,top_n=10):
        return self.get_feature_importance(visualize=True,feature_names=feature_names,top_n=top_n)

class GMTransGBFEWrapper(BaseEstimator,RegressorMixin):
    def __init__(self,input_dim,output_dim,n_components=4,d_model=96,
                n_layers=2,n_heads=4,dim_feedforward=128,dropout=0.15,
                n_evolution_stages=2,max_seq_length=100,l2_reg=0.005,
                feature_selection_threshold=0.02,learning_rate=0.002,
                batch_size=32,epochs=250,patience=25,min_delta=0.0005,
                lr_scheduler=True,lr_factor=0.6,lr_patience=12,
                lr_scheduler_type='one_cycle',lr_warmup_epochs=8,
                lr_min=5e-6,lr_max=0.01,lr_cycle_epochs=30,
                use_xgboost=False,use_lightgbm=False,use_ensemble=True,
                dynamic_components=True,min_components=2,max_components=10,
                device='cuda' if torch.cuda.is_available() else 'cpu'):
        for k,v in locals().items():
            if k!='self': setattr(self,k,v)

        self.model=GMTransGBFE(
            input_dim,output_dim,n_components,d_model,
            n_layers,n_heads,dim_feedforward,dropout,
            n_evolution_stages,max_seq_length,l2_reg,
            feature_selection_threshold,
            use_xgboost=use_xgboost,
            use_lightgbm=use_lightgbm,
            use_ensemble=use_ensemble,
            dynamic_components=dynamic_components
        ).to(device)

        self.xgb_model=None
        self.optimizer=torch.optim.AdamW(
            self.model.parameters(),lr=learning_rate,weight_decay=0.008,
            betas=(0.9,0.999),amsgrad=True
        )

        self.scheduler = None
        if lr_scheduler:
            if lr_scheduler_type == 'plateau':
                self.scheduler = torch.optim.lr_scheduler.ReduceLROnPlateau(
                    self.optimizer, mode='min', factor=lr_factor,
                    patience=lr_patience, verbose=True, threshold=min_delta
                )
            elif lr_scheduler_type == 'cosine':
                self.scheduler = torch.optim.lr_scheduler.CosineAnnealingLR(
                    self.optimizer, T_max=epochs, eta_min=lr_min
                )
            elif lr_scheduler_type == 'cyclic':
                self.scheduler = torch.optim.lr_scheduler.CyclicLR(
                    self.optimizer, base_lr=lr_min, max_lr=lr_max,
                    step_size_up=lr_cycle_epochs//2, cycle_momentum=False
                )
            elif lr_scheduler_type == 'one_cycle':
                self.scheduler = torch.optim.lr_scheduler.OneCycleLR(
                    self.optimizer, max_lr=lr_max, total_steps=epochs,
                    pct_start=0.3, div_factor=25.0, final_div_factor=10000.0
                )
            elif lr_scheduler_type == 'warmup_cosine':
                # Custom warmup + cosine annealing
                warmup_scheduler = torch.optim.lr_scheduler.LinearLR(
                    self.optimizer, start_factor=0.1, end_factor=1.0, total_iters=lr_warmup_epochs
                )
                cosine_scheduler = torch.optim.lr_scheduler.CosineAnnealingLR(
                    self.optimizer, T_max=epochs-lr_warmup_epochs, eta_min=lr_min
                )
                self.scheduler = torch.optim.lr_scheduler.SequentialLR(
                    self.optimizer, schedulers=[warmup_scheduler, cosine_scheduler],
                    milestones=[lr_warmup_epochs]
                )

        self.criterion=nn.MSELoss()
        self.history={k:[] for k in ['train_loss','val_loss','contrastive_loss',
                                    'feature_importance','learning_rate']}

    def fit(self, X, y, X_val=None, y_val=None):
        X_tensor = torch.FloatTensor(X).to(self.device)
        y_tensor = torch.FloatTensor(y).to(self.device)

        if X_val is None or y_val is None:
            val_size = min(0.2, 5 / len(X))
            n_val = max(2, int(len(X) * val_size))

            indices = torch.randperm(len(X_tensor))
            train_indices = indices[n_val:]
            val_indices = indices[:n_val]

            X_train_tensor = X_tensor[train_indices]
            y_train_tensor = y_tensor[train_indices]
            X_val_tensor = X_tensor[val_indices]
            y_val_tensor = y_tensor[val_indices]
        else:
            X_train_tensor = X_tensor
            y_train_tensor = y_tensor
            X_val_tensor = torch.FloatTensor(X_val).to(self.device)
            y_val_tensor = torch.FloatTensor(y_val).to(self.device)

        batch_size = min(self.batch_size, len(X_train_tensor) // 2)
        batch_size = max(1, batch_size)

        dataset = torch.utils.data.TensorDataset(X_train_tensor, y_train_tensor)
        dataloader = torch.utils.data.DataLoader(dataset, batch_size=batch_size, shuffle=True)

        best_val_loss = float('inf')
        best_model_state = None
        patience_counter = 0

        self.model.train()
        for epoch in range(self.epochs):
            epoch_loss = 0.0
            contrastive_loss_sum = 0.0
            l2_reg_loss_sum = 0.0

            # Calculate total number of batches for progress reporting
            total_batches = len(dataloader)

            for batch_idx, (batch_X, batch_y) in enumerate(dataloader):
                # Print batch progress
                print(f'Epoch {epoch+1}/{self.epochs}, Batch {batch_idx+1}/{total_batches} (Size: {batch_X.shape[0]})')

                self.optimizer.zero_grad()

                outputs, uncertainties = self.model(batch_X)

                outputs_clipped = torch.clamp(outputs, -5.0, 5.0)
                batch_y_clipped = torch.clamp(batch_y, -5.0, 5.0)

                mse_loss = self.criterion(outputs_clipped, batch_y_clipped)

                error = outputs_clipped - batch_y_clipped
                target_scale = 1.0 + 0.5 * torch.sigmoid(batch_y_clipped * 5)
                error_clipped = torch.clamp(error, -2.0, 2.0)

                asymmetric_penalty = torch.mean(torch.where(
                    error_clipped < 0,
                    torch.abs(error_clipped) * 1.5 * target_scale,
                    torch.abs(error_clipped)
                ))

                clipped_uncertainties = torch.clamp(uncertainties, -2.0, 2.0)
                squared_errors = torch.clamp((outputs_clipped - batch_y_clipped)**2, 0.0, 10.0)

                uncertainty_loss = torch.mean(
                    0.5 * torch.exp(torch.clamp(-clipped_uncertainties, -5.0, 5.0)) * squared_errors +
                    0.5 * clipped_uncertainties
                )

                try:
                    contrastive_loss = self.model.get_contrastive_loss(batch_X)
                    # Use a higher upper bound to allow more dynamic values
                    contrastive_loss = torch.clamp(contrastive_loss, 0.0, 20.0)
                except Exception as e:
                    print(f"Contrastive loss error: {e}")
                    contrastive_loss = torch.tensor(0.1, device=outputs.device)

                l2_reg_loss = getattr(self.model, 'l2_reg_loss', 0.0)
                if not isinstance(l2_reg_loss, torch.Tensor):
                    l2_reg_loss = torch.tensor(0.0, device=outputs.device)

                mse_loss_clipped = torch.clamp(mse_loss, 0.0, 10.0)
                asymmetric_penalty_clipped = torch.clamp(asymmetric_penalty, 0.0, 10.0)
                uncertainty_loss_clipped = torch.clamp(uncertainty_loss, 0.0, 10.0)
                l2_reg_loss_clipped = torch.clamp(l2_reg_loss, 0.0, 1.0)

                loss = mse_loss_clipped + 0.15 * asymmetric_penalty_clipped + 0.03 * uncertainty_loss_clipped
                # Increase contrastive loss weight to make it more impactful
                loss = loss + 0.05 * contrastive_loss + 0.002 * l2_reg_loss_clipped

                if not torch.isfinite(loss):
                    loss = mse_loss_clipped

                loss.backward()
                torch.nn.utils.clip_grad_norm_(self.model.parameters(), max_norm=1.0)
                self.optimizer.step()

                loss_value = loss.item()
                if np.isnan(loss_value) or np.isinf(loss_value) or loss_value > 1e6:
                    loss_value = 1000.0

                mse_value = mse_loss.item()
                epoch_loss += mse_value * batch_X.size(0)

                contrastive_loss_sum += contrastive_loss.item() * batch_X.size(0)
                if isinstance(l2_reg_loss, torch.Tensor):
                    l2_reg_loss_sum += l2_reg_loss.item() * batch_X.size(0)

            epoch_loss /= len(dataset)
            contrastive_loss_avg = contrastive_loss_sum / len(dataset)

            self.model.eval()
            with torch.no_grad():
                outputs, uncertainties = self.model(X_val_tensor)

                val_pred = outputs.cpu().numpy()
                val_true = y_val_tensor.cpu().numpy()
                val_rmse = np.sqrt(np.mean((val_pred - val_true) ** 2))
                val_loss = val_rmse
            self.model.train()

            current_lr = self.optimizer.param_groups[0]['lr']
            feature_importance = self.model.get_feature_importance()

            self.history['train_loss'].append(epoch_loss)
            self.history['val_loss'].append(val_loss)
            self.history['contrastive_loss'].append(contrastive_loss_avg)
            self.history['feature_importance'].append(feature_importance)
            self.history['learning_rate'].append(current_lr)

            if (epoch + 1) % 10 == 0 or epoch == 0:
                train_rmse = np.sqrt(epoch_loss)
                print(f'Epoch {epoch+1}/{self.epochs} SUMMARY: Train MSE: {epoch_loss:.4f}, Train RMSE: {train_rmse:.4f}, '
                      f'Val RMSE: {val_loss:.4f}, LR: {current_lr:.6f}, Batches: {total_batches}, Batch Size: {batch_size}')

            if self.lr_scheduler:
                if self.lr_scheduler_type == 'plateau':
                    self.scheduler.step(val_loss)
                elif self.lr_scheduler_type in ['cosine', 'cyclic', 'one_cycle', 'warmup_cosine']:
                    self.scheduler.step()

            if val_loss < best_val_loss - self.min_delta:
                best_val_loss = val_loss
                best_model_state = self.model.state_dict().copy()
                patience_counter = 0
            else:
                patience_counter += 1

            if patience_counter >= self.patience:
                print(f'Early stopping at epoch {epoch+1}')
                break

        if best_model_state is not None:
            self.model.load_state_dict(best_model_state)
            print(f'Restored best model with validation loss: {best_val_loss:.4f}')

        return self

    def predict(self, X, return_uncertainty=False):
        X_tensor = torch.FloatTensor(X).to(self.device)
        self.model.eval()

        try:
            with torch.no_grad():
                outputs, uncertainties = self.model(X_tensor)
                outputs = F.relu(outputs)
                y_pred = outputs.cpu().numpy()
                uncertainties = uncertainties.cpu().numpy()
                return (y_pred, uncertainties) if return_uncertainty else y_pred
        except Exception as e:
            print(f"Error during prediction: {e}")
            return (np.zeros((X.shape[0], self.output_dim)), np.ones((X.shape[0], self.output_dim))) if return_uncertainty else np.zeros((X.shape[0], self.output_dim))

    def predict_with_attention(self, X):
        X_tensor = torch.FloatTensor(X).to(self.device)
        self.model.eval()

        try:
            with torch.no_grad():
                outputs, uncertainties, attention_weights = self.model(X_tensor, return_attention=True)

            y_pred = outputs.cpu().numpy()
            uncertainties = uncertainties.cpu().numpy()

            attn_weights, component_attn = attention_weights
            processed_attn = [attn.cpu().numpy() for attn in attn_weights]
            processed_comp_attn = [comp_attn.cpu().numpy() for comp_attn in component_attn]

            return y_pred, uncertainties, (processed_attn, processed_comp_attn)

        except Exception as e:
            print(f"Error during prediction with attention: {e}")
            dummy_attn = np.zeros((X.shape[0], 1, 1))
            dummy_comp_attn = np.zeros((X.shape[0], self.n_components))
            return (
                np.zeros((X.shape[0], self.output_dim)),
                np.ones((X.shape[0], self.output_dim)),
                ([dummy_attn], [dummy_comp_attn])
            )

    def get_feature_importance(self,X=None,visualize=False,feature_names=None,top_n=10):
        if X is None:
            imp=self.model.get_feature_importance()
        else:
            try:
                X_tensor=torch.FloatTensor(X).to(self.device)
                X_tensor.requires_grad=True
                self.model.eval()
                sel_imp=self.model.get_feature_importance()

                outputs,_=self.model(X_tensor)
                grads=[]
                for i in range(outputs.shape[1]):
                    self.model.zero_grad()
                    outputs[:,i].sum().backward(retain_graph=(i<outputs.shape[1]-1))
                    grads.append(X_tensor.grad.abs().mean(dim=0).cpu().numpy())

                grad_imp=np.mean(grads,axis=0)/np.sum(np.mean(grads,axis=0))
                imp=0.7*sel_imp+0.3*grad_imp
                imp=imp/np.sum(imp)
            except Exception as e:
                print(f"Error in gradient importance: {e}")
                imp=self.model.get_feature_importance()

        if visualize:
            top_idx=np.argsort(imp)[-(min(top_n,len(imp))):][::-1]
            top_imp=imp[top_idx]
            return top_idx,top_imp

        return imp

    def visualize_feature_importance(self, feature_names=None, top_n=10, X=None):
        return self.get_feature_importance(X, visualize=True, feature_names=feature_names, top_n=top_n)

    def save_model(self,path):
        os.makedirs(os.path.dirname(path),exist_ok=True)
        params={k:getattr(self,k) for k in ['input_dim','output_dim','n_components','d_model',
                                          'n_layers','n_heads','dim_feedforward','dropout',
                                          'n_evolution_stages','max_seq_length','l2_reg',
                                          'feature_selection_threshold','learning_rate',
                                          'batch_size','epochs','patience','min_delta',
                                          'lr_scheduler','lr_factor','lr_patience',
                                          'use_xgboost','use_lightgbm','use_ensemble',
                                          'dynamic_components','min_components','max_components']}
        torch.save({'model_state_dict':self.model.state_dict(),'hyperparams':params,'version':'2.2'},path)
        print(f"Model saved to {path}")

    def load_model(self,path):
        try:
            cp=torch.load(path,map_location=self.device,weights_only=False)

            for k,v in cp['hyperparams'].items(): setattr(self,k,v)

            for k,v in {'l2_reg':0.01,'feature_selection_threshold':0.01,'patience':20,
                       'min_delta':0.001,'lr_scheduler':True,'lr_factor':0.5,'lr_patience':10,
                       'use_xgboost':False,'use_lightgbm':False,'use_ensemble':True,
                       'dynamic_components':False,'min_components':2,'max_components':10}.items():
                if not hasattr(self,k) or getattr(self,k) is None: setattr(self,k,v)

            self.model=GMTransGBFE(
                self.input_dim,self.output_dim,self.n_components,self.d_model,
                self.n_layers,self.n_heads,self.dim_feedforward,self.dropout,
                self.n_evolution_stages,self.max_seq_length,self.l2_reg,
                self.feature_selection_threshold,
                use_xgboost=self.use_xgboost,
                use_lightgbm=self.use_lightgbm,
                use_ensemble=self.use_ensemble,
                dynamic_components=self.dynamic_components
            ).to(self.device)

            self.is_cpu_usage=True
            self.model.load_state_dict(cp['model_state_dict'])

            self.optimizer=torch.optim.AdamW(
                self.model.parameters(),lr=self.learning_rate,
                weight_decay=0.01,amsgrad=True
            )

            # Recreate scheduler if needed
            self.scheduler = None
            if hasattr(self, 'lr_scheduler') and self.lr_scheduler:
                if hasattr(self, 'lr_scheduler_type'):
                    lr_scheduler_type = self.lr_scheduler_type
                else:
                    lr_scheduler_type = 'plateau'  # Default for backward compatibility

                if lr_scheduler_type == 'plateau':
                    self.scheduler = torch.optim.lr_scheduler.ReduceLROnPlateau(
                        self.optimizer, mode='min', factor=self.lr_factor,
                        patience=self.lr_patience, verbose=True, threshold=self.min_delta
                    )
                elif lr_scheduler_type == 'cosine':
                    self.scheduler = torch.optim.lr_scheduler.CosineAnnealingLR(
                        self.optimizer, T_max=self.epochs, eta_min=self.lr_min
                    )
                elif lr_scheduler_type == 'cyclic':
                    self.scheduler = torch.optim.lr_scheduler.CyclicLR(
                        self.optimizer, base_lr=self.lr_min, max_lr=self.lr_max,
                        step_size_up=self.lr_cycle_epochs//2, cycle_momentum=False
                    )
                elif lr_scheduler_type == 'one_cycle':
                    self.scheduler = torch.optim.lr_scheduler.OneCycleLR(
                        self.optimizer, max_lr=self.lr_max, total_steps=self.epochs,
                        pct_start=0.3, div_factor=25.0, final_div_factor=10000.0
                    )
                elif lr_scheduler_type == 'warmup_cosine':
                    # Custom warmup + cosine annealing
                    warmup_scheduler = torch.optim.lr_scheduler.LinearLR(
                        self.optimizer, start_factor=0.1, end_factor=1.0, total_iters=self.lr_warmup_epochs
                    )
                    cosine_scheduler = torch.optim.lr_scheduler.CosineAnnealingLR(
                        self.optimizer, T_max=self.epochs-self.lr_warmup_epochs, eta_min=self.lr_min
                    )
                    self.scheduler = torch.optim.lr_scheduler.SequentialLR(
                        self.optimizer, schedulers=[warmup_scheduler, cosine_scheduler],
                        milestones=[self.lr_warmup_epochs]
                    )

            self.history=cp.get('history',{k:[] for k in ['train_loss','val_loss',
                                                        'contrastive_loss','feature_importance',
                                                        'learning_rate']})
            print(f"Model loaded from {path}")

        except Exception as e:
            print(f"Error loading model: {e}")
            self.model=GMTransGBFE(
                self.input_dim,self.output_dim,self.n_components,self.d_model,
                self.n_layers,self.n_heads,self.dim_feedforward,self.dropout,
                self.n_evolution_stages,self.max_seq_length,self.l2_reg,
                self.feature_selection_threshold,
                use_xgboost=self.use_xgboost,
                use_lightgbm=self.use_lightgbm,
                use_ensemble=self.use_ensemble,
                dynamic_components=self.dynamic_components
            ).to(self.device)
            self.is_cpu_usage=True
