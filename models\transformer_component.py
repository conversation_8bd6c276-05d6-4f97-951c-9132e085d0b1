"""
Transformer component for GMTrans-GBFE
"""
import torch
import torch.nn as nn
import torch.nn.functional as F
import numpy as np
import math

class PositionalEncoding(nn.Module):
    """Positional encoding for transformer"""

    def __init__(self, d_model, max_seq_length=100):
        """Initialize positional encoding

        Args:
            d_model: Embedding dimension
            max_seq_length: Maximum sequence length
        """
        super(PositionalEncoding, self).__init__()

        # Create positional encoding matrix
        pe = torch.zeros(max_seq_length, d_model)
        position = torch.arange(0, max_seq_length, dtype=torch.float).unsqueeze(1)
        div_term = torch.exp(
            torch.arange(0, d_model, 2).float() * (-math.log(10000.0) / d_model)
        )

        pe[:, 0::2] = torch.sin(position * div_term)
        pe[:, 1::2] = torch.cos(position * div_term)
        pe = pe.unsqueeze(0)

        # Register buffer (not a parameter, but part of the module)
        self.register_buffer('pe', pe)

    def forward(self, x):
        """Add positional encoding to input

        Args:
            x: Input tensor of shape (batch_size, seq_length, d_model)

        Returns:
            x: Output tensor with positional encoding added
        """
        x = x + self.pe[:, :x.size(1), :]
        return x

class GMMAttention(nn.Module):
    """Custom attention mechanism integrating GMM components with dynamic component support"""

    def __init__(self, d_model, n_components, dropout=0.1):
        """Initialize GMM attention"""
        super(GMMAttention, self).__init__()

        self.d_model = d_model
        self.n_components = n_components

        # Maximum number of components we might need to support
        self.max_components = max(n_components, 15)

        # Combined QKV projection for efficiency
        self.qkv_proj = nn.Linear(d_model, 3 * d_model)

        # Component attention projection (with max_components)
        self.component_proj = nn.Linear(d_model, self.max_components)

        # Output projection
        self.out_proj = nn.Linear(d_model, d_model)

        # Dropout
        self.dropout = nn.Dropout(dropout)

        # Scaling factor
        self.scale = 1.0 / math.sqrt(d_model)

    def forward(self, query, key, value, component_weights, mask=None):
        """Forward pass with integrated GMM component weighting"""
        # Get batch size and sequence length
        batch_size, seq_len, _ = query.shape

        # Combined QKV projection
        qkv = self.qkv_proj(query)
        qkv = qkv.reshape(batch_size, seq_len, 3, self.d_model)
        q, k, v = qkv.unbind(dim=2)

        # Calculate attention scores
        attn_scores = torch.matmul(q, k.transpose(-2, -1)) * self.scale

        # Apply mask if provided
        if mask is not None:
            attn_scores = attn_scores.masked_fill(mask == 0, -1e9)

        # Calculate attention weights
        attn_weights = F.softmax(attn_scores, dim=-1)
        attn_weights = self.dropout(attn_weights)

        # Apply attention to value
        context = torch.matmul(attn_weights, v)

        # Calculate component attention
        component_logits = self.component_proj(query)

        # Integrate GMM component weights
        if component_weights is not None:
            # Get the actual number of components from the weights
            actual_n_components = component_weights.size(-1)

            # Slice the component logits to match the actual number of components
            component_logits = component_logits[:, :, :actual_n_components]

            # Reshape for broadcasting
            comp_weights = component_weights.unsqueeze(1)

            # Weight the component logits
            component_logits = component_logits * comp_weights

        # Final component attention
        component_attn = F.softmax(component_logits, dim=-1)

        # Apply component attention
        # Reshape context: [batch, seq, 1, dim] * [batch, seq, comp, 1] -> [batch, seq, comp, dim]
        context_weighted = context.unsqueeze(2) * component_attn.unsqueeze(-1)
        # Sum over components
        context = context_weighted.sum(dim=2)

        # Project output
        output = self.out_proj(context)

        return output, attn_weights, component_attn

class TransformerEncoderLayer(nn.Module):
    """Custom transformer encoder layer with GMM attention"""

    def __init__(self, d_model, n_components, nhead=8, dim_feedforward=2048, dropout=0.1):
        """Initialize transformer encoder layer

        Args:
            d_model: Model dimension
            n_components: Number of GMM components
            nhead: Number of attention heads
            dim_feedforward: Dimension of feedforward network
            dropout: Dropout rate
        """
        super(TransformerEncoderLayer, self).__init__()

        # GMM attention
        self.gmm_attn = GMMAttention(d_model, n_components, dropout)

        # Feedforward network
        self.linear1 = nn.Linear(d_model, dim_feedforward)
        self.dropout = nn.Dropout(dropout)
        self.linear2 = nn.Linear(dim_feedforward, d_model)

        # Layer normalization
        self.norm1 = nn.LayerNorm(d_model)
        self.norm2 = nn.LayerNorm(d_model)

        # Dropout
        self.dropout1 = nn.Dropout(dropout)
        self.dropout2 = nn.Dropout(dropout)

    def forward(self, src, component_weights=None, src_mask=None):
        """Forward pass

        Args:
            src: Input tensor (batch_size, seq_length, d_model)
            component_weights: GMM component weights (batch_size, n_components)
            src_mask: Source mask (optional)

        Returns:
            src: Output tensor
            attn_weights: Attention weights
            component_attn: Component attention weights
        """
        # Self-attention with GMM component weighting
        src2, attn_weights, component_attn = self.gmm_attn(
            query=src, key=src, value=src,
            component_weights=component_weights, mask=src_mask
        )
        src = src + self.dropout1(src2)
        src = self.norm1(src)

        # Feedforward
        src2 = self.linear2(self.dropout(F.relu(self.linear1(src))))
        src = src + self.dropout2(src2)
        src = self.norm2(src)

        return src, attn_weights, component_attn

class TransformerEncoder(nn.Module):
    """Transformer encoder with GMM attention"""

    def __init__(self, d_model, n_components, n_layers=3, nhead=8,
                 dim_feedforward=2048, dropout=0.1, max_seq_length=100):
        """Initialize transformer encoder

        Args:
            d_model: Model dimension
            n_components: Number of GMM components
            n_layers: Number of encoder layers
            nhead: Number of attention heads
            dim_feedforward: Dimension of feedforward network
            dropout: Dropout rate
            max_seq_length: Maximum sequence length
        """
        super(TransformerEncoder, self).__init__()

        # Positional encoding
        self.pos_encoder = PositionalEncoding(d_model, max_seq_length)

        # Encoder layers
        self.layers = nn.ModuleList([
            TransformerEncoderLayer(
                d_model, n_components, nhead, dim_feedforward, dropout
            ) for _ in range(n_layers)
        ])

        # Layer normalization
        self.norm = nn.LayerNorm(d_model)

    def forward(self, src, component_weights=None, src_mask=None):
        """Forward pass

        Args:
            src: Input tensor (batch_size, seq_length, d_model)
            component_weights: GMM component weights (batch_size, n_components)
            src_mask: Source mask (optional)

        Returns:
            output: Encoder output
            attn_weights: Attention weights from all layers
            component_attn: Component attention weights from all layers
        """
        # Add positional encoding
        src = self.pos_encoder(src)

        # Initialize attention weights storage
        attn_weights_layers = []
        component_attn_layers = []

        # Pass through encoder layers
        output = src
        for layer in self.layers:
            output, attn_weights, component_attn = layer(
                output, component_weights, src_mask
            )
            attn_weights_layers.append(attn_weights)
            component_attn_layers.append(component_attn)

        # Apply final normalization
        output = self.norm(output)

        return output, attn_weights_layers, component_attn_layers

class ContrastiveLoss(nn.Module):
    """Contrastive loss for diverse pattern learning"""

    def __init__(self, temperature=0.5):
        """Initialize contrastive loss

        Args:
            temperature: Temperature parameter for scaling
        """
        super(ContrastiveLoss, self).__init__()
        self.temperature = temperature

    def forward(self, features, labels=None):
        """Calculate contrastive loss

        Args:
            features: Feature embeddings (batch_size, d_model)
            labels: Optional labels for supervised contrastive loss

        Returns:
            loss: Contrastive loss
        """
        batch_size = features.size(0)

        # Normalize features
        features = F.normalize(features, dim=1)

        # Calculate similarity matrix
        similarity_matrix = torch.matmul(features, features.T) / self.temperature

        # Mask out self-similarity
        mask = torch.eye(batch_size, dtype=torch.bool, device=features.device)
        similarity_matrix = similarity_matrix.masked_fill(mask, -1e9)

        # If labels are provided, use supervised contrastive loss
        if labels is not None:
            labels = labels.contiguous().view(-1, 1)
            mask = torch.eq(labels, labels.T).float()

            # Calculate positive and negative similarities
            pos_sim = torch.exp(similarity_matrix) * mask
            neg_sim = torch.exp(similarity_matrix) * (1 - mask)

            # Calculate loss
            pos_sim_sum = torch.sum(pos_sim, dim=1)
            neg_sim_sum = torch.sum(neg_sim, dim=1)

            # Handle case where there are no positives
            denominator = pos_sim_sum + neg_sim_sum + 1e-8
            loss = -torch.log(pos_sim_sum / denominator)
            loss = torch.mean(loss)
        else:
            # Unsupervised contrastive loss (SimCLR-like)
            logits = F.log_softmax(similarity_matrix, dim=1)
            loss = -torch.mean(torch.diag(logits))

        return loss
