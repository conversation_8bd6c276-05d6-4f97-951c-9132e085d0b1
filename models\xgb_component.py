"""
XGBoost component for GMTrans-GBFE
"""
import numpy as np
import torch
import torch.nn as nn
import torch.nn.functional as F
import xgboost as xgb
from sklearn.base import BaseEstimator, RegressorMixin

class XGBComponent:
    """XGBoost component for feature evolution and regression"""

    def __init__(self, task='regression', n_estimators=100, learning_rate=0.1,
                 max_depth=3, random_state=42, use_gpu=torch.cuda.is_available(),
                 tree_method='auto', early_stopping_rounds=10):
        """Initialize XGBoost component

        Args:
            task: Task type ('regression' or 'classification')
            n_estimators: Number of boosting rounds
            learning_rate: Learning rate
            max_depth: Maximum tree depth
            random_state: Random state for reproducibility
            use_gpu: Whether to use GPU acceleration
            tree_method: Tree construction algorithm
            early_stopping_rounds: Early stopping rounds
        """
        self.task = task
        self.n_estimators = n_estimators
        self.learning_rate = learning_rate
        self.max_depth = max_depth
        self.random_state = random_state
        self.use_gpu = use_gpu
        self.tree_method = tree_method
        self.early_stopping_rounds = early_stopping_rounds
        self.model = None
        self.models = None

        # Initialize parameters
        self.params = {
            'objective': 'reg:squarederror' if task == 'regression' else 'binary:logistic',
            'n_estimators': n_estimators,
            'learning_rate': learning_rate,
            'max_depth': max_depth,
            'random_state': random_state,
            'tree_method': tree_method,
            'verbosity': 0,
            'n_jobs': -1
        }

        # Add GPU configuration if available and requested
        if self.use_gpu:
            self.params.update({
                'tree_method': 'gpu_hist',
                'gpu_id': 0,
            })

    def fit(self, X, y, X_val=None, y_val=None):
        """Fit model to data

        Args:
            X: Input features
            y: Target values
            X_val: Validation features
            y_val: Validation targets
        """
        # Handle multi-output regression
        if len(y.shape) > 1 and y.shape[1] > 1:
            # Multi-output regression
            self.models = []
            for i in range(y.shape[1]):
                dtrain = xgb.DMatrix(X, y[:, i])

                # Add validation set if provided
                if X_val is not None and y_val is not None:
                    dval = xgb.DMatrix(X_val, y_val[:, i])
                    watchlist = [(dtrain, 'train'), (dval, 'eval')]
                    model = xgb.train(
                        self.params,
                        dtrain,
                        num_boost_round=self.n_estimators,
                        evals=watchlist,
                        early_stopping_rounds=self.early_stopping_rounds,
                        verbose_eval=False
                    )
                else:
                    model = xgb.train(
                        self.params,
                        dtrain,
                        num_boost_round=self.n_estimators
                    )

                self.models.append(model)
        else:
            # Single output
            if len(y.shape) > 1:
                y = y.ravel()

            dtrain = xgb.DMatrix(X, y)

            # Add validation set if provided
            if X_val is not None and y_val is not None:
                if len(y_val.shape) > 1:
                    y_val = y_val.ravel()
                dval = xgb.DMatrix(X_val, y_val)
                watchlist = [(dtrain, 'train'), (dval, 'eval')]
                self.model = xgb.train(
                    self.params,
                    dtrain,
                    num_boost_round=self.n_estimators,
                    evals=watchlist,
                    early_stopping_rounds=self.early_stopping_rounds,
                    verbose_eval=False
                )
            else:
                self.model = xgb.train(
                    self.params,
                    dtrain,
                    num_boost_round=self.n_estimators
                )

    def predict(self, X):
        """Make predictions

        Args:
            X: Input features

        Returns:
            y_pred: Predictions
        """
        dtest = xgb.DMatrix(X)

        if self.models is not None:
            # Multi-output regression
            y_preds = []
            for model in self.models:
                y_pred = model.predict(dtest)
                y_preds.append(y_pred)
            return np.column_stack(y_preds)
        else:
            # Single output
            return self.model.predict(dtest)

    def get_feature_importance(self):
        """Get feature importance

        Returns:
            feature_importance: Feature importance scores
        """
        if self.models is not None:
            # Multi-output regression
            importances = []
            for model in self.models:
                importance = model.get_score(importance_type='gain')
                importances.append(importance)

            # Combine importances from all models
            combined_importance = {}
            for importance in importances:
                for feature, score in importance.items():
                    if feature in combined_importance:
                        combined_importance[feature] += score
                    else:
                        combined_importance[feature] = score

            # Convert to array
            feature_importance = np.zeros(self.params.get('num_feature', len(combined_importance)))
            for feature, score in combined_importance.items():
                feature_idx = int(feature.replace('f', ''))
                feature_importance[feature_idx] = score

            # Normalize
            if np.sum(feature_importance) > 0:
                feature_importance = feature_importance / np.sum(feature_importance)

            return feature_importance
        else:
            # Single output
            importance = self.model.get_score(importance_type='gain')

            # Convert to array
            feature_importance = np.zeros(self.params.get('num_feature', len(importance)))
            for feature, score in importance.items():
                feature_idx = int(feature.replace('f', ''))
                feature_importance[feature_idx] = score

            # Normalize
            if np.sum(feature_importance) > 0:
                feature_importance = feature_importance / np.sum(feature_importance)

            return feature_importance

    def get_staged_predictions(self, X, n_stages=None):
        """Get staged predictions for feature evolution

        Args:
            X: Input features
            n_stages: Number of stages to return

        Returns:
            staged_preds: List of predictions at each stage
        """
        if n_stages is None:
            n_stages = self.n_estimators

        dtest = xgb.DMatrix(X)

        if self.models is not None:
            # Multi-output regression
            staged_preds = []
            for i in range(1, n_stages + 1):
                stage_preds = []
                for model in self.models:
                    # Get prediction with limited number of trees
                    pred = model.predict(dtest, ntree_limit=i)
                    stage_preds.append(pred)
                staged_preds.append(np.column_stack(stage_preds))
            return staged_preds
        else:
            # Single output
            staged_preds = []
            for i in range(1, n_stages + 1):
                # Get prediction with limited number of trees
                pred = self.model.predict(dtest, ntree_limit=i)
                staged_preds.append(pred)
            return staged_preds

class XGBFeatureEvolutionLayer(nn.Module):
    """XGBoost-based feature evolution layer for neural networks"""

    def __init__(self, input_dim, hidden_dim, n_stages=2, dropout=0.1):
        """Initialize feature evolution layer

        Args:
            input_dim: Input dimension
            hidden_dim: Hidden dimension
            n_stages: Number of evolution stages
            dropout: Dropout rate
        """
        super(XGBFeatureEvolutionLayer, self).__init__()

        self.input_dim = input_dim
        self.hidden_dim = hidden_dim
        self.n_stages = n_stages
        self.dropout = dropout

        # Initial projection
        self.initial_proj = nn.Linear(input_dim, hidden_dim)

        # Stage weights for weighted combination
        self.stage_weights = nn.Parameter(torch.ones(n_stages))

        # XGBoost-inspired feature transformation
        # We'll use neural network layers that mimic XGBoost's behavior
        # with tree-like decision boundaries and residual learning

        # Feature transformation layers (like XGBoost trees)
        self.feature_transforms = nn.ModuleList([
            nn.Sequential(
                nn.Linear(input_dim, hidden_dim),
                nn.ReLU(),
                nn.Dropout(dropout),
                nn.Linear(hidden_dim, hidden_dim // 2),
                nn.ReLU(),
                nn.Linear(hidden_dim // 2, input_dim)
            ) for _ in range(n_stages)
        ])

        # Residual connections (like XGBoost's residual learning)
        self.residual_transforms = nn.ModuleList([
            nn.Linear(hidden_dim, hidden_dim) for _ in range(n_stages)
        ])

        # Layer normalization for stability
        self.layer_norms = nn.ModuleList([
            nn.LayerNorm(hidden_dim) for _ in range(n_stages)
        ])

        # Attention mechanism to mimic XGBoost's feature importance
        self.feature_attention = nn.Sequential(
            nn.Linear(input_dim, input_dim),
            nn.Sigmoid()
        )

        # Final projection
        self.final_proj = nn.Linear(hidden_dim, input_dim)

        # Initialize weights
        self._init_weights()

    def _init_weights(self):
        """Initialize weights with Xavier uniform"""
        for m in self.modules():
            if isinstance(m, nn.Linear):
                nn.init.xavier_uniform_(m.weight)
                if m.bias is not None:
                    nn.init.zeros_(m.bias)

    def forward(self, x):
        """Forward pass with XGBoost-inspired feature evolution

        Args:
            x: Input tensor

        Returns:
            evolved_features: Evolved features
            stage_outputs: Outputs at each stage
        """
        # Apply feature attention (mimics XGBoost feature importance)
        feature_weights = self.feature_attention(x)
        weighted_input = x * feature_weights

        # Initial projection
        h = self.initial_proj(weighted_input)

        # Apply stages sequentially with residual connections (like XGBoost boosting)
        stage_outputs = []
        residual = h

        # Normalize weights with softmax for better stability
        weights = F.softmax(self.stage_weights, dim=0)

        # Accumulated output (like XGBoost's additive model)
        accumulated = torch.zeros_like(h)

        # XGBoost-inspired feature transformations
        feature_residuals = weighted_input

        for i in range(self.n_stages):
            # Apply feature transformation (like XGBoost tree)
            tree_output = self.feature_transforms[i](feature_residuals)

            # Update feature residuals (like XGBoost's residual learning)
            feature_residuals = feature_residuals - 0.1 * tree_output

            # Apply stage transformation to hidden representation
            stage_output = residual + F.dropout(
                F.relu(tree_output @ self.initial_proj.weight.t() + self.initial_proj.bias),
                p=self.dropout,
                training=self.training
            )

            # Apply layer normalization
            stage_output = self.layer_norms[i](stage_output)

            # Update accumulated output with weighted stage output (like XGBoost's additive model)
            accumulated = accumulated + stage_output * weights[i].unsqueeze(0).unsqueeze(1)

            # Update residual for next stage
            residual = stage_output

            # Save stage output
            stage_outputs.append(stage_output)

        # Final projection back to input dimension
        evolved_features = self.final_proj(accumulated)

        # Add residual connection to input (like XGBoost's final prediction)
        evolved_features = evolved_features + 0.1 * weighted_input

        return evolved_features, stage_outputs
