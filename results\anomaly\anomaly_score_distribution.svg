<?xml version="1.0" encoding="utf-8" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN"
  "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<svg xmlns:xlink="http://www.w3.org/1999/xlink" width="568.495625pt" height="423.034375pt" viewBox="0 0 568.495625 423.034375" xmlns="http://www.w3.org/2000/svg" version="1.1">
 <metadata>
  <rdf:RDF xmlns:dc="http://purl.org/dc/elements/1.1/" xmlns:cc="http://creativecommons.org/ns#" xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#">
   <cc:Work>
    <dc:type rdf:resource="http://purl.org/dc/dcmitype/StillImage"/>
    <dc:date>2025-05-15T21:23:33.046021</dc:date>
    <dc:format>image/svg+xml</dc:format>
    <dc:creator>
     <cc:Agent>
      <dc:title>Matplotlib v3.10.1, https://matplotlib.org/</dc:title>
     </cc:Agent>
    </dc:creator>
   </cc:Work>
  </rdf:RDF>
 </metadata>
 <defs>
  <style type="text/css">*{stroke-linejoin: round; stroke-linecap: butt}</style>
 </defs>
 <g id="figure_1">
  <g id="patch_1">
   <path d="M 0 423.034375 
L 568.495625 423.034375 
L 568.495625 0 
L 0 0 
z
" style="fill: #ffffff"/>
  </g>
  <g id="axes_1">
   <g id="patch_2">
    <path d="M 46.965625 385.478125 
L 561.295625 385.478125 
L 561.295625 22.318125 
L 46.965625 22.318125 
z
" style="fill: #ffffff"/>
   </g>
   <g id="patch_3">
    <path d="M 70.344261 385.478125 
L 82.400645 385.478125 
L 82.400645 270.189236 
L 70.344261 270.189236 
z
" clip-path="url(#p18dbaca589)" style="fill: #1f77b4; fill-opacity: 0.5; stroke: #000000; stroke-linejoin: miter"/>
   </g>
   <g id="patch_4">
    <path d="M 82.400645 385.478125 
L 94.457028 385.478125 
L 94.457028 201.015903 
L 82.400645 201.015903 
z
" clip-path="url(#p18dbaca589)" style="fill: #1f77b4; fill-opacity: 0.5; stroke: #000000; stroke-linejoin: miter"/>
   </g>
   <g id="patch_5">
    <path d="M 94.457028 385.478125 
L 106.513412 385.478125 
L 106.513412 195.251458 
L 94.457028 195.251458 
z
" clip-path="url(#p18dbaca589)" style="fill: #1f77b4; fill-opacity: 0.5; stroke: #000000; stroke-linejoin: miter"/>
   </g>
   <g id="patch_6">
    <path d="M 106.513412 385.478125 
L 118.569795 385.478125 
L 118.569795 39.611458 
L 106.513412 39.611458 
z
" clip-path="url(#p18dbaca589)" style="fill: #1f77b4; fill-opacity: 0.5; stroke: #000000; stroke-linejoin: miter"/>
   </g>
   <g id="patch_7">
    <path d="M 118.569795 385.478125 
L 130.626179 385.478125 
L 130.626179 105.902569 
L 118.569795 105.902569 
z
" clip-path="url(#p18dbaca589)" style="fill: #1f77b4; fill-opacity: 0.5; stroke: #000000; stroke-linejoin: miter"/>
   </g>
   <g id="patch_8">
    <path d="M 130.626179 385.478125 
L 142.682562 385.478125 
L 142.682562 137.607014 
L 130.626179 137.607014 
z
" clip-path="url(#p18dbaca589)" style="fill: #1f77b4; fill-opacity: 0.5; stroke: #000000; stroke-linejoin: miter"/>
   </g>
   <g id="patch_9">
    <path d="M 142.682562 385.478125 
L 154.738946 385.478125 
L 154.738946 143.371458 
L 142.682562 143.371458 
z
" clip-path="url(#p18dbaca589)" style="fill: #1f77b4; fill-opacity: 0.5; stroke: #000000; stroke-linejoin: miter"/>
   </g>
   <g id="patch_10">
    <path d="M 154.738946 385.478125 
L 166.795329 385.478125 
L 166.795329 91.491458 
L 154.738946 91.491458 
z
" clip-path="url(#p18dbaca589)" style="fill: #1f77b4; fill-opacity: 0.5; stroke: #000000; stroke-linejoin: miter"/>
   </g>
   <g id="patch_11">
    <path d="M 166.795329 385.478125 
L 178.851713 385.478125 
L 178.851713 218.309236 
L 166.795329 218.309236 
z
" clip-path="url(#p18dbaca589)" style="fill: #1f77b4; fill-opacity: 0.5; stroke: #000000; stroke-linejoin: miter"/>
   </g>
   <g id="patch_12">
    <path d="M 178.851713 385.478125 
L 190.908096 385.478125 
L 190.908096 267.307014 
L 178.851713 267.307014 
z
" clip-path="url(#p18dbaca589)" style="fill: #1f77b4; fill-opacity: 0.5; stroke: #000000; stroke-linejoin: miter"/>
   </g>
   <g id="patch_13">
    <path d="M 190.908096 385.478125 
L 202.964479 385.478125 
L 202.964479 299.011458 
L 190.908096 299.011458 
z
" clip-path="url(#p18dbaca589)" style="fill: #1f77b4; fill-opacity: 0.5; stroke: #000000; stroke-linejoin: miter"/>
   </g>
   <g id="patch_14">
    <path d="M 202.964479 385.478125 
L 215.020863 385.478125 
L 215.020863 293.247014 
L 202.964479 293.247014 
z
" clip-path="url(#p18dbaca589)" style="fill: #1f77b4; fill-opacity: 0.5; stroke: #000000; stroke-linejoin: miter"/>
   </g>
   <g id="patch_15">
    <path d="M 215.020863 385.478125 
L 227.077246 385.478125 
L 227.077246 345.127014 
L 215.020863 345.127014 
z
" clip-path="url(#p18dbaca589)" style="fill: #1f77b4; fill-opacity: 0.5; stroke: #000000; stroke-linejoin: miter"/>
   </g>
   <g id="patch_16">
    <path d="M 227.077246 385.478125 
L 239.13363 385.478125 
L 239.13363 330.715903 
L 227.077246 330.715903 
z
" clip-path="url(#p18dbaca589)" style="fill: #1f77b4; fill-opacity: 0.5; stroke: #000000; stroke-linejoin: miter"/>
   </g>
   <g id="patch_17">
    <path d="M 239.13363 385.478125 
L 251.190013 385.478125 
L 251.190013 350.891458 
L 239.13363 350.891458 
z
" clip-path="url(#p18dbaca589)" style="fill: #1f77b4; fill-opacity: 0.5; stroke: #000000; stroke-linejoin: miter"/>
   </g>
   <g id="patch_18">
    <path d="M 251.190013 385.478125 
L 263.246397 385.478125 
L 263.246397 365.302569 
L 251.190013 365.302569 
z
" clip-path="url(#p18dbaca589)" style="fill: #1f77b4; fill-opacity: 0.5; stroke: #000000; stroke-linejoin: miter"/>
   </g>
   <g id="patch_19">
    <path d="M 263.246397 385.478125 
L 275.30278 385.478125 
L 275.30278 356.655903 
L 263.246397 356.655903 
z
" clip-path="url(#p18dbaca589)" style="fill: #1f77b4; fill-opacity: 0.5; stroke: #000000; stroke-linejoin: miter"/>
   </g>
   <g id="patch_20">
    <path d="M 275.30278 385.478125 
L 287.359164 385.478125 
L 287.359164 362.420347 
L 275.30278 362.420347 
z
" clip-path="url(#p18dbaca589)" style="fill: #1f77b4; fill-opacity: 0.5; stroke: #000000; stroke-linejoin: miter"/>
   </g>
   <g id="patch_21">
    <path d="M 287.359164 385.478125 
L 299.415547 385.478125 
L 299.415547 376.831458 
L 287.359164 376.831458 
z
" clip-path="url(#p18dbaca589)" style="fill: #1f77b4; fill-opacity: 0.5; stroke: #000000; stroke-linejoin: miter"/>
   </g>
   <g id="patch_22">
    <path d="M 299.415547 385.478125 
L 311.471931 385.478125 
L 311.471931 382.595903 
L 299.415547 382.595903 
z
" clip-path="url(#p18dbaca589)" style="fill: #1f77b4; fill-opacity: 0.5; stroke: #000000; stroke-linejoin: miter"/>
   </g>
   <g id="patch_23">
    <path d="M 311.471931 385.478125 
L 323.528314 385.478125 
L 323.528314 379.713681 
L 311.471931 379.713681 
z
" clip-path="url(#p18dbaca589)" style="fill: #1f77b4; fill-opacity: 0.5; stroke: #000000; stroke-linejoin: miter"/>
   </g>
   <g id="patch_24">
    <path d="M 323.528314 385.478125 
L 335.584698 385.478125 
L 335.584698 379.713681 
L 323.528314 379.713681 
z
" clip-path="url(#p18dbaca589)" style="fill: #1f77b4; fill-opacity: 0.5; stroke: #000000; stroke-linejoin: miter"/>
   </g>
   <g id="patch_25">
    <path d="M 335.584698 385.478125 
L 347.641081 385.478125 
L 347.641081 382.595903 
L 335.584698 382.595903 
z
" clip-path="url(#p18dbaca589)" style="fill: #1f77b4; fill-opacity: 0.5; stroke: #000000; stroke-linejoin: miter"/>
   </g>
   <g id="patch_26">
    <path d="M 347.641081 385.478125 
L 359.697464 385.478125 
L 359.697464 382.595903 
L 347.641081 382.595903 
z
" clip-path="url(#p18dbaca589)" style="fill: #1f77b4; fill-opacity: 0.5; stroke: #000000; stroke-linejoin: miter"/>
   </g>
   <g id="patch_27">
    <path d="M 96.436627 385.478125 
L 140.584663 385.478125 
L 140.584663 356.655903 
L 96.436627 356.655903 
z
" clip-path="url(#p18dbaca589)" style="fill: #ff7f0e; fill-opacity: 0.5; stroke: #000000; stroke-linejoin: miter"/>
   </g>
   <g id="patch_28">
    <path d="M 140.584663 385.478125 
L 184.732699 385.478125 
L 184.732699 284.600347 
L 140.584663 284.600347 
z
" clip-path="url(#p18dbaca589)" style="fill: #ff7f0e; fill-opacity: 0.5; stroke: #000000; stroke-linejoin: miter"/>
   </g>
   <g id="patch_29">
    <path d="M 184.732699 385.478125 
L 228.880735 385.478125 
L 228.880735 293.247014 
L 184.732699 293.247014 
z
" clip-path="url(#p18dbaca589)" style="fill: #ff7f0e; fill-opacity: 0.5; stroke: #000000; stroke-linejoin: miter"/>
   </g>
   <g id="patch_30">
    <path d="M 228.880735 385.478125 
L 273.028772 385.478125 
L 273.028772 290.364792 
L 228.880735 290.364792 
z
" clip-path="url(#p18dbaca589)" style="fill: #ff7f0e; fill-opacity: 0.5; stroke: #000000; stroke-linejoin: miter"/>
   </g>
   <g id="patch_31">
    <path d="M 273.028772 385.478125 
L 317.176808 385.478125 
L 317.176808 250.013681 
L 273.028772 250.013681 
z
" clip-path="url(#p18dbaca589)" style="fill: #ff7f0e; fill-opacity: 0.5; stroke: #000000; stroke-linejoin: miter"/>
   </g>
   <g id="patch_32">
    <path d="M 317.176808 385.478125 
L 361.324844 385.478125 
L 361.324844 235.602569 
L 317.176808 235.602569 
z
" clip-path="url(#p18dbaca589)" style="fill: #ff7f0e; fill-opacity: 0.5; stroke: #000000; stroke-linejoin: miter"/>
   </g>
   <g id="patch_33">
    <path d="M 361.324844 385.478125 
L 405.47288 385.478125 
L 405.47288 258.660347 
L 361.324844 258.660347 
z
" clip-path="url(#p18dbaca589)" style="fill: #ff7f0e; fill-opacity: 0.5; stroke: #000000; stroke-linejoin: miter"/>
   </g>
   <g id="patch_34">
    <path d="M 405.47288 385.478125 
L 449.620916 385.478125 
L 449.620916 287.482569 
L 405.47288 287.482569 
z
" clip-path="url(#p18dbaca589)" style="fill: #ff7f0e; fill-opacity: 0.5; stroke: #000000; stroke-linejoin: miter"/>
   </g>
   <g id="patch_35">
    <path d="M 449.620916 385.478125 
L 493.768952 385.478125 
L 493.768952 359.538125 
L 449.620916 359.538125 
z
" clip-path="url(#p18dbaca589)" style="fill: #ff7f0e; fill-opacity: 0.5; stroke: #000000; stroke-linejoin: miter"/>
   </g>
   <g id="patch_36">
    <path d="M 493.768952 385.478125 
L 537.916989 385.478125 
L 537.916989 373.949236 
L 493.768952 373.949236 
z
" clip-path="url(#p18dbaca589)" style="fill: #ff7f0e; fill-opacity: 0.5; stroke: #000000; stroke-linejoin: miter"/>
   </g>
   <g id="matplotlib.axis_1">
    <g id="xtick_1">
     <g id="line2d_1">
      <path d="M 156.212905 385.478125 
L 156.212905 22.318125 
" clip-path="url(#p18dbaca589)" style="fill: none; stroke: #b0b0b0; stroke-opacity: 0.3; stroke-width: 0.8; stroke-linecap: square"/>
     </g>
     <g id="line2d_2">
      <defs>
       <path id="m14da31c16f" d="M 0 0 
L 0 3.5 
" style="stroke: #000000; stroke-width: 0.8"/>
      </defs>
      <g>
       <use xlink:href="#m14da31c16f" x="156.212905" y="385.478125" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_1">
      <!-- −0.05 -->
      <g transform="translate(140.890249 400.076562) scale(0.1 -0.1)">
       <defs>
        <path id="DejaVuSans-2212" d="M 678 2272 
L 4684 2272 
L 4684 1741 
L 678 1741 
L 678 2272 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-30" d="M 2034 4250 
Q 1547 4250 1301 3770 
Q 1056 3291 1056 2328 
Q 1056 1369 1301 889 
Q 1547 409 2034 409 
Q 2525 409 2770 889 
Q 3016 1369 3016 2328 
Q 3016 3291 2770 3770 
Q 2525 4250 2034 4250 
z
M 2034 4750 
Q 2819 4750 3233 4129 
Q 3647 3509 3647 2328 
Q 3647 1150 3233 529 
Q 2819 -91 2034 -91 
Q 1250 -91 836 529 
Q 422 1150 422 2328 
Q 422 3509 836 4129 
Q 1250 4750 2034 4750 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-2e" d="M 684 794 
L 1344 794 
L 1344 0 
L 684 0 
L 684 794 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-35" d="M 691 4666 
L 3169 4666 
L 3169 4134 
L 1269 4134 
L 1269 2991 
Q 1406 3038 1543 3061 
Q 1681 3084 1819 3084 
Q 2600 3084 3056 2656 
Q 3513 2228 3513 1497 
Q 3513 744 3044 326 
Q 2575 -91 1722 -91 
Q 1428 -91 1123 -41 
Q 819 9 494 109 
L 494 744 
Q 775 591 1075 516 
Q 1375 441 1709 441 
Q 2250 441 2565 725 
Q 2881 1009 2881 1497 
Q 2881 1984 2565 2268 
Q 2250 2553 1709 2553 
Q 1456 2553 1204 2497 
Q 953 2441 691 2322 
L 691 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-2212"/>
       <use xlink:href="#DejaVuSans-30" transform="translate(83.789062 0)"/>
       <use xlink:href="#DejaVuSans-2e" transform="translate(147.412109 0)"/>
       <use xlink:href="#DejaVuSans-30" transform="translate(179.199219 0)"/>
       <use xlink:href="#DejaVuSans-35" transform="translate(242.822266 0)"/>
      </g>
     </g>
    </g>
    <g id="xtick_2">
     <g id="line2d_3">
      <path d="M 266.601617 385.478125 
L 266.601617 22.318125 
" clip-path="url(#p18dbaca589)" style="fill: none; stroke: #b0b0b0; stroke-opacity: 0.3; stroke-width: 0.8; stroke-linecap: square"/>
     </g>
     <g id="line2d_4">
      <g>
       <use xlink:href="#m14da31c16f" x="266.601617" y="385.478125" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_2">
      <!-- 0.00 -->
      <g transform="translate(255.468804 400.076562) scale(0.1 -0.1)">
       <use xlink:href="#DejaVuSans-30"/>
       <use xlink:href="#DejaVuSans-2e" transform="translate(63.623047 0)"/>
       <use xlink:href="#DejaVuSans-30" transform="translate(95.410156 0)"/>
       <use xlink:href="#DejaVuSans-30" transform="translate(159.033203 0)"/>
      </g>
     </g>
    </g>
    <g id="xtick_3">
     <g id="line2d_5">
      <path d="M 376.990328 385.478125 
L 376.990328 22.318125 
" clip-path="url(#p18dbaca589)" style="fill: none; stroke: #b0b0b0; stroke-opacity: 0.3; stroke-width: 0.8; stroke-linecap: square"/>
     </g>
     <g id="line2d_6">
      <g>
       <use xlink:href="#m14da31c16f" x="376.990328" y="385.478125" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_3">
      <!-- 0.05 -->
      <g transform="translate(365.857516 400.076562) scale(0.1 -0.1)">
       <use xlink:href="#DejaVuSans-30"/>
       <use xlink:href="#DejaVuSans-2e" transform="translate(63.623047 0)"/>
       <use xlink:href="#DejaVuSans-30" transform="translate(95.410156 0)"/>
       <use xlink:href="#DejaVuSans-35" transform="translate(159.033203 0)"/>
      </g>
     </g>
    </g>
    <g id="xtick_4">
     <g id="line2d_7">
      <path d="M 487.37904 385.478125 
L 487.37904 22.318125 
" clip-path="url(#p18dbaca589)" style="fill: none; stroke: #b0b0b0; stroke-opacity: 0.3; stroke-width: 0.8; stroke-linecap: square"/>
     </g>
     <g id="line2d_8">
      <g>
       <use xlink:href="#m14da31c16f" x="487.37904" y="385.478125" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_4">
      <!-- 0.10 -->
      <g transform="translate(476.246227 400.076562) scale(0.1 -0.1)">
       <defs>
        <path id="DejaVuSans-31" d="M 794 531 
L 1825 531 
L 1825 4091 
L 703 3866 
L 703 4441 
L 1819 4666 
L 2450 4666 
L 2450 531 
L 3481 531 
L 3481 0 
L 794 0 
L 794 531 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-30"/>
       <use xlink:href="#DejaVuSans-2e" transform="translate(63.623047 0)"/>
       <use xlink:href="#DejaVuSans-31" transform="translate(95.410156 0)"/>
       <use xlink:href="#DejaVuSans-30" transform="translate(159.033203 0)"/>
      </g>
     </g>
    </g>
    <g id="text_5">
     <!-- Anomaly Score -->
     <g transform="translate(266.607969 413.754687) scale(0.1 -0.1)">
      <defs>
       <path id="DejaVuSans-41" d="M 2188 4044 
L 1331 1722 
L 3047 1722 
L 2188 4044 
z
M 1831 4666 
L 2547 4666 
L 4325 0 
L 3669 0 
L 3244 1197 
L 1141 1197 
L 716 0 
L 50 0 
L 1831 4666 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-6e" d="M 3513 2113 
L 3513 0 
L 2938 0 
L 2938 2094 
Q 2938 2591 2744 2837 
Q 2550 3084 2163 3084 
Q 1697 3084 1428 2787 
Q 1159 2491 1159 1978 
L 1159 0 
L 581 0 
L 581 3500 
L 1159 3500 
L 1159 2956 
Q 1366 3272 1645 3428 
Q 1925 3584 2291 3584 
Q 2894 3584 3203 3211 
Q 3513 2838 3513 2113 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-6f" d="M 1959 3097 
Q 1497 3097 1228 2736 
Q 959 2375 959 1747 
Q 959 1119 1226 758 
Q 1494 397 1959 397 
Q 2419 397 2687 759 
Q 2956 1122 2956 1747 
Q 2956 2369 2687 2733 
Q 2419 3097 1959 3097 
z
M 1959 3584 
Q 2709 3584 3137 3096 
Q 3566 2609 3566 1747 
Q 3566 888 3137 398 
Q 2709 -91 1959 -91 
Q 1206 -91 779 398 
Q 353 888 353 1747 
Q 353 2609 779 3096 
Q 1206 3584 1959 3584 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-6d" d="M 3328 2828 
Q 3544 3216 3844 3400 
Q 4144 3584 4550 3584 
Q 5097 3584 5394 3201 
Q 5691 2819 5691 2113 
L 5691 0 
L 5113 0 
L 5113 2094 
Q 5113 2597 4934 2840 
Q 4756 3084 4391 3084 
Q 3944 3084 3684 2787 
Q 3425 2491 3425 1978 
L 3425 0 
L 2847 0 
L 2847 2094 
Q 2847 2600 2669 2842 
Q 2491 3084 2119 3084 
Q 1678 3084 1418 2786 
Q 1159 2488 1159 1978 
L 1159 0 
L 581 0 
L 581 3500 
L 1159 3500 
L 1159 2956 
Q 1356 3278 1631 3431 
Q 1906 3584 2284 3584 
Q 2666 3584 2933 3390 
Q 3200 3197 3328 2828 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-61" d="M 2194 1759 
Q 1497 1759 1228 1600 
Q 959 1441 959 1056 
Q 959 750 1161 570 
Q 1363 391 1709 391 
Q 2188 391 2477 730 
Q 2766 1069 2766 1631 
L 2766 1759 
L 2194 1759 
z
M 3341 1997 
L 3341 0 
L 2766 0 
L 2766 531 
Q 2569 213 2275 61 
Q 1981 -91 1556 -91 
Q 1019 -91 701 211 
Q 384 513 384 1019 
Q 384 1609 779 1909 
Q 1175 2209 1959 2209 
L 2766 2209 
L 2766 2266 
Q 2766 2663 2505 2880 
Q 2244 3097 1772 3097 
Q 1472 3097 1187 3025 
Q 903 2953 641 2809 
L 641 3341 
Q 956 3463 1253 3523 
Q 1550 3584 1831 3584 
Q 2591 3584 2966 3190 
Q 3341 2797 3341 1997 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-6c" d="M 603 4863 
L 1178 4863 
L 1178 0 
L 603 0 
L 603 4863 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-79" d="M 2059 -325 
Q 1816 -950 1584 -1140 
Q 1353 -1331 966 -1331 
L 506 -1331 
L 506 -850 
L 844 -850 
Q 1081 -850 1212 -737 
Q 1344 -625 1503 -206 
L 1606 56 
L 191 3500 
L 800 3500 
L 1894 763 
L 2988 3500 
L 3597 3500 
L 2059 -325 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-20" transform="scale(0.015625)"/>
       <path id="DejaVuSans-53" d="M 3425 4513 
L 3425 3897 
Q 3066 4069 2747 4153 
Q 2428 4238 2131 4238 
Q 1616 4238 1336 4038 
Q 1056 3838 1056 3469 
Q 1056 3159 1242 3001 
Q 1428 2844 1947 2747 
L 2328 2669 
Q 3034 2534 3370 2195 
Q 3706 1856 3706 1288 
Q 3706 609 3251 259 
Q 2797 -91 1919 -91 
Q 1588 -91 1214 -16 
Q 841 59 441 206 
L 441 856 
Q 825 641 1194 531 
Q 1563 422 1919 422 
Q 2459 422 2753 634 
Q 3047 847 3047 1241 
Q 3047 1584 2836 1778 
Q 2625 1972 2144 2069 
L 1759 2144 
Q 1053 2284 737 2584 
Q 422 2884 422 3419 
Q 422 4038 858 4394 
Q 1294 4750 2059 4750 
Q 2388 4750 2728 4690 
Q 3069 4631 3425 4513 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-63" d="M 3122 3366 
L 3122 2828 
Q 2878 2963 2633 3030 
Q 2388 3097 2138 3097 
Q 1578 3097 1268 2742 
Q 959 2388 959 1747 
Q 959 1106 1268 751 
Q 1578 397 2138 397 
Q 2388 397 2633 464 
Q 2878 531 3122 666 
L 3122 134 
Q 2881 22 2623 -34 
Q 2366 -91 2075 -91 
Q 1284 -91 818 406 
Q 353 903 353 1747 
Q 353 2603 823 3093 
Q 1294 3584 2113 3584 
Q 2378 3584 2631 3529 
Q 2884 3475 3122 3366 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-72" d="M 2631 2963 
Q 2534 3019 2420 3045 
Q 2306 3072 2169 3072 
Q 1681 3072 1420 2755 
Q 1159 2438 1159 1844 
L 1159 0 
L 581 0 
L 581 3500 
L 1159 3500 
L 1159 2956 
Q 1341 3275 1631 3429 
Q 1922 3584 2338 3584 
Q 2397 3584 2469 3576 
Q 2541 3569 2628 3553 
L 2631 2963 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-65" d="M 3597 1894 
L 3597 1613 
L 953 1613 
Q 991 1019 1311 708 
Q 1631 397 2203 397 
Q 2534 397 2845 478 
Q 3156 559 3463 722 
L 3463 178 
Q 3153 47 2828 -22 
Q 2503 -91 2169 -91 
Q 1331 -91 842 396 
Q 353 884 353 1716 
Q 353 2575 817 3079 
Q 1281 3584 2069 3584 
Q 2775 3584 3186 3129 
Q 3597 2675 3597 1894 
z
M 3022 2063 
Q 3016 2534 2758 2815 
Q 2500 3097 2075 3097 
Q 1594 3097 1305 2825 
Q 1016 2553 972 2059 
L 3022 2063 
z
" transform="scale(0.015625)"/>
      </defs>
      <use xlink:href="#DejaVuSans-41"/>
      <use xlink:href="#DejaVuSans-6e" transform="translate(68.408203 0)"/>
      <use xlink:href="#DejaVuSans-6f" transform="translate(131.787109 0)"/>
      <use xlink:href="#DejaVuSans-6d" transform="translate(192.96875 0)"/>
      <use xlink:href="#DejaVuSans-61" transform="translate(290.380859 0)"/>
      <use xlink:href="#DejaVuSans-6c" transform="translate(351.660156 0)"/>
      <use xlink:href="#DejaVuSans-79" transform="translate(379.443359 0)"/>
      <use xlink:href="#DejaVuSans-20" transform="translate(438.623047 0)"/>
      <use xlink:href="#DejaVuSans-53" transform="translate(470.410156 0)"/>
      <use xlink:href="#DejaVuSans-63" transform="translate(533.886719 0)"/>
      <use xlink:href="#DejaVuSans-6f" transform="translate(588.867188 0)"/>
      <use xlink:href="#DejaVuSans-72" transform="translate(650.048828 0)"/>
      <use xlink:href="#DejaVuSans-65" transform="translate(688.912109 0)"/>
     </g>
    </g>
   </g>
   <g id="matplotlib.axis_2">
    <g id="ytick_1">
     <g id="line2d_9">
      <path d="M 46.965625 385.478125 
L 561.295625 385.478125 
" clip-path="url(#p18dbaca589)" style="fill: none; stroke: #b0b0b0; stroke-opacity: 0.3; stroke-width: 0.8; stroke-linecap: square"/>
     </g>
     <g id="line2d_10">
      <defs>
       <path id="m4e99e66212" d="M 0 0 
L -3.5 0 
" style="stroke: #000000; stroke-width: 0.8"/>
      </defs>
      <g>
       <use xlink:href="#m4e99e66212" x="46.965625" y="385.478125" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_6">
      <!-- 0 -->
      <g transform="translate(33.603125 389.277344) scale(0.1 -0.1)">
       <use xlink:href="#DejaVuSans-30"/>
      </g>
     </g>
    </g>
    <g id="ytick_2">
     <g id="line2d_11">
      <path d="M 46.965625 327.833681 
L 561.295625 327.833681 
" clip-path="url(#p18dbaca589)" style="fill: none; stroke: #b0b0b0; stroke-opacity: 0.3; stroke-width: 0.8; stroke-linecap: square"/>
     </g>
     <g id="line2d_12">
      <g>
       <use xlink:href="#m4e99e66212" x="46.965625" y="327.833681" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_7">
      <!-- 20 -->
      <g transform="translate(27.240625 331.632899) scale(0.1 -0.1)">
       <defs>
        <path id="DejaVuSans-32" d="M 1228 531 
L 3431 531 
L 3431 0 
L 469 0 
L 469 531 
Q 828 903 1448 1529 
Q 2069 2156 2228 2338 
Q 2531 2678 2651 2914 
Q 2772 3150 2772 3378 
Q 2772 3750 2511 3984 
Q 2250 4219 1831 4219 
Q 1534 4219 1204 4116 
Q 875 4013 500 3803 
L 500 4441 
Q 881 4594 1212 4672 
Q 1544 4750 1819 4750 
Q 2544 4750 2975 4387 
Q 3406 4025 3406 3419 
Q 3406 3131 3298 2873 
Q 3191 2616 2906 2266 
Q 2828 2175 2409 1742 
Q 1991 1309 1228 531 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-32"/>
       <use xlink:href="#DejaVuSans-30" transform="translate(63.623047 0)"/>
      </g>
     </g>
    </g>
    <g id="ytick_3">
     <g id="line2d_13">
      <path d="M 46.965625 270.189236 
L 561.295625 270.189236 
" clip-path="url(#p18dbaca589)" style="fill: none; stroke: #b0b0b0; stroke-opacity: 0.3; stroke-width: 0.8; stroke-linecap: square"/>
     </g>
     <g id="line2d_14">
      <g>
       <use xlink:href="#m4e99e66212" x="46.965625" y="270.189236" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_8">
      <!-- 40 -->
      <g transform="translate(27.240625 273.988455) scale(0.1 -0.1)">
       <defs>
        <path id="DejaVuSans-34" d="M 2419 4116 
L 825 1625 
L 2419 1625 
L 2419 4116 
z
M 2253 4666 
L 3047 4666 
L 3047 1625 
L 3713 1625 
L 3713 1100 
L 3047 1100 
L 3047 0 
L 2419 0 
L 2419 1100 
L 313 1100 
L 313 1709 
L 2253 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-34"/>
       <use xlink:href="#DejaVuSans-30" transform="translate(63.623047 0)"/>
      </g>
     </g>
    </g>
    <g id="ytick_4">
     <g id="line2d_15">
      <path d="M 46.965625 212.544792 
L 561.295625 212.544792 
" clip-path="url(#p18dbaca589)" style="fill: none; stroke: #b0b0b0; stroke-opacity: 0.3; stroke-width: 0.8; stroke-linecap: square"/>
     </g>
     <g id="line2d_16">
      <g>
       <use xlink:href="#m4e99e66212" x="46.965625" y="212.544792" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_9">
      <!-- 60 -->
      <g transform="translate(27.240625 216.34401) scale(0.1 -0.1)">
       <defs>
        <path id="DejaVuSans-36" d="M 2113 2584 
Q 1688 2584 1439 2293 
Q 1191 2003 1191 1497 
Q 1191 994 1439 701 
Q 1688 409 2113 409 
Q 2538 409 2786 701 
Q 3034 994 3034 1497 
Q 3034 2003 2786 2293 
Q 2538 2584 2113 2584 
z
M 3366 4563 
L 3366 3988 
Q 3128 4100 2886 4159 
Q 2644 4219 2406 4219 
Q 1781 4219 1451 3797 
Q 1122 3375 1075 2522 
Q 1259 2794 1537 2939 
Q 1816 3084 2150 3084 
Q 2853 3084 3261 2657 
Q 3669 2231 3669 1497 
Q 3669 778 3244 343 
Q 2819 -91 2113 -91 
Q 1303 -91 875 529 
Q 447 1150 447 2328 
Q 447 3434 972 4092 
Q 1497 4750 2381 4750 
Q 2619 4750 2861 4703 
Q 3103 4656 3366 4563 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-36"/>
       <use xlink:href="#DejaVuSans-30" transform="translate(63.623047 0)"/>
      </g>
     </g>
    </g>
    <g id="ytick_5">
     <g id="line2d_17">
      <path d="M 46.965625 154.900347 
L 561.295625 154.900347 
" clip-path="url(#p18dbaca589)" style="fill: none; stroke: #b0b0b0; stroke-opacity: 0.3; stroke-width: 0.8; stroke-linecap: square"/>
     </g>
     <g id="line2d_18">
      <g>
       <use xlink:href="#m4e99e66212" x="46.965625" y="154.900347" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_10">
      <!-- 80 -->
      <g transform="translate(27.240625 158.699566) scale(0.1 -0.1)">
       <defs>
        <path id="DejaVuSans-38" d="M 2034 2216 
Q 1584 2216 1326 1975 
Q 1069 1734 1069 1313 
Q 1069 891 1326 650 
Q 1584 409 2034 409 
Q 2484 409 2743 651 
Q 3003 894 3003 1313 
Q 3003 1734 2745 1975 
Q 2488 2216 2034 2216 
z
M 1403 2484 
Q 997 2584 770 2862 
Q 544 3141 544 3541 
Q 544 4100 942 4425 
Q 1341 4750 2034 4750 
Q 2731 4750 3128 4425 
Q 3525 4100 3525 3541 
Q 3525 3141 3298 2862 
Q 3072 2584 2669 2484 
Q 3125 2378 3379 2068 
Q 3634 1759 3634 1313 
Q 3634 634 3220 271 
Q 2806 -91 2034 -91 
Q 1263 -91 848 271 
Q 434 634 434 1313 
Q 434 1759 690 2068 
Q 947 2378 1403 2484 
z
M 1172 3481 
Q 1172 3119 1398 2916 
Q 1625 2713 2034 2713 
Q 2441 2713 2670 2916 
Q 2900 3119 2900 3481 
Q 2900 3844 2670 4047 
Q 2441 4250 2034 4250 
Q 1625 4250 1398 4047 
Q 1172 3844 1172 3481 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-38"/>
       <use xlink:href="#DejaVuSans-30" transform="translate(63.623047 0)"/>
      </g>
     </g>
    </g>
    <g id="ytick_6">
     <g id="line2d_19">
      <path d="M 46.965625 97.255903 
L 561.295625 97.255903 
" clip-path="url(#p18dbaca589)" style="fill: none; stroke: #b0b0b0; stroke-opacity: 0.3; stroke-width: 0.8; stroke-linecap: square"/>
     </g>
     <g id="line2d_20">
      <g>
       <use xlink:href="#m4e99e66212" x="46.965625" y="97.255903" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_11">
      <!-- 100 -->
      <g transform="translate(20.878125 101.055122) scale(0.1 -0.1)">
       <use xlink:href="#DejaVuSans-31"/>
       <use xlink:href="#DejaVuSans-30" transform="translate(63.623047 0)"/>
       <use xlink:href="#DejaVuSans-30" transform="translate(127.246094 0)"/>
      </g>
     </g>
    </g>
    <g id="ytick_7">
     <g id="line2d_21">
      <path d="M 46.965625 39.611458 
L 561.295625 39.611458 
" clip-path="url(#p18dbaca589)" style="fill: none; stroke: #b0b0b0; stroke-opacity: 0.3; stroke-width: 0.8; stroke-linecap: square"/>
     </g>
     <g id="line2d_22">
      <g>
       <use xlink:href="#m4e99e66212" x="46.965625" y="39.611458" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_12">
      <!-- 120 -->
      <g transform="translate(20.878125 43.410677) scale(0.1 -0.1)">
       <use xlink:href="#DejaVuSans-31"/>
       <use xlink:href="#DejaVuSans-32" transform="translate(63.623047 0)"/>
       <use xlink:href="#DejaVuSans-30" transform="translate(127.246094 0)"/>
      </g>
     </g>
    </g>
    <g id="text_13">
     <!-- Count -->
     <g transform="translate(14.798437 218.746562) rotate(-90) scale(0.1 -0.1)">
      <defs>
       <path id="DejaVuSans-43" d="M 4122 4306 
L 4122 3641 
Q 3803 3938 3442 4084 
Q 3081 4231 2675 4231 
Q 1875 4231 1450 3742 
Q 1025 3253 1025 2328 
Q 1025 1406 1450 917 
Q 1875 428 2675 428 
Q 3081 428 3442 575 
Q 3803 722 4122 1019 
L 4122 359 
Q 3791 134 3420 21 
Q 3050 -91 2638 -91 
Q 1578 -91 968 557 
Q 359 1206 359 2328 
Q 359 3453 968 4101 
Q 1578 4750 2638 4750 
Q 3056 4750 3426 4639 
Q 3797 4528 4122 4306 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-75" d="M 544 1381 
L 544 3500 
L 1119 3500 
L 1119 1403 
Q 1119 906 1312 657 
Q 1506 409 1894 409 
Q 2359 409 2629 706 
Q 2900 1003 2900 1516 
L 2900 3500 
L 3475 3500 
L 3475 0 
L 2900 0 
L 2900 538 
Q 2691 219 2414 64 
Q 2138 -91 1772 -91 
Q 1169 -91 856 284 
Q 544 659 544 1381 
z
M 1991 3584 
L 1991 3584 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-74" d="M 1172 4494 
L 1172 3500 
L 2356 3500 
L 2356 3053 
L 1172 3053 
L 1172 1153 
Q 1172 725 1289 603 
Q 1406 481 1766 481 
L 2356 481 
L 2356 0 
L 1766 0 
Q 1100 0 847 248 
Q 594 497 594 1153 
L 594 3053 
L 172 3053 
L 172 3500 
L 594 3500 
L 594 4494 
L 1172 4494 
z
" transform="scale(0.015625)"/>
      </defs>
      <use xlink:href="#DejaVuSans-43"/>
      <use xlink:href="#DejaVuSans-6f" transform="translate(69.824219 0)"/>
      <use xlink:href="#DejaVuSans-75" transform="translate(131.005859 0)"/>
      <use xlink:href="#DejaVuSans-6e" transform="translate(194.384766 0)"/>
      <use xlink:href="#DejaVuSans-74" transform="translate(257.763672 0)"/>
     </g>
    </g>
   </g>
   <g id="line2d_23">
    <path d="M 70.344261 319.525349 
L 73.252334 302.95701 
L 79.068479 267.735275 
L 83.430587 241.755694 
L 87.792696 217.318752 
L 92.154804 194.290085 
L 97.970949 164.754942 
L 102.333058 143.600341 
L 105.24113 130.694332 
L 106.695166 124.837841 
L 108.149202 119.493238 
L 109.603239 114.740147 
L 111.057275 110.647086 
L 112.511311 107.267038 
L 113.965347 104.634096 
L 115.419383 102.761395 
L 116.87342 101.640411 
L 118.327456 101.241583 
L 119.781492 101.516098 
L 121.235528 102.398608 
L 122.689564 103.810587 
L 124.143601 105.664027 
L 125.597637 107.865217 
L 128.505709 112.928922 
L 132.867818 120.837263 
L 135.77589 125.459508 
L 137.229926 127.4229 
L 138.683963 129.119382 
L 140.137999 130.542482 
L 141.592035 131.702454 
L 143.046071 132.626452 
L 144.500107 133.358125 
L 150.316252 135.725361 
L 151.770288 136.60263 
L 153.224325 137.776751 
L 154.678361 139.332872 
L 156.132397 141.345351 
L 157.586433 143.87382 
L 159.040469 146.960081 
L 160.494506 150.626057 
L 161.948542 154.872942 
L 163.402578 159.681581 
L 164.856614 165.014007 
L 167.764687 177.020151 
L 170.672759 190.323259 
L 179.396976 231.628209 
L 182.305049 244.111113 
L 185.213121 255.409023 
L 188.121193 265.402185 
L 191.029266 274.08675 
L 193.937338 281.556181 
L 196.84541 287.980821 
L 199.753483 293.581758 
L 202.661555 298.597083 
L 207.023664 305.481858 
L 212.839809 314.115084 
L 217.201917 320.2409 
L 220.10999 324.059784 
L 223.018062 327.611683 
L 225.926134 330.893822 
L 230.288243 335.408717 
L 236.104388 341.007818 
L 241.920533 346.328428 
L 244.828605 348.792117 
L 247.736677 351.024698 
L 250.64475 352.959948 
L 253.552822 354.566266 
L 256.460895 355.859382 
L 259.368967 356.902427 
L 271.001256 360.670895 
L 273.909329 361.978026 
L 276.817401 363.518114 
L 279.725474 365.272832 
L 284.087582 368.193064 
L 291.357763 373.14313 
L 294.265836 374.885738 
L 297.173908 376.380414 
L 300.08198 377.589399 
L 302.990053 378.509296 
L 305.898125 379.167941 
L 308.806198 379.614733 
L 313.168306 380.013794 
L 320.438487 380.354399 
L 329.162704 380.782073 
L 334.978849 381.303636 
L 342.24903 382.236342 
L 349.519211 383.145865 
L 355.335356 383.666862 
L 359.697464 383.965975 
L 359.697464 383.965975 
" clip-path="url(#p18dbaca589)" style="fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square"/>
   </g>
   <g id="line2d_24">
    <path d="M 96.436627 367.899489 
L 100.873616 364.647347 
L 105.310604 361.032873 
L 109.747593 357.062709 
L 114.184581 352.754156 
L 118.62157 348.136446 
L 123.058558 343.251645 
L 129.714041 335.54802 
L 145.243501 317.166686 
L 149.68049 312.218621 
L 154.117478 307.577073 
L 158.554467 303.325696 
L 162.991455 299.53439 
L 167.428444 296.254828 
L 169.646938 294.817252 
L 171.865432 293.517062 
L 174.083927 292.354316 
L 176.302421 291.327566 
L 178.520915 290.433893 
L 180.73941 289.668971 
L 182.957904 289.027146 
L 187.394892 288.084235 
L 191.831881 287.538109 
L 196.26887 287.309366 
L 200.705858 287.311667 
L 209.579835 287.661346 
L 216.235318 287.911466 
L 220.672307 287.946867 
L 225.109295 287.817227 
L 229.546284 287.484123 
L 233.983272 286.919784 
L 238.420261 286.105967 
L 242.857249 285.032909 
L 247.294238 283.698621 
L 251.731227 282.108602 
L 256.168215 280.275894 
L 260.605204 278.221271 
L 265.042192 275.973253 
L 271.697675 272.318704 
L 282.790146 265.84511 
L 291.664124 260.74823 
L 298.319606 257.196824 
L 302.756595 255.019392 
L 307.193584 253.021479 
L 311.630572 251.220424 
L 316.067561 249.62999 
L 320.504549 248.262297 
L 324.941538 247.129458 
L 329.378526 246.244576 
L 333.815515 245.621875 
L 338.252503 245.275948 
L 342.689492 245.22033 
L 347.126481 245.465747 
L 351.563469 246.018502 
L 356.000458 246.879435 
L 360.437446 248.043776 
L 364.874435 249.502042 
L 369.311423 251.24186 
L 373.748412 253.250395 
L 378.1854 255.516901 
L 382.622389 258.034834 
L 387.059378 260.803047 
L 391.496366 263.825748 
L 395.933355 267.111138 
L 400.370343 270.668929 
L 404.807332 274.507143 
L 409.24432 278.628759 
L 413.681309 283.028761 
L 418.118298 287.69207 
L 424.77378 295.120819 
L 431.429263 302.949664 
L 453.614206 329.601189 
L 460.269689 337.034433 
L 464.706677 341.694415 
L 469.143666 346.076638 
L 473.580655 350.154264 
L 478.017643 353.908602 
L 482.454632 357.329388 
L 486.89162 360.414835 
L 491.328609 363.171421 
L 495.765597 365.613341 
L 500.202586 367.761598 
L 504.639574 369.642748 
L 509.076563 371.287336 
L 513.513552 372.728124 
L 520.169034 374.579287 
L 526.824517 376.150085 
L 535.698494 377.952648 
L 537.916989 378.366015 
L 537.916989 378.366015 
" clip-path="url(#p18dbaca589)" style="fill: none; stroke: #ff7f0e; stroke-width: 1.5; stroke-linecap: square"/>
   </g>
   <g id="patch_37">
    <path d="M 46.965625 385.478125 
L 46.965625 22.318125 
" style="fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square"/>
   </g>
   <g id="patch_38">
    <path d="M 561.295625 385.478125 
L 561.295625 22.318125 
" style="fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square"/>
   </g>
   <g id="patch_39">
    <path d="M 46.965625 385.478125 
L 561.295625 385.478125 
" style="fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square"/>
   </g>
   <g id="patch_40">
    <path d="M 46.965625 22.318125 
L 561.295625 22.318125 
" style="fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square"/>
   </g>
   <g id="text_14">
    <!-- Anomaly Score Distribution -->
    <g transform="translate(222.194062 16.318125) scale(0.12 -0.12)">
     <defs>
      <path id="DejaVuSans-44" d="M 1259 4147 
L 1259 519 
L 2022 519 
Q 2988 519 3436 956 
Q 3884 1394 3884 2338 
Q 3884 3275 3436 3711 
Q 2988 4147 2022 4147 
L 1259 4147 
z
M 628 4666 
L 1925 4666 
Q 3281 4666 3915 4102 
Q 4550 3538 4550 2338 
Q 4550 1131 3912 565 
Q 3275 0 1925 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-69" d="M 603 3500 
L 1178 3500 
L 1178 0 
L 603 0 
L 603 3500 
z
M 603 4863 
L 1178 4863 
L 1178 4134 
L 603 4134 
L 603 4863 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-73" d="M 2834 3397 
L 2834 2853 
Q 2591 2978 2328 3040 
Q 2066 3103 1784 3103 
Q 1356 3103 1142 2972 
Q 928 2841 928 2578 
Q 928 2378 1081 2264 
Q 1234 2150 1697 2047 
L 1894 2003 
Q 2506 1872 2764 1633 
Q 3022 1394 3022 966 
Q 3022 478 2636 193 
Q 2250 -91 1575 -91 
Q 1294 -91 989 -36 
Q 684 19 347 128 
L 347 722 
Q 666 556 975 473 
Q 1284 391 1588 391 
Q 1994 391 2212 530 
Q 2431 669 2431 922 
Q 2431 1156 2273 1281 
Q 2116 1406 1581 1522 
L 1381 1569 
Q 847 1681 609 1914 
Q 372 2147 372 2553 
Q 372 3047 722 3315 
Q 1072 3584 1716 3584 
Q 2034 3584 2315 3537 
Q 2597 3491 2834 3397 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-62" d="M 3116 1747 
Q 3116 2381 2855 2742 
Q 2594 3103 2138 3103 
Q 1681 3103 1420 2742 
Q 1159 2381 1159 1747 
Q 1159 1113 1420 752 
Q 1681 391 2138 391 
Q 2594 391 2855 752 
Q 3116 1113 3116 1747 
z
M 1159 2969 
Q 1341 3281 1617 3432 
Q 1894 3584 2278 3584 
Q 2916 3584 3314 3078 
Q 3713 2572 3713 1747 
Q 3713 922 3314 415 
Q 2916 -91 2278 -91 
Q 1894 -91 1617 61 
Q 1341 213 1159 525 
L 1159 0 
L 581 0 
L 581 4863 
L 1159 4863 
L 1159 2969 
z
" transform="scale(0.015625)"/>
     </defs>
     <use xlink:href="#DejaVuSans-41"/>
     <use xlink:href="#DejaVuSans-6e" transform="translate(68.408203 0)"/>
     <use xlink:href="#DejaVuSans-6f" transform="translate(131.787109 0)"/>
     <use xlink:href="#DejaVuSans-6d" transform="translate(192.96875 0)"/>
     <use xlink:href="#DejaVuSans-61" transform="translate(290.380859 0)"/>
     <use xlink:href="#DejaVuSans-6c" transform="translate(351.660156 0)"/>
     <use xlink:href="#DejaVuSans-79" transform="translate(379.443359 0)"/>
     <use xlink:href="#DejaVuSans-20" transform="translate(438.623047 0)"/>
     <use xlink:href="#DejaVuSans-53" transform="translate(470.410156 0)"/>
     <use xlink:href="#DejaVuSans-63" transform="translate(533.886719 0)"/>
     <use xlink:href="#DejaVuSans-6f" transform="translate(588.867188 0)"/>
     <use xlink:href="#DejaVuSans-72" transform="translate(650.048828 0)"/>
     <use xlink:href="#DejaVuSans-65" transform="translate(688.912109 0)"/>
     <use xlink:href="#DejaVuSans-20" transform="translate(750.435547 0)"/>
     <use xlink:href="#DejaVuSans-44" transform="translate(782.222656 0)"/>
     <use xlink:href="#DejaVuSans-69" transform="translate(859.224609 0)"/>
     <use xlink:href="#DejaVuSans-73" transform="translate(887.007812 0)"/>
     <use xlink:href="#DejaVuSans-74" transform="translate(939.107422 0)"/>
     <use xlink:href="#DejaVuSans-72" transform="translate(978.316406 0)"/>
     <use xlink:href="#DejaVuSans-69" transform="translate(1019.429688 0)"/>
     <use xlink:href="#DejaVuSans-62" transform="translate(1047.212891 0)"/>
     <use xlink:href="#DejaVuSans-75" transform="translate(1110.689453 0)"/>
     <use xlink:href="#DejaVuSans-74" transform="translate(1174.068359 0)"/>
     <use xlink:href="#DejaVuSans-69" transform="translate(1213.277344 0)"/>
     <use xlink:href="#DejaVuSans-6f" transform="translate(1241.060547 0)"/>
     <use xlink:href="#DejaVuSans-6e" transform="translate(1302.242188 0)"/>
    </g>
   </g>
   <g id="legend_1">
    <g id="patch_41">
     <path d="M 478.433125 59.674375 
L 554.295625 59.674375 
Q 556.295625 59.674375 556.295625 57.674375 
L 556.295625 29.318125 
Q 556.295625 27.318125 554.295625 27.318125 
L 478.433125 27.318125 
Q 476.433125 27.318125 476.433125 29.318125 
L 476.433125 57.674375 
Q 476.433125 59.674375 478.433125 59.674375 
z
" style="fill: #ffffff; opacity: 0.8; stroke: #cccccc; stroke-linejoin: miter"/>
    </g>
    <g id="patch_42">
     <path d="M 480.433125 38.916562 
L 500.433125 38.916562 
L 500.433125 31.916562 
L 480.433125 31.916562 
z
" style="fill: #1f77b4; fill-opacity: 0.5; stroke: #000000; stroke-linejoin: miter"/>
    </g>
    <g id="text_15">
     <!-- Normal -->
     <g transform="translate(508.433125 38.916562) scale(0.1 -0.1)">
      <defs>
       <path id="DejaVuSans-4e" d="M 628 4666 
L 1478 4666 
L 3547 763 
L 3547 4666 
L 4159 4666 
L 4159 0 
L 3309 0 
L 1241 3903 
L 1241 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
      </defs>
      <use xlink:href="#DejaVuSans-4e"/>
      <use xlink:href="#DejaVuSans-6f" transform="translate(74.804688 0)"/>
      <use xlink:href="#DejaVuSans-72" transform="translate(135.986328 0)"/>
      <use xlink:href="#DejaVuSans-6d" transform="translate(175.349609 0)"/>
      <use xlink:href="#DejaVuSans-61" transform="translate(272.761719 0)"/>
      <use xlink:href="#DejaVuSans-6c" transform="translate(334.041016 0)"/>
     </g>
    </g>
    <g id="patch_43">
     <path d="M 480.433125 53.594687 
L 500.433125 53.594687 
L 500.433125 46.594687 
L 480.433125 46.594687 
z
" style="fill: #ff7f0e; fill-opacity: 0.5; stroke: #000000; stroke-linejoin: miter"/>
    </g>
    <g id="text_16">
     <!-- Anomaly -->
     <g transform="translate(508.433125 53.594687) scale(0.1 -0.1)">
      <use xlink:href="#DejaVuSans-41"/>
      <use xlink:href="#DejaVuSans-6e" transform="translate(68.408203 0)"/>
      <use xlink:href="#DejaVuSans-6f" transform="translate(131.787109 0)"/>
      <use xlink:href="#DejaVuSans-6d" transform="translate(192.96875 0)"/>
      <use xlink:href="#DejaVuSans-61" transform="translate(290.380859 0)"/>
      <use xlink:href="#DejaVuSans-6c" transform="translate(351.660156 0)"/>
      <use xlink:href="#DejaVuSans-79" transform="translate(379.443359 0)"/>
     </g>
    </g>
   </g>
  </g>
 </g>
 <defs>
  <clipPath id="p18dbaca589">
   <rect x="46.965625" y="22.318125" width="514.33" height="363.16"/>
  </clipPath>
 </defs>
</svg>
