<?xml version="1.0" encoding="utf-8" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN"
  "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<svg xmlns:xlink="http://www.w3.org/1999/xlink" width="568.492812pt" height="423.834062pt" viewBox="0 0 568.492812 423.834062" xmlns="http://www.w3.org/2000/svg" version="1.1">
 <metadata>
  <rdf:RDF xmlns:dc="http://purl.org/dc/elements/1.1/" xmlns:cc="http://creativecommons.org/ns#" xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#">
   <cc:Work>
    <dc:type rdf:resource="http://purl.org/dc/dcmitype/StillImage"/>
    <dc:date>2025-05-15T21:24:02.081599</dc:date>
    <dc:format>image/svg+xml</dc:format>
    <dc:creator>
     <cc:Agent>
      <dc:title>Matplotlib v3.10.1, https://matplotlib.org/</dc:title>
     </cc:Agent>
    </dc:creator>
   </cc:Work>
  </rdf:RDF>
 </metadata>
 <defs>
  <style type="text/css">*{stroke-linejoin: round; stroke-linecap: butt}</style>
 </defs>
 <g id="figure_1">
  <g id="patch_1">
   <path d="M 0 423.834062 
L 568.492812 423.834062 
L 568.492812 0 
L 0 0 
z
" style="fill: #ffffff"/>
  </g>
  <g id="axes_1">
   <g id="patch_2">
    <path d="M 43.78125 386.277812 
L 553.34125 386.277812 
L 553.34125 23.837812 
L 43.78125 23.837812 
z
" style="fill: #ffffff"/>
   </g>
   <g id="matplotlib.axis_1">
    <g id="xtick_1">
     <g id="line2d_1">
      <defs>
       <path id="mb6c3ba01bf" d="M 0 0 
L 0 3.5 
" style="stroke: #000000; stroke-width: 0.8"/>
      </defs>
      <g>
       <use xlink:href="#mb6c3ba01bf" x="43.78125" y="386.277812" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_1">
      <!-- 0.0 -->
      <g transform="translate(35.829688 400.87625) scale(0.1 -0.1)">
       <defs>
        <path id="DejaVuSans-30" d="M 2034 4250 
Q 1547 4250 1301 3770 
Q 1056 3291 1056 2328 
Q 1056 1369 1301 889 
Q 1547 409 2034 409 
Q 2525 409 2770 889 
Q 3016 1369 3016 2328 
Q 3016 3291 2770 3770 
Q 2525 4250 2034 4250 
z
M 2034 4750 
Q 2819 4750 3233 4129 
Q 3647 3509 3647 2328 
Q 3647 1150 3233 529 
Q 2819 -91 2034 -91 
Q 1250 -91 836 529 
Q 422 1150 422 2328 
Q 422 3509 836 4129 
Q 1250 4750 2034 4750 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-2e" d="M 684 794 
L 1344 794 
L 1344 0 
L 684 0 
L 684 794 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-30"/>
       <use xlink:href="#DejaVuSans-2e" transform="translate(63.623047 0)"/>
       <use xlink:href="#DejaVuSans-30" transform="translate(95.410156 0)"/>
      </g>
     </g>
    </g>
    <g id="xtick_2">
     <g id="line2d_2">
      <g>
       <use xlink:href="#mb6c3ba01bf" x="145.69325" y="386.277812" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_2">
      <!-- 0.2 -->
      <g transform="translate(137.741688 400.87625) scale(0.1 -0.1)">
       <defs>
        <path id="DejaVuSans-32" d="M 1228 531 
L 3431 531 
L 3431 0 
L 469 0 
L 469 531 
Q 828 903 1448 1529 
Q 2069 2156 2228 2338 
Q 2531 2678 2651 2914 
Q 2772 3150 2772 3378 
Q 2772 3750 2511 3984 
Q 2250 4219 1831 4219 
Q 1534 4219 1204 4116 
Q 875 4013 500 3803 
L 500 4441 
Q 881 4594 1212 4672 
Q 1544 4750 1819 4750 
Q 2544 4750 2975 4387 
Q 3406 4025 3406 3419 
Q 3406 3131 3298 2873 
Q 3191 2616 2906 2266 
Q 2828 2175 2409 1742 
Q 1991 1309 1228 531 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-30"/>
       <use xlink:href="#DejaVuSans-2e" transform="translate(63.623047 0)"/>
       <use xlink:href="#DejaVuSans-32" transform="translate(95.410156 0)"/>
      </g>
     </g>
    </g>
    <g id="xtick_3">
     <g id="line2d_3">
      <g>
       <use xlink:href="#mb6c3ba01bf" x="247.60525" y="386.277812" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_3">
      <!-- 0.4 -->
      <g transform="translate(239.653687 400.87625) scale(0.1 -0.1)">
       <defs>
        <path id="DejaVuSans-34" d="M 2419 4116 
L 825 1625 
L 2419 1625 
L 2419 4116 
z
M 2253 4666 
L 3047 4666 
L 3047 1625 
L 3713 1625 
L 3713 1100 
L 3047 1100 
L 3047 0 
L 2419 0 
L 2419 1100 
L 313 1100 
L 313 1709 
L 2253 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-30"/>
       <use xlink:href="#DejaVuSans-2e" transform="translate(63.623047 0)"/>
       <use xlink:href="#DejaVuSans-34" transform="translate(95.410156 0)"/>
      </g>
     </g>
    </g>
    <g id="xtick_4">
     <g id="line2d_4">
      <g>
       <use xlink:href="#mb6c3ba01bf" x="349.51725" y="386.277812" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_4">
      <!-- 0.6 -->
      <g transform="translate(341.565687 400.87625) scale(0.1 -0.1)">
       <defs>
        <path id="DejaVuSans-36" d="M 2113 2584 
Q 1688 2584 1439 2293 
Q 1191 2003 1191 1497 
Q 1191 994 1439 701 
Q 1688 409 2113 409 
Q 2538 409 2786 701 
Q 3034 994 3034 1497 
Q 3034 2003 2786 2293 
Q 2538 2584 2113 2584 
z
M 3366 4563 
L 3366 3988 
Q 3128 4100 2886 4159 
Q 2644 4219 2406 4219 
Q 1781 4219 1451 3797 
Q 1122 3375 1075 2522 
Q 1259 2794 1537 2939 
Q 1816 3084 2150 3084 
Q 2853 3084 3261 2657 
Q 3669 2231 3669 1497 
Q 3669 778 3244 343 
Q 2819 -91 2113 -91 
Q 1303 -91 875 529 
Q 447 1150 447 2328 
Q 447 3434 972 4092 
Q 1497 4750 2381 4750 
Q 2619 4750 2861 4703 
Q 3103 4656 3366 4563 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-30"/>
       <use xlink:href="#DejaVuSans-2e" transform="translate(63.623047 0)"/>
       <use xlink:href="#DejaVuSans-36" transform="translate(95.410156 0)"/>
      </g>
     </g>
    </g>
    <g id="xtick_5">
     <g id="line2d_5">
      <g>
       <use xlink:href="#mb6c3ba01bf" x="451.42925" y="386.277812" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_5">
      <!-- 0.8 -->
      <g transform="translate(443.477687 400.87625) scale(0.1 -0.1)">
       <defs>
        <path id="DejaVuSans-38" d="M 2034 2216 
Q 1584 2216 1326 1975 
Q 1069 1734 1069 1313 
Q 1069 891 1326 650 
Q 1584 409 2034 409 
Q 2484 409 2743 651 
Q 3003 894 3003 1313 
Q 3003 1734 2745 1975 
Q 2488 2216 2034 2216 
z
M 1403 2484 
Q 997 2584 770 2862 
Q 544 3141 544 3541 
Q 544 4100 942 4425 
Q 1341 4750 2034 4750 
Q 2731 4750 3128 4425 
Q 3525 4100 3525 3541 
Q 3525 3141 3298 2862 
Q 3072 2584 2669 2484 
Q 3125 2378 3379 2068 
Q 3634 1759 3634 1313 
Q 3634 634 3220 271 
Q 2806 -91 2034 -91 
Q 1263 -91 848 271 
Q 434 634 434 1313 
Q 434 1759 690 2068 
Q 947 2378 1403 2484 
z
M 1172 3481 
Q 1172 3119 1398 2916 
Q 1625 2713 2034 2713 
Q 2441 2713 2670 2916 
Q 2900 3119 2900 3481 
Q 2900 3844 2670 4047 
Q 2441 4250 2034 4250 
Q 1625 4250 1398 4047 
Q 1172 3844 1172 3481 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-30"/>
       <use xlink:href="#DejaVuSans-2e" transform="translate(63.623047 0)"/>
       <use xlink:href="#DejaVuSans-38" transform="translate(95.410156 0)"/>
      </g>
     </g>
    </g>
    <g id="xtick_6">
     <g id="line2d_6">
      <g>
       <use xlink:href="#mb6c3ba01bf" x="553.34125" y="386.277812" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_6">
      <!-- 1.0 -->
      <g transform="translate(545.389687 400.87625) scale(0.1 -0.1)">
       <defs>
        <path id="DejaVuSans-31" d="M 794 531 
L 1825 531 
L 1825 4091 
L 703 3866 
L 703 4441 
L 1819 4666 
L 2450 4666 
L 2450 531 
L 3481 531 
L 3481 0 
L 794 0 
L 794 531 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-31"/>
       <use xlink:href="#DejaVuSans-2e" transform="translate(63.623047 0)"/>
       <use xlink:href="#DejaVuSans-30" transform="translate(95.410156 0)"/>
      </g>
     </g>
    </g>
    <g id="text_7">
     <!-- False Positive Rate -->
     <g transform="translate(252.094844 414.554375) scale(0.1 -0.1)">
      <defs>
       <path id="DejaVuSans-46" d="M 628 4666 
L 3309 4666 
L 3309 4134 
L 1259 4134 
L 1259 2759 
L 3109 2759 
L 3109 2228 
L 1259 2228 
L 1259 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-61" d="M 2194 1759 
Q 1497 1759 1228 1600 
Q 959 1441 959 1056 
Q 959 750 1161 570 
Q 1363 391 1709 391 
Q 2188 391 2477 730 
Q 2766 1069 2766 1631 
L 2766 1759 
L 2194 1759 
z
M 3341 1997 
L 3341 0 
L 2766 0 
L 2766 531 
Q 2569 213 2275 61 
Q 1981 -91 1556 -91 
Q 1019 -91 701 211 
Q 384 513 384 1019 
Q 384 1609 779 1909 
Q 1175 2209 1959 2209 
L 2766 2209 
L 2766 2266 
Q 2766 2663 2505 2880 
Q 2244 3097 1772 3097 
Q 1472 3097 1187 3025 
Q 903 2953 641 2809 
L 641 3341 
Q 956 3463 1253 3523 
Q 1550 3584 1831 3584 
Q 2591 3584 2966 3190 
Q 3341 2797 3341 1997 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-6c" d="M 603 4863 
L 1178 4863 
L 1178 0 
L 603 0 
L 603 4863 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-73" d="M 2834 3397 
L 2834 2853 
Q 2591 2978 2328 3040 
Q 2066 3103 1784 3103 
Q 1356 3103 1142 2972 
Q 928 2841 928 2578 
Q 928 2378 1081 2264 
Q 1234 2150 1697 2047 
L 1894 2003 
Q 2506 1872 2764 1633 
Q 3022 1394 3022 966 
Q 3022 478 2636 193 
Q 2250 -91 1575 -91 
Q 1294 -91 989 -36 
Q 684 19 347 128 
L 347 722 
Q 666 556 975 473 
Q 1284 391 1588 391 
Q 1994 391 2212 530 
Q 2431 669 2431 922 
Q 2431 1156 2273 1281 
Q 2116 1406 1581 1522 
L 1381 1569 
Q 847 1681 609 1914 
Q 372 2147 372 2553 
Q 372 3047 722 3315 
Q 1072 3584 1716 3584 
Q 2034 3584 2315 3537 
Q 2597 3491 2834 3397 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-65" d="M 3597 1894 
L 3597 1613 
L 953 1613 
Q 991 1019 1311 708 
Q 1631 397 2203 397 
Q 2534 397 2845 478 
Q 3156 559 3463 722 
L 3463 178 
Q 3153 47 2828 -22 
Q 2503 -91 2169 -91 
Q 1331 -91 842 396 
Q 353 884 353 1716 
Q 353 2575 817 3079 
Q 1281 3584 2069 3584 
Q 2775 3584 3186 3129 
Q 3597 2675 3597 1894 
z
M 3022 2063 
Q 3016 2534 2758 2815 
Q 2500 3097 2075 3097 
Q 1594 3097 1305 2825 
Q 1016 2553 972 2059 
L 3022 2063 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-20" transform="scale(0.015625)"/>
       <path id="DejaVuSans-50" d="M 1259 4147 
L 1259 2394 
L 2053 2394 
Q 2494 2394 2734 2622 
Q 2975 2850 2975 3272 
Q 2975 3691 2734 3919 
Q 2494 4147 2053 4147 
L 1259 4147 
z
M 628 4666 
L 2053 4666 
Q 2838 4666 3239 4311 
Q 3641 3956 3641 3272 
Q 3641 2581 3239 2228 
Q 2838 1875 2053 1875 
L 1259 1875 
L 1259 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-6f" d="M 1959 3097 
Q 1497 3097 1228 2736 
Q 959 2375 959 1747 
Q 959 1119 1226 758 
Q 1494 397 1959 397 
Q 2419 397 2687 759 
Q 2956 1122 2956 1747 
Q 2956 2369 2687 2733 
Q 2419 3097 1959 3097 
z
M 1959 3584 
Q 2709 3584 3137 3096 
Q 3566 2609 3566 1747 
Q 3566 888 3137 398 
Q 2709 -91 1959 -91 
Q 1206 -91 779 398 
Q 353 888 353 1747 
Q 353 2609 779 3096 
Q 1206 3584 1959 3584 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-69" d="M 603 3500 
L 1178 3500 
L 1178 0 
L 603 0 
L 603 3500 
z
M 603 4863 
L 1178 4863 
L 1178 4134 
L 603 4134 
L 603 4863 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-74" d="M 1172 4494 
L 1172 3500 
L 2356 3500 
L 2356 3053 
L 1172 3053 
L 1172 1153 
Q 1172 725 1289 603 
Q 1406 481 1766 481 
L 2356 481 
L 2356 0 
L 1766 0 
Q 1100 0 847 248 
Q 594 497 594 1153 
L 594 3053 
L 172 3053 
L 172 3500 
L 594 3500 
L 594 4494 
L 1172 4494 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-76" d="M 191 3500 
L 800 3500 
L 1894 563 
L 2988 3500 
L 3597 3500 
L 2284 0 
L 1503 0 
L 191 3500 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-52" d="M 2841 2188 
Q 3044 2119 3236 1894 
Q 3428 1669 3622 1275 
L 4263 0 
L 3584 0 
L 2988 1197 
Q 2756 1666 2539 1819 
Q 2322 1972 1947 1972 
L 1259 1972 
L 1259 0 
L 628 0 
L 628 4666 
L 2053 4666 
Q 2853 4666 3247 4331 
Q 3641 3997 3641 3322 
Q 3641 2881 3436 2590 
Q 3231 2300 2841 2188 
z
M 1259 4147 
L 1259 2491 
L 2053 2491 
Q 2509 2491 2742 2702 
Q 2975 2913 2975 3322 
Q 2975 3731 2742 3939 
Q 2509 4147 2053 4147 
L 1259 4147 
z
" transform="scale(0.015625)"/>
      </defs>
      <use xlink:href="#DejaVuSans-46"/>
      <use xlink:href="#DejaVuSans-61" transform="translate(48.394531 0)"/>
      <use xlink:href="#DejaVuSans-6c" transform="translate(109.673828 0)"/>
      <use xlink:href="#DejaVuSans-73" transform="translate(137.457031 0)"/>
      <use xlink:href="#DejaVuSans-65" transform="translate(189.556641 0)"/>
      <use xlink:href="#DejaVuSans-20" transform="translate(251.080078 0)"/>
      <use xlink:href="#DejaVuSans-50" transform="translate(282.867188 0)"/>
      <use xlink:href="#DejaVuSans-6f" transform="translate(339.544922 0)"/>
      <use xlink:href="#DejaVuSans-73" transform="translate(400.726562 0)"/>
      <use xlink:href="#DejaVuSans-69" transform="translate(452.826172 0)"/>
      <use xlink:href="#DejaVuSans-74" transform="translate(480.609375 0)"/>
      <use xlink:href="#DejaVuSans-69" transform="translate(519.818359 0)"/>
      <use xlink:href="#DejaVuSans-76" transform="translate(547.601562 0)"/>
      <use xlink:href="#DejaVuSans-65" transform="translate(606.78125 0)"/>
      <use xlink:href="#DejaVuSans-20" transform="translate(668.304688 0)"/>
      <use xlink:href="#DejaVuSans-52" transform="translate(700.091797 0)"/>
      <use xlink:href="#DejaVuSans-61" transform="translate(767.324219 0)"/>
      <use xlink:href="#DejaVuSans-74" transform="translate(828.603516 0)"/>
      <use xlink:href="#DejaVuSans-65" transform="translate(867.8125 0)"/>
     </g>
    </g>
   </g>
   <g id="matplotlib.axis_2">
    <g id="ytick_1">
     <g id="line2d_7">
      <defs>
       <path id="m3bfdbb1d5f" d="M 0 0 
L -3.5 0 
" style="stroke: #000000; stroke-width: 0.8"/>
      </defs>
      <g>
       <use xlink:href="#m3bfdbb1d5f" x="43.78125" y="386.277812" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_8">
      <!-- 0.0 -->
      <g transform="translate(20.878125 390.077031) scale(0.1 -0.1)">
       <use xlink:href="#DejaVuSans-30"/>
       <use xlink:href="#DejaVuSans-2e" transform="translate(63.623047 0)"/>
       <use xlink:href="#DejaVuSans-30" transform="translate(95.410156 0)"/>
      </g>
     </g>
    </g>
    <g id="ytick_2">
     <g id="line2d_8">
      <g>
       <use xlink:href="#m3bfdbb1d5f" x="43.78125" y="317.241622" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_9">
      <!-- 0.2 -->
      <g transform="translate(20.878125 321.040841) scale(0.1 -0.1)">
       <use xlink:href="#DejaVuSans-30"/>
       <use xlink:href="#DejaVuSans-2e" transform="translate(63.623047 0)"/>
       <use xlink:href="#DejaVuSans-32" transform="translate(95.410156 0)"/>
      </g>
     </g>
    </g>
    <g id="ytick_3">
     <g id="line2d_9">
      <g>
       <use xlink:href="#m3bfdbb1d5f" x="43.78125" y="248.205432" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_10">
      <!-- 0.4 -->
      <g transform="translate(20.878125 252.00465) scale(0.1 -0.1)">
       <use xlink:href="#DejaVuSans-30"/>
       <use xlink:href="#DejaVuSans-2e" transform="translate(63.623047 0)"/>
       <use xlink:href="#DejaVuSans-34" transform="translate(95.410156 0)"/>
      </g>
     </g>
    </g>
    <g id="ytick_4">
     <g id="line2d_10">
      <g>
       <use xlink:href="#m3bfdbb1d5f" x="43.78125" y="179.169241" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_11">
      <!-- 0.6 -->
      <g transform="translate(20.878125 182.96846) scale(0.1 -0.1)">
       <use xlink:href="#DejaVuSans-30"/>
       <use xlink:href="#DejaVuSans-2e" transform="translate(63.623047 0)"/>
       <use xlink:href="#DejaVuSans-36" transform="translate(95.410156 0)"/>
      </g>
     </g>
    </g>
    <g id="ytick_5">
     <g id="line2d_11">
      <g>
       <use xlink:href="#m3bfdbb1d5f" x="43.78125" y="110.133051" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_12">
      <!-- 0.8 -->
      <g transform="translate(20.878125 113.932269) scale(0.1 -0.1)">
       <use xlink:href="#DejaVuSans-30"/>
       <use xlink:href="#DejaVuSans-2e" transform="translate(63.623047 0)"/>
       <use xlink:href="#DejaVuSans-38" transform="translate(95.410156 0)"/>
      </g>
     </g>
    </g>
    <g id="ytick_6">
     <g id="line2d_12">
      <g>
       <use xlink:href="#m3bfdbb1d5f" x="43.78125" y="41.09686" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_13">
      <!-- 1.0 -->
      <g transform="translate(20.878125 44.896079) scale(0.1 -0.1)">
       <use xlink:href="#DejaVuSans-31"/>
       <use xlink:href="#DejaVuSans-2e" transform="translate(63.623047 0)"/>
       <use xlink:href="#DejaVuSans-30" transform="translate(95.410156 0)"/>
      </g>
     </g>
    </g>
    <g id="text_14">
     <!-- True Positive Rate -->
     <g transform="translate(14.798438 249.5875) rotate(-90) scale(0.1 -0.1)">
      <defs>
       <path id="DejaVuSans-54" d="M -19 4666 
L 3928 4666 
L 3928 4134 
L 2272 4134 
L 2272 0 
L 1638 0 
L 1638 4134 
L -19 4134 
L -19 4666 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-72" d="M 2631 2963 
Q 2534 3019 2420 3045 
Q 2306 3072 2169 3072 
Q 1681 3072 1420 2755 
Q 1159 2438 1159 1844 
L 1159 0 
L 581 0 
L 581 3500 
L 1159 3500 
L 1159 2956 
Q 1341 3275 1631 3429 
Q 1922 3584 2338 3584 
Q 2397 3584 2469 3576 
Q 2541 3569 2628 3553 
L 2631 2963 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-75" d="M 544 1381 
L 544 3500 
L 1119 3500 
L 1119 1403 
Q 1119 906 1312 657 
Q 1506 409 1894 409 
Q 2359 409 2629 706 
Q 2900 1003 2900 1516 
L 2900 3500 
L 3475 3500 
L 3475 0 
L 2900 0 
L 2900 538 
Q 2691 219 2414 64 
Q 2138 -91 1772 -91 
Q 1169 -91 856 284 
Q 544 659 544 1381 
z
M 1991 3584 
L 1991 3584 
z
" transform="scale(0.015625)"/>
      </defs>
      <use xlink:href="#DejaVuSans-54"/>
      <use xlink:href="#DejaVuSans-72" transform="translate(46.333984 0)"/>
      <use xlink:href="#DejaVuSans-75" transform="translate(87.447266 0)"/>
      <use xlink:href="#DejaVuSans-65" transform="translate(150.826172 0)"/>
      <use xlink:href="#DejaVuSans-20" transform="translate(212.349609 0)"/>
      <use xlink:href="#DejaVuSans-50" transform="translate(244.136719 0)"/>
      <use xlink:href="#DejaVuSans-6f" transform="translate(300.814453 0)"/>
      <use xlink:href="#DejaVuSans-73" transform="translate(361.996094 0)"/>
      <use xlink:href="#DejaVuSans-69" transform="translate(414.095703 0)"/>
      <use xlink:href="#DejaVuSans-74" transform="translate(441.878906 0)"/>
      <use xlink:href="#DejaVuSans-69" transform="translate(481.087891 0)"/>
      <use xlink:href="#DejaVuSans-76" transform="translate(508.871094 0)"/>
      <use xlink:href="#DejaVuSans-65" transform="translate(568.050781 0)"/>
      <use xlink:href="#DejaVuSans-20" transform="translate(629.574219 0)"/>
      <use xlink:href="#DejaVuSans-52" transform="translate(661.361328 0)"/>
      <use xlink:href="#DejaVuSans-61" transform="translate(728.59375 0)"/>
      <use xlink:href="#DejaVuSans-74" transform="translate(789.873047 0)"/>
      <use xlink:href="#DejaVuSans-65" transform="translate(829.082031 0)"/>
     </g>
    </g>
   </g>
   <g id="line2d_13">
    <path d="M 43.78125 386.277812 
L 43.78125 278.121114 
L 44.347428 278.121114 
L 44.347428 247.054828 
L 44.913606 247.054828 
L 44.913606 242.452416 
L 45.479783 242.452416 
L 45.479783 236.6994 
L 46.045961 236.6994 
L 46.045961 221.741559 
L 47.178317 221.741559 
L 47.178317 215.988543 
L 47.744494 215.988543 
L 47.744494 198.729495 
L 48.310672 198.729495 
L 48.310672 190.675273 
L 48.87685 190.675273 
L 48.87685 186.07286 
L 49.443028 186.07286 
L 49.443028 184.922257 
L 50.009206 184.922257 
L 50.009206 181.470447 
L 50.575383 181.470447 
L 50.575383 178.018638 
L 51.141561 178.018638 
L 51.141561 176.868035 
L 51.707739 176.868035 
L 51.707739 174.566828 
L 52.273917 174.566828 
L 52.273917 173.416225 
L 52.840094 173.416225 
L 52.840094 169.964416 
L 53.406272 169.964416 
L 53.406272 167.663209 
L 56.237161 167.663209 
L 56.237161 166.512606 
L 58.501872 166.512606 
L 58.501872 165.362003 
L 59.06805 165.362003 
L 59.06805 163.060797 
L 59.634228 163.060797 
L 59.634228 159.608987 
L 61.898939 159.608987 
L 61.898939 157.307781 
L 62.465117 157.307781 
L 62.465117 155.006574 
L 63.031294 155.006574 
L 63.031294 151.554765 
L 63.597472 151.554765 
L 63.597472 150.404162 
L 64.729828 150.404162 
L 64.729828 148.102955 
L 65.862183 148.102955 
L 65.862183 145.801749 
L 66.994539 145.801749 
L 66.994539 144.651146 
L 67.560717 144.651146 
L 67.560717 143.500543 
L 70.957783 143.500543 
L 70.957783 141.199336 
L 71.523961 141.199336 
L 71.523961 140.048733 
L 73.222494 140.048733 
L 73.222494 134.295717 
L 74.921028 134.295717 
L 74.921028 131.994511 
L 75.487206 131.994511 
L 75.487206 130.843908 
L 78.318094 130.843908 
L 78.318094 129.693305 
L 81.148983 129.693305 
L 81.148983 128.542701 
L 83.979872 128.542701 
L 83.979872 126.241495 
L 86.810761 126.241495 
L 86.810761 121.639082 
L 92.472539 121.639082 
L 92.472539 119.337876 
L 94.73725 119.337876 
L 94.73725 118.187273 
L 95.869606 118.187273 
L 95.869606 115.886066 
L 97.001961 115.886066 
L 97.001961 114.735463 
L 102.097561 114.735463 
L 102.097561 112.434257 
L 103.229917 112.434257 
L 103.229917 111.283654 
L 105.494628 111.283654 
L 105.494628 110.133051 
L 111.722583 110.133051 
L 111.722583 108.982447 
L 112.288761 108.982447 
L 112.288761 107.831844 
L 116.252006 107.831844 
L 116.252006 105.530638 
L 117.384361 105.530638 
L 117.384361 104.380035 
L 120.21525 104.380035 
L 120.21525 103.229432 
L 124.178494 103.229432 
L 124.178494 99.777622 
L 125.31085 99.777622 
L 125.31085 97.476416 
L 128.707917 97.476416 
L 128.707917 95.175209 
L 130.40645 95.175209 
L 130.40645 94.024606 
L 134.935872 94.024606 
L 134.935872 92.874003 
L 139.465294 92.874003 
L 139.465294 91.7234 
L 145.127072 91.7234 
L 145.127072 90.572797 
L 146.259428 90.572797 
L 146.259428 89.422193 
L 146.825606 89.422193 
L 146.825606 88.27159 
L 148.524139 88.27159 
L 148.524139 85.970384 
L 149.656494 85.970384 
L 149.656494 84.819781 
L 155.318272 84.819781 
L 155.318272 83.669178 
L 156.450628 83.669178 
L 156.450628 82.518574 
L 158.715339 82.518574 
L 158.715339 81.367971 
L 160.413872 81.367971 
L 160.413872 80.217368 
L 164.943294 80.217368 
L 164.943294 79.066765 
L 172.303606 79.066765 
L 172.303606 77.916162 
L 174.568317 77.916162 
L 174.568317 76.765559 
L 177.965383 76.765559 
L 177.965383 75.614955 
L 181.36245 75.614955 
L 181.36245 73.313749 
L 184.759517 73.313749 
L 184.759517 72.163146 
L 192.686006 72.163146 
L 192.686006 69.861939 
L 202.877206 69.861939 
L 202.877206 68.711336 
L 221.561072 68.711336 
L 221.561072 67.560733 
L 223.825783 67.560733 
L 223.825783 66.41013 
L 224.391961 66.41013 
L 224.391961 65.259527 
L 226.090494 65.259527 
L 226.090494 64.108924 
L 229.487561 64.108924 
L 229.487561 62.95832 
L 230.053739 62.95832 
L 230.053739 61.807717 
L 233.450806 61.807717 
L 233.450806 60.657114 
L 241.377294 60.657114 
L 241.377294 59.506511 
L 256.664094 59.506511 
L 256.664094 58.355908 
L 259.494983 58.355908 
L 259.494983 57.205305 
L 275.347961 57.205305 
L 275.347961 56.054701 
L 278.17885 56.054701 
L 278.17885 54.904098 
L 281.575917 54.904098 
L 281.575917 53.753495 
L 291.767117 53.753495 
L 291.767117 52.602892 
L 297.428894 52.602892 
L 297.428894 51.452289 
L 303.65685 51.452289 
L 303.65685 50.301686 
L 319.509828 50.301686 
L 319.509828 49.151082 
L 334.796628 49.151082 
L 334.796628 48.000479 
L 345.554006 48.000479 
L 345.554006 46.849876 
L 377.259961 46.849876 
L 377.259961 44.54867 
L 389.715872 44.54867 
L 389.715872 43.398066 
L 448.032183 43.398066 
L 448.032183 42.247463 
L 487.09845 42.247463 
L 487.09845 41.09686 
L 553.34125 41.09686 
L 553.34125 41.09686 
" clip-path="url(#pbdb07478d9)" style="fill: none; stroke: #ff8c00; stroke-width: 2; stroke-linecap: square"/>
   </g>
   <g id="line2d_14">
    <path d="M 43.78125 386.277812 
L 553.34125 41.09686 
" clip-path="url(#pbdb07478d9)" style="fill: none; stroke-dasharray: 7.4,3.2; stroke-dashoffset: 0; stroke: #000080; stroke-width: 2"/>
   </g>
   <g id="patch_3">
    <path d="M 43.78125 386.277812 
L 43.78125 23.837812 
" style="fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square"/>
   </g>
   <g id="patch_4">
    <path d="M 553.34125 386.277812 
L 553.34125 23.837812 
" style="fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square"/>
   </g>
   <g id="patch_5">
    <path d="M 43.78125 386.277812 
L 553.34125 386.277812 
" style="fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square"/>
   </g>
   <g id="patch_6">
    <path d="M 43.78125 23.837812 
L 553.34125 23.837812 
" style="fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square"/>
   </g>
   <g id="text_15">
    <!-- Receiver Operating Characteristic -->
    <g transform="translate(164.688437 17.837812) scale(0.14 -0.14)">
     <defs>
      <path id="DejaVuSans-Bold-52" d="M 2297 2597 
Q 2675 2597 2839 2737 
Q 3003 2878 3003 3200 
Q 3003 3519 2839 3656 
Q 2675 3794 2297 3794 
L 1791 3794 
L 1791 2597 
L 2297 2597 
z
M 1791 1766 
L 1791 0 
L 588 0 
L 588 4666 
L 2425 4666 
Q 3347 4666 3776 4356 
Q 4206 4047 4206 3378 
Q 4206 2916 3982 2619 
Q 3759 2322 3309 2181 
Q 3556 2125 3751 1926 
Q 3947 1728 4147 1325 
L 4800 0 
L 3519 0 
L 2950 1159 
Q 2778 1509 2601 1637 
Q 2425 1766 2131 1766 
L 1791 1766 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-Bold-65" d="M 4031 1759 
L 4031 1441 
L 1416 1441 
Q 1456 1047 1700 850 
Q 1944 653 2381 653 
Q 2734 653 3104 758 
Q 3475 863 3866 1075 
L 3866 213 
Q 3469 63 3072 -14 
Q 2675 -91 2278 -91 
Q 1328 -91 801 392 
Q 275 875 275 1747 
Q 275 2603 792 3093 
Q 1309 3584 2216 3584 
Q 3041 3584 3536 3087 
Q 4031 2591 4031 1759 
z
M 2881 2131 
Q 2881 2450 2695 2645 
Q 2509 2841 2209 2841 
Q 1884 2841 1681 2658 
Q 1478 2475 1428 2131 
L 2881 2131 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-Bold-63" d="M 3366 3391 
L 3366 2478 
Q 3138 2634 2908 2709 
Q 2678 2784 2431 2784 
Q 1963 2784 1702 2511 
Q 1441 2238 1441 1747 
Q 1441 1256 1702 982 
Q 1963 709 2431 709 
Q 2694 709 2930 787 
Q 3166 866 3366 1019 
L 3366 103 
Q 3103 6 2833 -42 
Q 2563 -91 2291 -91 
Q 1344 -91 809 395 
Q 275 881 275 1747 
Q 275 2613 809 3098 
Q 1344 3584 2291 3584 
Q 2566 3584 2833 3536 
Q 3100 3488 3366 3391 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-Bold-69" d="M 538 3500 
L 1656 3500 
L 1656 0 
L 538 0 
L 538 3500 
z
M 538 4863 
L 1656 4863 
L 1656 3950 
L 538 3950 
L 538 4863 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-Bold-76" d="M 97 3500 
L 1216 3500 
L 2088 1081 
L 2956 3500 
L 4078 3500 
L 2700 0 
L 1472 0 
L 97 3500 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-Bold-72" d="M 3138 2547 
Q 2991 2616 2845 2648 
Q 2700 2681 2553 2681 
Q 2122 2681 1889 2404 
Q 1656 2128 1656 1613 
L 1656 0 
L 538 0 
L 538 3500 
L 1656 3500 
L 1656 2925 
Q 1872 3269 2151 3426 
Q 2431 3584 2822 3584 
Q 2878 3584 2943 3579 
Q 3009 3575 3134 3559 
L 3138 2547 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-Bold-20" transform="scale(0.015625)"/>
      <path id="DejaVuSans-Bold-4f" d="M 2719 3878 
Q 2169 3878 1866 3472 
Q 1563 3066 1563 2328 
Q 1563 1594 1866 1187 
Q 2169 781 2719 781 
Q 3272 781 3575 1187 
Q 3878 1594 3878 2328 
Q 3878 3066 3575 3472 
Q 3272 3878 2719 3878 
z
M 2719 4750 
Q 3844 4750 4481 4106 
Q 5119 3463 5119 2328 
Q 5119 1197 4481 553 
Q 3844 -91 2719 -91 
Q 1597 -91 958 553 
Q 319 1197 319 2328 
Q 319 3463 958 4106 
Q 1597 4750 2719 4750 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-Bold-70" d="M 1656 506 
L 1656 -1331 
L 538 -1331 
L 538 3500 
L 1656 3500 
L 1656 2988 
Q 1888 3294 2169 3439 
Q 2450 3584 2816 3584 
Q 3463 3584 3878 3070 
Q 4294 2556 4294 1747 
Q 4294 938 3878 423 
Q 3463 -91 2816 -91 
Q 2450 -91 2169 54 
Q 1888 200 1656 506 
z
M 2400 2772 
Q 2041 2772 1848 2508 
Q 1656 2244 1656 1747 
Q 1656 1250 1848 986 
Q 2041 722 2400 722 
Q 2759 722 2948 984 
Q 3138 1247 3138 1747 
Q 3138 2247 2948 2509 
Q 2759 2772 2400 2772 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-Bold-61" d="M 2106 1575 
Q 1756 1575 1579 1456 
Q 1403 1338 1403 1106 
Q 1403 894 1545 773 
Q 1688 653 1941 653 
Q 2256 653 2472 879 
Q 2688 1106 2688 1447 
L 2688 1575 
L 2106 1575 
z
M 3816 1997 
L 3816 0 
L 2688 0 
L 2688 519 
Q 2463 200 2181 54 
Q 1900 -91 1497 -91 
Q 953 -91 614 226 
Q 275 544 275 1050 
Q 275 1666 698 1953 
Q 1122 2241 2028 2241 
L 2688 2241 
L 2688 2328 
Q 2688 2594 2478 2717 
Q 2269 2841 1825 2841 
Q 1466 2841 1156 2769 
Q 847 2697 581 2553 
L 581 3406 
Q 941 3494 1303 3539 
Q 1666 3584 2028 3584 
Q 2975 3584 3395 3211 
Q 3816 2838 3816 1997 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-Bold-74" d="M 1759 4494 
L 1759 3500 
L 2913 3500 
L 2913 2700 
L 1759 2700 
L 1759 1216 
Q 1759 972 1856 886 
Q 1953 800 2241 800 
L 2816 800 
L 2816 0 
L 1856 0 
Q 1194 0 917 276 
Q 641 553 641 1216 
L 641 2700 
L 84 2700 
L 84 3500 
L 641 3500 
L 641 4494 
L 1759 4494 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-Bold-6e" d="M 4056 2131 
L 4056 0 
L 2931 0 
L 2931 347 
L 2931 1631 
Q 2931 2084 2911 2256 
Q 2891 2428 2841 2509 
Q 2775 2619 2662 2680 
Q 2550 2741 2406 2741 
Q 2056 2741 1856 2470 
Q 1656 2200 1656 1722 
L 1656 0 
L 538 0 
L 538 3500 
L 1656 3500 
L 1656 2988 
Q 1909 3294 2193 3439 
Q 2478 3584 2822 3584 
Q 3428 3584 3742 3212 
Q 4056 2841 4056 2131 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-Bold-67" d="M 2919 594 
Q 2688 288 2409 144 
Q 2131 0 1766 0 
Q 1125 0 706 504 
Q 288 1009 288 1791 
Q 288 2575 706 3076 
Q 1125 3578 1766 3578 
Q 2131 3578 2409 3434 
Q 2688 3291 2919 2981 
L 2919 3500 
L 4044 3500 
L 4044 353 
Q 4044 -491 3511 -936 
Q 2978 -1381 1966 -1381 
Q 1638 -1381 1331 -1331 
Q 1025 -1281 716 -1178 
L 716 -306 
Q 1009 -475 1290 -558 
Q 1572 -641 1856 -641 
Q 2406 -641 2662 -400 
Q 2919 -159 2919 353 
L 2919 594 
z
M 2181 2772 
Q 1834 2772 1640 2515 
Q 1447 2259 1447 1791 
Q 1447 1309 1634 1061 
Q 1822 813 2181 813 
Q 2531 813 2725 1069 
Q 2919 1325 2919 1791 
Q 2919 2259 2725 2515 
Q 2531 2772 2181 2772 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-Bold-43" d="M 4288 256 
Q 3956 84 3597 -3 
Q 3238 -91 2847 -91 
Q 1681 -91 1000 561 
Q 319 1213 319 2328 
Q 319 3447 1000 4098 
Q 1681 4750 2847 4750 
Q 3238 4750 3597 4662 
Q 3956 4575 4288 4403 
L 4288 3438 
Q 3953 3666 3628 3772 
Q 3303 3878 2944 3878 
Q 2300 3878 1931 3465 
Q 1563 3053 1563 2328 
Q 1563 1606 1931 1193 
Q 2300 781 2944 781 
Q 3303 781 3628 887 
Q 3953 994 4288 1222 
L 4288 256 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-Bold-68" d="M 4056 2131 
L 4056 0 
L 2931 0 
L 2931 347 
L 2931 1625 
Q 2931 2084 2911 2256 
Q 2891 2428 2841 2509 
Q 2775 2619 2662 2680 
Q 2550 2741 2406 2741 
Q 2056 2741 1856 2470 
Q 1656 2200 1656 1722 
L 1656 0 
L 538 0 
L 538 4863 
L 1656 4863 
L 1656 2988 
Q 1909 3294 2193 3439 
Q 2478 3584 2822 3584 
Q 3428 3584 3742 3212 
Q 4056 2841 4056 2131 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-Bold-73" d="M 3272 3391 
L 3272 2541 
Q 2913 2691 2578 2766 
Q 2244 2841 1947 2841 
Q 1628 2841 1473 2761 
Q 1319 2681 1319 2516 
Q 1319 2381 1436 2309 
Q 1553 2238 1856 2203 
L 2053 2175 
Q 2913 2066 3209 1816 
Q 3506 1566 3506 1031 
Q 3506 472 3093 190 
Q 2681 -91 1863 -91 
Q 1516 -91 1145 -36 
Q 775 19 384 128 
L 384 978 
Q 719 816 1070 734 
Q 1422 653 1784 653 
Q 2113 653 2278 743 
Q 2444 834 2444 1013 
Q 2444 1163 2330 1236 
Q 2216 1309 1875 1350 
L 1678 1375 
Q 931 1469 631 1722 
Q 331 1975 331 2491 
Q 331 3047 712 3315 
Q 1094 3584 1881 3584 
Q 2191 3584 2531 3537 
Q 2872 3491 3272 3391 
z
" transform="scale(0.015625)"/>
     </defs>
     <use xlink:href="#DejaVuSans-Bold-52"/>
     <use xlink:href="#DejaVuSans-Bold-65" transform="translate(77.001953 0)"/>
     <use xlink:href="#DejaVuSans-Bold-63" transform="translate(144.824219 0)"/>
     <use xlink:href="#DejaVuSans-Bold-65" transform="translate(204.101562 0)"/>
     <use xlink:href="#DejaVuSans-Bold-69" transform="translate(271.923828 0)"/>
     <use xlink:href="#DejaVuSans-Bold-76" transform="translate(306.201172 0)"/>
     <use xlink:href="#DejaVuSans-Bold-65" transform="translate(371.386719 0)"/>
     <use xlink:href="#DejaVuSans-Bold-72" transform="translate(439.208984 0)"/>
     <use xlink:href="#DejaVuSans-Bold-20" transform="translate(488.525391 0)"/>
     <use xlink:href="#DejaVuSans-Bold-4f" transform="translate(523.339844 0)"/>
     <use xlink:href="#DejaVuSans-Bold-70" transform="translate(608.349609 0)"/>
     <use xlink:href="#DejaVuSans-Bold-65" transform="translate(679.931641 0)"/>
     <use xlink:href="#DejaVuSans-Bold-72" transform="translate(747.753906 0)"/>
     <use xlink:href="#DejaVuSans-Bold-61" transform="translate(797.070312 0)"/>
     <use xlink:href="#DejaVuSans-Bold-74" transform="translate(864.550781 0)"/>
     <use xlink:href="#DejaVuSans-Bold-69" transform="translate(912.353516 0)"/>
     <use xlink:href="#DejaVuSans-Bold-6e" transform="translate(946.630859 0)"/>
     <use xlink:href="#DejaVuSans-Bold-67" transform="translate(1017.822266 0)"/>
     <use xlink:href="#DejaVuSans-Bold-20" transform="translate(1089.404297 0)"/>
     <use xlink:href="#DejaVuSans-Bold-43" transform="translate(1124.21875 0)"/>
     <use xlink:href="#DejaVuSans-Bold-68" transform="translate(1197.607422 0)"/>
     <use xlink:href="#DejaVuSans-Bold-61" transform="translate(1268.798828 0)"/>
     <use xlink:href="#DejaVuSans-Bold-72" transform="translate(1336.279297 0)"/>
     <use xlink:href="#DejaVuSans-Bold-61" transform="translate(1385.595703 0)"/>
     <use xlink:href="#DejaVuSans-Bold-63" transform="translate(1453.076172 0)"/>
     <use xlink:href="#DejaVuSans-Bold-74" transform="translate(1512.353516 0)"/>
     <use xlink:href="#DejaVuSans-Bold-65" transform="translate(1560.15625 0)"/>
     <use xlink:href="#DejaVuSans-Bold-72" transform="translate(1627.978516 0)"/>
     <use xlink:href="#DejaVuSans-Bold-69" transform="translate(1677.294922 0)"/>
     <use xlink:href="#DejaVuSans-Bold-73" transform="translate(1711.572266 0)"/>
     <use xlink:href="#DejaVuSans-Bold-74" transform="translate(1771.09375 0)"/>
     <use xlink:href="#DejaVuSans-Bold-69" transform="translate(1818.896484 0)"/>
     <use xlink:href="#DejaVuSans-Bold-63" transform="translate(1853.173828 0)"/>
    </g>
   </g>
   <g id="legend_1">
    <g id="patch_7">
     <path d="M 385.85375 381.277812 
L 546.34125 381.277812 
Q 548.34125 381.277812 548.34125 379.277812 
L 548.34125 365.599687 
Q 548.34125 363.599687 546.34125 363.599687 
L 385.85375 363.599687 
Q 383.85375 363.599687 383.85375 365.599687 
L 383.85375 379.277812 
Q 383.85375 381.277812 385.85375 381.277812 
z
" style="fill: #ffffff; opacity: 0.8; stroke: #cccccc; stroke-linejoin: miter"/>
    </g>
    <g id="line2d_15">
     <path d="M 387.85375 371.698125 
L 397.85375 371.698125 
L 407.85375 371.698125 
" style="fill: none; stroke: #ff8c00; stroke-width: 2; stroke-linecap: square"/>
    </g>
    <g id="text_16">
     <!-- ROC curve (AUC = 0.923) -->
     <g transform="translate(415.85375 375.198125) scale(0.1 -0.1)">
      <defs>
       <path id="DejaVuSans-4f" d="M 2522 4238 
Q 1834 4238 1429 3725 
Q 1025 3213 1025 2328 
Q 1025 1447 1429 934 
Q 1834 422 2522 422 
Q 3209 422 3611 934 
Q 4013 1447 4013 2328 
Q 4013 3213 3611 3725 
Q 3209 4238 2522 4238 
z
M 2522 4750 
Q 3503 4750 4090 4092 
Q 4678 3434 4678 2328 
Q 4678 1225 4090 567 
Q 3503 -91 2522 -91 
Q 1538 -91 948 565 
Q 359 1222 359 2328 
Q 359 3434 948 4092 
Q 1538 4750 2522 4750 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-43" d="M 4122 4306 
L 4122 3641 
Q 3803 3938 3442 4084 
Q 3081 4231 2675 4231 
Q 1875 4231 1450 3742 
Q 1025 3253 1025 2328 
Q 1025 1406 1450 917 
Q 1875 428 2675 428 
Q 3081 428 3442 575 
Q 3803 722 4122 1019 
L 4122 359 
Q 3791 134 3420 21 
Q 3050 -91 2638 -91 
Q 1578 -91 968 557 
Q 359 1206 359 2328 
Q 359 3453 968 4101 
Q 1578 4750 2638 4750 
Q 3056 4750 3426 4639 
Q 3797 4528 4122 4306 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-63" d="M 3122 3366 
L 3122 2828 
Q 2878 2963 2633 3030 
Q 2388 3097 2138 3097 
Q 1578 3097 1268 2742 
Q 959 2388 959 1747 
Q 959 1106 1268 751 
Q 1578 397 2138 397 
Q 2388 397 2633 464 
Q 2878 531 3122 666 
L 3122 134 
Q 2881 22 2623 -34 
Q 2366 -91 2075 -91 
Q 1284 -91 818 406 
Q 353 903 353 1747 
Q 353 2603 823 3093 
Q 1294 3584 2113 3584 
Q 2378 3584 2631 3529 
Q 2884 3475 3122 3366 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-28" d="M 1984 4856 
Q 1566 4138 1362 3434 
Q 1159 2731 1159 2009 
Q 1159 1288 1364 580 
Q 1569 -128 1984 -844 
L 1484 -844 
Q 1016 -109 783 600 
Q 550 1309 550 2009 
Q 550 2706 781 3412 
Q 1013 4119 1484 4856 
L 1984 4856 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-41" d="M 2188 4044 
L 1331 1722 
L 3047 1722 
L 2188 4044 
z
M 1831 4666 
L 2547 4666 
L 4325 0 
L 3669 0 
L 3244 1197 
L 1141 1197 
L 716 0 
L 50 0 
L 1831 4666 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-55" d="M 556 4666 
L 1191 4666 
L 1191 1831 
Q 1191 1081 1462 751 
Q 1734 422 2344 422 
Q 2950 422 3222 751 
Q 3494 1081 3494 1831 
L 3494 4666 
L 4128 4666 
L 4128 1753 
Q 4128 841 3676 375 
Q 3225 -91 2344 -91 
Q 1459 -91 1007 375 
Q 556 841 556 1753 
L 556 4666 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-3d" d="M 678 2906 
L 4684 2906 
L 4684 2381 
L 678 2381 
L 678 2906 
z
M 678 1631 
L 4684 1631 
L 4684 1100 
L 678 1100 
L 678 1631 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-39" d="M 703 97 
L 703 672 
Q 941 559 1184 500 
Q 1428 441 1663 441 
Q 2288 441 2617 861 
Q 2947 1281 2994 2138 
Q 2813 1869 2534 1725 
Q 2256 1581 1919 1581 
Q 1219 1581 811 2004 
Q 403 2428 403 3163 
Q 403 3881 828 4315 
Q 1253 4750 1959 4750 
Q 2769 4750 3195 4129 
Q 3622 3509 3622 2328 
Q 3622 1225 3098 567 
Q 2575 -91 1691 -91 
Q 1453 -91 1209 -44 
Q 966 3 703 97 
z
M 1959 2075 
Q 2384 2075 2632 2365 
Q 2881 2656 2881 3163 
Q 2881 3666 2632 3958 
Q 2384 4250 1959 4250 
Q 1534 4250 1286 3958 
Q 1038 3666 1038 3163 
Q 1038 2656 1286 2365 
Q 1534 2075 1959 2075 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-33" d="M 2597 2516 
Q 3050 2419 3304 2112 
Q 3559 1806 3559 1356 
Q 3559 666 3084 287 
Q 2609 -91 1734 -91 
Q 1441 -91 1130 -33 
Q 819 25 488 141 
L 488 750 
Q 750 597 1062 519 
Q 1375 441 1716 441 
Q 2309 441 2620 675 
Q 2931 909 2931 1356 
Q 2931 1769 2642 2001 
Q 2353 2234 1838 2234 
L 1294 2234 
L 1294 2753 
L 1863 2753 
Q 2328 2753 2575 2939 
Q 2822 3125 2822 3475 
Q 2822 3834 2567 4026 
Q 2313 4219 1838 4219 
Q 1578 4219 1281 4162 
Q 984 4106 628 3988 
L 628 4550 
Q 988 4650 1302 4700 
Q 1616 4750 1894 4750 
Q 2613 4750 3031 4423 
Q 3450 4097 3450 3541 
Q 3450 3153 3228 2886 
Q 3006 2619 2597 2516 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-29" d="M 513 4856 
L 1013 4856 
Q 1481 4119 1714 3412 
Q 1947 2706 1947 2009 
Q 1947 1309 1714 600 
Q 1481 -109 1013 -844 
L 513 -844 
Q 928 -128 1133 580 
Q 1338 1288 1338 2009 
Q 1338 2731 1133 3434 
Q 928 4138 513 4856 
z
" transform="scale(0.015625)"/>
      </defs>
      <use xlink:href="#DejaVuSans-52"/>
      <use xlink:href="#DejaVuSans-4f" transform="translate(69.482422 0)"/>
      <use xlink:href="#DejaVuSans-43" transform="translate(148.193359 0)"/>
      <use xlink:href="#DejaVuSans-20" transform="translate(218.017578 0)"/>
      <use xlink:href="#DejaVuSans-63" transform="translate(249.804688 0)"/>
      <use xlink:href="#DejaVuSans-75" transform="translate(304.785156 0)"/>
      <use xlink:href="#DejaVuSans-72" transform="translate(368.164062 0)"/>
      <use xlink:href="#DejaVuSans-76" transform="translate(409.277344 0)"/>
      <use xlink:href="#DejaVuSans-65" transform="translate(468.457031 0)"/>
      <use xlink:href="#DejaVuSans-20" transform="translate(529.980469 0)"/>
      <use xlink:href="#DejaVuSans-28" transform="translate(561.767578 0)"/>
      <use xlink:href="#DejaVuSans-41" transform="translate(600.78125 0)"/>
      <use xlink:href="#DejaVuSans-55" transform="translate(669.189453 0)"/>
      <use xlink:href="#DejaVuSans-43" transform="translate(742.382812 0)"/>
      <use xlink:href="#DejaVuSans-20" transform="translate(812.207031 0)"/>
      <use xlink:href="#DejaVuSans-3d" transform="translate(843.994141 0)"/>
      <use xlink:href="#DejaVuSans-20" transform="translate(927.783203 0)"/>
      <use xlink:href="#DejaVuSans-30" transform="translate(959.570312 0)"/>
      <use xlink:href="#DejaVuSans-2e" transform="translate(1023.193359 0)"/>
      <use xlink:href="#DejaVuSans-39" transform="translate(1054.980469 0)"/>
      <use xlink:href="#DejaVuSans-32" transform="translate(1118.603516 0)"/>
      <use xlink:href="#DejaVuSans-33" transform="translate(1182.226562 0)"/>
      <use xlink:href="#DejaVuSans-29" transform="translate(1245.849609 0)"/>
     </g>
    </g>
   </g>
  </g>
 </g>
 <defs>
  <clipPath id="pbdb07478d9">
   <rect x="43.78125" y="23.837812" width="509.56" height="362.44"/>
  </clipPath>
 </defs>
</svg>
