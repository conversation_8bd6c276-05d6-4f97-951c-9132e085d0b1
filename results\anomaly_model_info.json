{"model_type": ["IsolationForest", "LocalOutlierFactor", "OneClassSVM"], "feature_cols": ["alloc_collection_id", "assigned_memory", "assigned_memory_outlier", "average_usage", "average_usage_cpu", "average_usage_cpu_outlier", "average_usage_memory", "average_usage_memory_outlier", "cluster", "cluster_scheduling", "collection_id", "collection_type", "collections_events_type", "cpu_usage_distribution", "cpu_usage_distribution_max", "cpu_usage_distribution_max_outlier", "cpu_usage_distribution_mean", "cpu_usage_distribution_mean_outlier", "cpu_usage_distribution_median", "cpu_usage_distribution_median_outlier", "cycles_per_instruction", "duration_category", "duration_hours", "duration_integer", "duration_minutes", "duration_seconds", "end_time", "end_time_day_part", "end_time_hour", "end_time_hour_cos", "end_time_hour_sin", "end_time_hour_tan", "end_time_hour_tan_outlier", "end_time_minute", "end_time_minute_cos", "end_time_minute_sin", "end_time_minute_tan", "end_time_minute_tan_outlier", "end_time_second", "end_time_second_cos", "end_time_second_sin", "end_time_second_tan", "end_time_weekday", "end_time_weekday_cos", "end_time_weekday_sin", "end_time_weekday_tan", "failed", "instance_events_type", "instance_index", "instance_index_outlier", "machine_id", "maximum_usage", "maximum_usage_cpu", "maximum_usage_cpu_outlier", "maximum_usage_memory", "maximum_usage_memory_outlier", "memory_accesses_per_instruction", "memory_accesses_per_instruction_outlier", "negative_duration", "page_cache_memory", "page_cache_memory_outlier", "priority", "priority_outlier", "resource_request", "resource_request_cpu", "resource_request_cpu_outlier", "resource_request_memory", "resource_request_memory_outlier", "sample_rate", "scheduler", "scheduling_class", "start_time", "start_time_day_part", "start_time_hour", "start_time_hour_cos", "start_time_hour_sin", "start_time_hour_tan", "start_time_hour_tan_outlier", "start_time_minute", "start_time_minute_cos", "start_time_minute_sin", "start_time_minute_tan", "start_time_minute_tan_outlier", "start_time_second", "start_time_second_cos", "start_time_second_sin", "start_time_second_tan", "start_time_weekday", "start_time_weekday_cos", "start_time_weekday_sin", "start_time_weekday_tan", "time", "time_day_part", "time_hour", "time_hour_cos", "time_hour_sin", "time_hour_tan", "time_hour_tan_outlier", "time_minute", "time_minute_cos", "time_minute_sin", "time_minute_tan", "time_minute_tan_outlier", "time_second", "time_second_cos", "time_second_sin", "time_second_tan", "time_second_tan_outlier", "time_weekday", "time_weekday_cos", "time_weekday_sin", "time_weekday_tan", "vertical_scaling"]}