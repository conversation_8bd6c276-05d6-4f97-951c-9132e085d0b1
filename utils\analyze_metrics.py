"""
Utility script to analyze metrics logs and generate summary reports.
This script reads the metrics log files and generates summary statistics and visualizations.
"""
import os
import re
import glob
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from datetime import datetime
from typing import Dict, List, Any, Optional, Tu<PERSON>

def parse_log_file(log_file: str) -> Dict[str, Any]:
    """
    Parse a metrics log file and extract structured data.

    Args:
        log_file: Path to the log file

    Returns:
        Dictionary containing parsed data
    """
    with open(log_file, 'r') as f:
        content = f.read()

    # Extract run ID and timestamp
    run_id_match = re.search(r'# Run ID: (\w+)', content)
    timestamp_match = re.search(r'# Timestamp: ([\d-]+ [\d:]+)', content)

    run_id = run_id_match.group(1) if run_id_match else "unknown"
    timestamp = timestamp_match.group(1) if timestamp_match else "unknown"

    # Extract configuration
    config_section = re.search(r'## RUN CONFIGURATION\n(.*?)={80}', content, re.DOTALL)
    config = {}
    if config_section:
        config_text = config_section.group(1)
        for line in config_text.strip().split('\n'):
            if ': ' in line:
                key, value = line.split(': ', 1)
                config[key] = value

    # Extract regression metrics
    regression_metrics = {}
    regression_section = re.search(r'## REGRESSION METRICS\n(.*?)(?:={80}|\Z)', content, re.DOTALL)
    if regression_section:
        metrics_text = regression_section.group(1)
        # Parse the table
        lines = metrics_text.strip().split('\n')
        if len(lines) > 2:  # Header + separator + at least one data row
            header = lines[0].split()
            data_rows = lines[2:-2]  # Skip header, separator, and average row

            for row in data_rows:
                parts = row.split()
                if len(parts) >= 4:
                    target = parts[0]
                    rmse = float(parts[1])
                    mae = float(parts[2])
                    r2 = float(parts[3])
                    regression_metrics[target] = {'rmse': rmse, 'mae': mae, 'r2': r2}

            # Extract average metrics
            avg_row = lines[-1].split()
            if len(avg_row) >= 4:
                regression_metrics['average'] = {
                    'rmse': float(avg_row[1]),
                    'mae': float(avg_row[2]),
                    'r2': float(avg_row[3])
                }

    # Extract anomaly detection metrics
    anomaly_metrics = {}
    anomaly_section = re.search(r'## ANOMALY DETECTION METRICS\n(.*?)(?:={80}|\Z)', content, re.DOTALL)
    if anomaly_section:
        metrics_text = anomaly_section.group(1)

        # Extract confusion matrix
        cm_match = re.search(r'True Negatives \(TN\): (\d+)\nFalse Positives \(FP\): (\d+)\nFalse Negatives \(FN\): (\d+)\nTrue Positives \(TP\): (\d+)', metrics_text)
        if cm_match:
            tn, fp, fn, tp = map(int, cm_match.groups())
            anomaly_metrics['confusion_matrix'] = {'tn': tn, 'fp': fp, 'fn': fn, 'tp': tp}

        # Extract performance metrics
        accuracy_match = re.search(r'Accuracy:\s+([\d.]+)', metrics_text)
        precision_match = re.search(r'Precision:\s+([\d.]+)', metrics_text)
        recall_match = re.search(r'Recall:\s+([\d.]+)', metrics_text)
        f1_match = re.search(r'F1 Score:\s+([\d.]+)', metrics_text)
        specificity_match = re.search(r'Specificity:\s+([\d.]+)', metrics_text)

        if accuracy_match:
            anomaly_metrics['accuracy'] = float(accuracy_match.group(1))
        if precision_match:
            anomaly_metrics['precision'] = float(precision_match.group(1))
        if recall_match:
            anomaly_metrics['recall'] = float(recall_match.group(1))
        if f1_match:
            anomaly_metrics['f1'] = float(f1_match.group(1))
        if specificity_match:
            anomaly_metrics['specificity'] = float(specificity_match.group(1))

        # Extract additional metrics
        roc_auc_match = re.search(r'roc_auc: ([\d.]+)', metrics_text)
        pr_auc_match = re.search(r'pr_auc: ([\d.]+)', metrics_text)

        if roc_auc_match:
            anomaly_metrics['roc_auc'] = float(roc_auc_match.group(1))
        if pr_auc_match:
            anomaly_metrics['pr_auc'] = float(pr_auc_match.group(1))

    # Extract feature importance
    feature_importance = {}
    importance_section = re.search(r'## FEATURE IMPORTANCE\n(.*?)(?:={80}|\Z)', content, re.DOTALL)
    if importance_section:
        importance_text = importance_section.group(1)
        lines = importance_text.strip().split('\n')
        if len(lines) > 2:  # Header + separator + at least one data row
            for line in lines[2:]:
                parts = line.split()
                if len(parts) >= 3:
                    rank = int(parts[0])
                    feature = parts[1]
                    importance = float(parts[2])
                    feature_importance[feature] = importance

    # Extract component metrics
    component_metrics = {}
    component_sections = re.finditer(r'## COMPONENT METRICS: ([^\n]+)\n(.*?)(?:={80}|\Z)', content, re.DOTALL)

    for match in component_sections:
        component_name = match.group(1)
        component_text = match.group(2)

        # Initialize component dict
        component_metrics[component_name] = {}

        # Extract sections within component
        section_matches = re.finditer(r'### ([^\n]+)\n(.*?)(?:###|\Z)', component_text, re.DOTALL)

        for section_match in section_matches:
            section_name = section_match.group(1)
            section_text = section_match.group(2)

            # Parse metrics in this section
            section_metrics = {}
            metric_matches = re.finditer(r'([^\s]+)\s+([\d.-]+)', section_text)

            for metric_match in metric_matches:
                metric_name = metric_match.group(1)
                try:
                    metric_value = float(metric_match.group(2))
                    section_metrics[metric_name] = metric_value
                except ValueError:
                    # Skip if not a valid float
                    pass

            component_metrics[component_name][section_name] = section_metrics

    # Extract execution time
    execution_time = None
    time_section = re.search(r'## EXECUTION TIME\n(.*?)(?:={80}|\Z)', content, re.DOTALL)
    if time_section:
        time_text = time_section.group(1)
        total_time_match = re.search(r'Total execution time: ([\d.]+) seconds', time_text)
        if total_time_match:
            execution_time = float(total_time_match.group(1))

    return {
        'run_id': run_id,
        'timestamp': timestamp,
        'config': config,
        'regression_metrics': regression_metrics,
        'anomaly_metrics': anomaly_metrics,
        'feature_importance': feature_importance,
        'component_metrics': component_metrics,
        'execution_time': execution_time
    }

def analyze_metrics_logs(task_type: str = 'all') -> pd.DataFrame:
    """
    Analyze all metrics logs for a specific task type.

    Args:
        task_type: Type of task ('regression', 'anomaly', 'all')

    Returns:
        DataFrame containing summary statistics
    """
    log_dir = os.path.join("logs", "metrics", task_type)
    if not os.path.exists(log_dir):
        print(f"No logs found for task type: {task_type}")
        return pd.DataFrame()

    log_files = glob.glob(os.path.join(log_dir, "*.txt"))
    if not log_files:
        print(f"No log files found in {log_dir}")
        return pd.DataFrame()

    results = []
    for log_file in log_files:
        try:
            data = parse_log_file(log_file)

            # Extract key metrics for summary
            result = {
                'run_id': data['run_id'],
                'timestamp': data['timestamp'],
                'execution_time': data['execution_time']
            }

            # Add configuration parameters
            for key, value in data['config'].items():
                result[f'config_{key}'] = value

            # Add regression metrics if available
            if data['regression_metrics']:
                for target, metrics in data['regression_metrics'].items():
                    for metric_name, metric_value in metrics.items():
                        result[f'reg_{target}_{metric_name}'] = metric_value

            # Add anomaly metrics if available
            if data['anomaly_metrics']:
                for metric_name, metric_value in data['anomaly_metrics'].items():
                    if isinstance(metric_value, dict):
                        for subname, subvalue in metric_value.items():
                            result[f'anom_{metric_name}_{subname}'] = subvalue
                    else:
                        result[f'anom_{metric_name}'] = metric_value

            results.append(result)
        except Exception as e:
            print(f"Error parsing log file {log_file}: {e}")

    return pd.DataFrame(results)

def generate_metrics_report(df: pd.DataFrame, output_file: str = 'metrics_report.html',
                        component_metrics: Optional[Dict[str, Dict[str, Dict[str, float]]]] = None) -> None:
    """
    Generate an HTML report from metrics data.

    Args:
        df: DataFrame containing metrics data
        output_file: Path to output HTML file
        component_metrics: Optional dictionary of component-specific metrics
    """
    if df.empty:
        print("No data to generate report")
        return

    # Create output directory if it doesn't exist
    os.makedirs(os.path.dirname(output_file), exist_ok=True)

    # Generate HTML report
    html = """
    <!DOCTYPE html>
    <html>
    <head>
        <title>Metrics Analysis Report</title>
        <style>
            body { font-family: Arial, sans-serif; margin: 20px; }
            h1, h2, h3, h4 { color: #333; }
            table { border-collapse: collapse; width: 100%; margin-bottom: 20px; }
            th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
            th { background-color: #f2f2f2; }
            tr:nth-child(even) { background-color: #f9f9f9; }
            .summary { background-color: #e6f7ff; padding: 15px; border-radius: 5px; margin-bottom: 20px; }
            .component { background-color: #f0f7e6; padding: 15px; border-radius: 5px; margin-bottom: 20px; }
            .collapsible {
                background-color: #f1f1f1;
                color: #444;
                cursor: pointer;
                padding: 18px;
                width: 100%;
                border: none;
                text-align: left;
                outline: none;
                font-size: 15px;
                margin-bottom: 5px;
            }
            .active, .collapsible:hover { background-color: #ccc; }
            .content {
                padding: 0 18px;
                display: none;
                overflow: hidden;
                background-color: #f9f9f9;
            }
        </style>
        <script>
            document.addEventListener('DOMContentLoaded', function() {
                var coll = document.getElementsByClassName("collapsible");
                for (var i = 0; i < coll.length; i++) {
                    coll[i].addEventListener("click", function() {
                        this.classList.toggle("active");
                        var content = this.nextElementSibling;
                        if (content.style.display === "block") {
                            content.style.display = "none";
                        } else {
                            content.style.display = "block";
                        }
                    });
                }
            });
        </script>
    </head>
    <body>
        <h1>Metrics Analysis Report</h1>
        <div class="summary">
            <h2>Summary Statistics</h2>
            <p>Total runs analyzed: {total_runs}</p>
            <p>Date range: {date_range}</p>
            <p>Average execution time: {avg_time:.2f} seconds</p>
        </div>
    """.format(
        total_runs=len(df),
        date_range=f"{df['timestamp'].min()} to {df['timestamp'].max()}" if 'timestamp' in df.columns else "Unknown",
        avg_time=df['execution_time'].mean() if 'execution_time' in df.columns else 0
    )

    # Add regression metrics summary if available
    reg_cols = [col for col in df.columns if col.startswith('reg_')]
    if reg_cols:
        html += """
        <h2>Regression Metrics Summary</h2>
        <table>
            <tr>
                <th>Metric</th>
                <th>Min</th>
                <th>Max</th>
                <th>Mean</th>
                <th>Std Dev</th>
            </tr>
        """

        for col in reg_cols:
            if df[col].dtype in [np.float64, np.int64]:
                html += f"""
                <tr>
                    <td>{col}</td>
                    <td>{df[col].min():.4f}</td>
                    <td>{df[col].max():.4f}</td>
                    <td>{df[col].mean():.4f}</td>
                    <td>{df[col].std():.4f}</td>
                </tr>
                """

        html += "</table>"

    # Add anomaly metrics summary if available
    anom_cols = [col for col in df.columns if col.startswith('anom_')]
    if anom_cols:
        html += """
        <h2>Anomaly Detection Metrics Summary</h2>
        <table>
            <tr>
                <th>Metric</th>
                <th>Min</th>
                <th>Max</th>
                <th>Mean</th>
                <th>Std Dev</th>
            </tr>
        """

        for col in anom_cols:
            if df[col].dtype in [np.float64, np.int64]:
                html += f"""
                <tr>
                    <td>{col}</td>
                    <td>{df[col].min():.4f}</td>
                    <td>{df[col].max():.4f}</td>
                    <td>{df[col].mean():.4f}</td>
                    <td>{df[col].std():.4f}</td>
                </tr>
                """

        html += "</table>"

    # Add component metrics if available
    comp_cols = [col for col in df.columns if col.startswith('comp_')]
    if comp_cols or component_metrics:
        html += """
        <h2>Component Metrics</h2>
        <div class="component">
        """

        # Add component metrics from DataFrame
        if comp_cols:
            html += """
            <h3>Component Summary</h3>
            <table>
                <tr>
                    <th>Metric</th>
                    <th>Min</th>
                    <th>Max</th>
                    <th>Mean</th>
                    <th>Std Dev</th>
                </tr>
            """

            for col in comp_cols:
                if df[col].dtype in [np.float64, np.int64]:
                    html += f"""
                    <tr>
                        <td>{col}</td>
                        <td>{df[col].min():.4f}</td>
                        <td>{df[col].max():.4f}</td>
                        <td>{df[col].mean():.4f}</td>
                        <td>{df[col].std():.4f}</td>
                    </tr>
                    """

            html += "</table>"

        # Add detailed component metrics
        if component_metrics:
            html += "<h3>Detailed Component Metrics</h3>"

            for component_name, sections in component_metrics.items():
                html += f"""
                <button class="collapsible">{component_name}</button>
                <div class="content">
                    <h4>{component_name}</h4>
                """

                for section_name, metrics in sections.items():
                    html += f"""
                    <h5>{section_name}</h5>
                    <table>
                        <tr>
                            <th>Metric</th>
                            <th>Value</th>
                        </tr>
                    """

                    for metric_name, metric_value in metrics.items():
                        html += f"""
                        <tr>
                            <td>{metric_name}</td>
                            <td>{metric_value:.6f if isinstance(metric_value, float) else metric_value}</td>
                        </tr>
                        """

                    html += "</table>"

                html += "</div>"

        html += "</div>"

    # Add raw data table
    html += """
    <h2>All Runs</h2>
    <button class="collapsible">Show/Hide Raw Data</button>
    <div class="content">
        <table>
            <tr>
    """

    for col in df.columns:
        html += f"<th>{col}</th>"

    html += "</tr>"

    for _, row in df.iterrows():
        html += "<tr>"
        for col in df.columns:
            value = row[col]
            if isinstance(value, (float, np.float64)):
                html += f"<td>{value:.4f}</td>"
            else:
                html += f"<td>{value}</td>"
        html += "</tr>"

    html += """
        </table>
    </div>
    </body>
    </html>
    """

    with open(output_file, 'w') as f:
        f.write(html)

    print(f"Report generated: {output_file}")

def extract_component_metrics(task_type: str = 'all') -> Dict[str, Dict[str, Dict[str, float]]]:
    """
    Extract component metrics from log files for a specific task type.

    Args:
        task_type: Type of task ('regression', 'anomaly', 'all')

    Returns:
        Dictionary of component metrics
    """
    log_dir = os.path.join("logs", "metrics", task_type)
    if not os.path.exists(log_dir):
        return {}

    log_files = glob.glob(os.path.join(log_dir, "*.txt"))
    if not log_files:
        return {}

    # Get the most recent log file
    latest_log = max(log_files, key=os.path.getmtime)

    try:
        data = parse_log_file(latest_log)
        return data.get('component_metrics', {})
    except Exception as e:
        print(f"Error extracting component metrics: {e}")
        return {}

def main():
    """Main function to run metrics analysis"""
    import argparse

    parser = argparse.ArgumentParser(description='Analyze metrics logs')
    parser.add_argument('--task', type=str, default='all', choices=['regression', 'anomaly', 'all'],
                       help='Task type to analyze')
    parser.add_argument('--output', type=str, default='logs/metrics_report.html',
                       help='Output file for the report')
    parser.add_argument('--include_components', action='store_true',
                       help='Include detailed component metrics in the report')
    args = parser.parse_args()

    print(f"Analyzing metrics logs for task: {args.task}")
    df = analyze_metrics_logs(args.task)

    component_metrics = None
    if args.include_components:
        print("Extracting component metrics...")
        component_metrics = extract_component_metrics(args.task)

    if not df.empty:
        generate_metrics_report(df, args.output, component_metrics)
        print(f"Analysis complete. Report saved to: {args.output}")
    else:
        print("No data found for analysis")

if __name__ == "__main__":
    main()
