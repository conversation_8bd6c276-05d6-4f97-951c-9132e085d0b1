"""
Data loading and preprocessing utilities for GMTrans-GBFE model
"""
import pandas as pd
import numpy as np
from sklearn.preprocessing import StandardScaler, MinMaxScaler
from sklearn.model_selection import train_test_split

def load_data(file_path='data/processed/borg_traces_cleaned.csv', sample_size=None):
    """Load data from CSV file with optional sampling"""
    if sample_size:
        # Load a random sample for faster processing
        df = pd.read_csv(file_path, skiprows=lambda i: i>0 and np.random.random() > sample_size)
    else:
        # Load full dataset
        df = pd.read_csv(file_path)
    
    return df

def preprocess_data(df, target_cols=['average_usage_cpu', 'average_usage_memory'], 
                   exclude_cols=['assigned_memory'], scale_method='standard'):
    """Preprocess data for model training
    
    Args:
        df: Input dataframe
        target_cols: Columns to predict
        exclude_cols: Columns to exclude from features
        scale_method: Scaling method ('standard' or 'minmax')
        
    Returns:
        X: Features
        y: Targets
        feature_cols: Feature column names
        scaler_X: Feature scaler
        scaler_y: Target scaler
    """
    # Convert all columns to numeric
    for col in df.columns:
        df[col] = pd.to_numeric(df[col], errors='coerce')
    
    # Fill missing values
    df = df.fillna(0)
    
    # Exclude target columns and specified columns from features
    feature_cols = [col for col in df.columns if col not in target_cols + exclude_cols]
    
    # Scale features
    if scale_method == 'standard':
        scaler_X = StandardScaler()
        scaler_y = StandardScaler()
    else:
        scaler_X = MinMaxScaler()
        scaler_y = MinMaxScaler()
    
    X = scaler_X.fit_transform(df[feature_cols])
    y = scaler_y.fit_transform(df[target_cols])
    
    return X, y, feature_cols, scaler_X, scaler_y

def split_data(X, y, test_size=0.2, val_size=0.1, random_state=42):
    """Split data into train, validation, and test sets"""
    # First split: separate test set
    X_train_val, X_test, y_train_val, y_test = train_test_split(
        X, y, test_size=test_size, random_state=random_state
    )
    
    # Second split: separate validation set from training set
    if val_size > 0:
        val_ratio = val_size / (1 - test_size)  # Adjust validation ratio
        X_train, X_val, y_train, y_val = train_test_split(
            X_train_val, y_train_val, test_size=val_ratio, random_state=random_state
        )
        return X_train, X_val, X_test, y_train, y_val, y_test
    else:
        return X_train_val, None, X_test, y_train_val, None, y_test

def prepare_data_for_clustering(df, n_features=20, exclude_cols=['assigned_memory']):
    """Prepare data for clustering by selecting most important features"""
    # Convert all columns to numeric
    for col in df.columns:
        df[col] = pd.to_numeric(df[col], errors='coerce')
    
    # Fill missing values
    df = df.fillna(0)
    
    # Exclude specified columns
    feature_cols = [col for col in df.columns if col not in exclude_cols]
    
    # Calculate variance for feature selection
    variances = df[feature_cols].var().sort_values(ascending=False)
    selected_features = variances.index[:n_features].tolist()
    
    # Scale features
    scaler = StandardScaler()
    X = scaler.fit_transform(df[selected_features])
    
    return X, selected_features, scaler

def prepare_data_for_anomaly_detection(df, target_cols=['average_usage_cpu', 'average_usage_memory'], 
                                     exclude_cols=['assigned_memory']):
    """Prepare data for anomaly detection"""
    # Convert all columns to numeric
    for col in df.columns:
        df[col] = pd.to_numeric(df[col], errors='coerce')
    
    # Fill missing values
    df = df.fillna(0)
    
    # Include target columns and exclude specified columns
    feature_cols = [col for col in df.columns if col not in exclude_cols]
    
    # Scale features
    scaler = StandardScaler()
    X = scaler.fit_transform(df[feature_cols])
    
    return X, feature_cols, scaler
