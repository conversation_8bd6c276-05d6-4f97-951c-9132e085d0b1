"""
Evaluation utilities for GMTrans-GBFE model
"""
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from sklearn.metrics import (
    mean_squared_error, mean_absolute_error, r2_score,
    confusion_matrix, precision_recall_curve, roc_curve, auc,
    silhouette_score, calinski_harabasz_score, davies_bouldin_score
)
import pandas as pd

def regression_metrics(y_true, y_pred):
    """Calculate regression metrics

    Args:
        y_true: True values
        y_pred: Predicted values

    Returns:
        metrics: Dictionary of metrics
    """
    # Handle multi-output case
    if len(y_true.shape) > 1 and y_true.shape[1] > 1:
        metrics = {}
        for i in range(y_true.shape[1]):
            metrics[f'output_{i}'] = {
                'mse': mean_squared_error(y_true[:, i], y_pred[:, i]),
                'rmse': np.sqrt(mean_squared_error(y_true[:, i], y_pred[:, i])),
                'mae': mean_absolute_error(y_true[:, i], y_pred[:, i]),
                'r2': r2_score(y_true[:, i], y_pred[:, i])
            }

        # Add average metrics
        metrics['average'] = {
            'mse': np.mean([metrics[f'output_{i}']['mse'] for i in range(y_true.shape[1])]),
            'rmse': np.mean([metrics[f'output_{i}']['rmse'] for i in range(y_true.shape[1])]),
            'mae': np.mean([metrics[f'output_{i}']['mae'] for i in range(y_true.shape[1])]),
            'r2': np.mean([metrics[f'output_{i}']['r2'] for i in range(y_true.shape[1])])
        }
    else:
        # Single output case
        metrics = {
            'mse': mean_squared_error(y_true, y_pred),
            'rmse': np.sqrt(mean_squared_error(y_true, y_pred)),
            'mae': mean_absolute_error(y_true, y_pred),
            'r2': r2_score(y_true, y_pred)
        }

    return metrics

def clustering_metrics(X, labels):
    """Calculate clustering metrics

    Args:
        X: Input features
        labels: Cluster labels

    Returns:
        metrics: Dictionary of metrics
    """
    metrics = {
        'silhouette': silhouette_score(X, labels),
        'calinski_harabasz': calinski_harabasz_score(X, labels),
        'davies_bouldin': davies_bouldin_score(X, labels)
    }

    return metrics

def anomaly_detection_metrics(y_true, anomaly_scores, threshold=None):
    """Calculate anomaly detection metrics

    Args:
        y_true: True anomaly labels
        anomaly_scores: Anomaly scores
        threshold: Threshold for anomaly detection (if None, use ROC curve)

    Returns:
        metrics: Dictionary of metrics
    """
    # If threshold is not provided, find optimal threshold using ROC curve
    if threshold is None:
        fpr, tpr, thresholds = roc_curve(y_true, anomaly_scores)
        optimal_idx = np.argmax(tpr - fpr)
        threshold = thresholds[optimal_idx]

    # Convert scores to binary predictions
    y_pred = (anomaly_scores > threshold).astype(int)

    # Calculate confusion matrix
    tn, fp, fn, tp = confusion_matrix(y_true, y_pred).ravel()

    # Calculate metrics
    precision = tp / (tp + fp) if (tp + fp) > 0 else 0
    recall = tp / (tp + fn) if (tp + fn) > 0 else 0
    f1 = 2 * precision * recall / (precision + recall) if (precision + recall) > 0 else 0

    # Calculate ROC AUC
    roc_auc = auc(*roc_curve(y_true, anomaly_scores)[:2])

    # Calculate PR AUC
    pr_auc = auc(*precision_recall_curve(y_true, anomaly_scores)[:2])

    metrics = {
        'threshold': threshold,
        'precision': precision,
        'recall': recall,
        'f1': f1,
        'roc_auc': roc_auc,
        'pr_auc': pr_auc,
        'confusion_matrix': {
            'tn': tn,
            'fp': fp,
            'fn': fn,
            'tp': tp
        }
    }

    return metrics

def plot_training_history(history):
    """Plot training history

    Args:
        history: Training history dictionary
    """
    plt.figure(figsize=(12, 4))

    # Plot training and validation loss
    plt.subplot(1, 2, 1)
    plt.plot(history['train_loss'], label='Train Loss')
    if 'val_loss' in history and history['val_loss']:
        plt.plot(history['val_loss'], label='Validation Loss')
    plt.xlabel('Epoch')
    plt.ylabel('Loss')
    plt.title('Training and Validation Loss')
    plt.legend()
    plt.grid(True, alpha=0.3)

    # Plot contrastive loss
    if 'contrastive_loss' in history and history['contrastive_loss']:
        plt.subplot(1, 2, 2)
        plt.plot(history['contrastive_loss'], label='Contrastive Loss')
        plt.xlabel('Epoch')
        plt.ylabel('Loss')
        plt.title('Contrastive Loss')
        plt.legend()
        plt.grid(True, alpha=0.3)

    plt.tight_layout()
    plt.savefig('results/training_history.png', dpi=300, bbox_inches='tight')
    plt.close()

def plot_regression_results(y_true, y_pred, feature_names=None, output_names=None):
    """Plot regression results

    Args:
        y_true: True values
        y_pred: Predicted values
        feature_names: Feature names
        output_names: Output names
    """
    # Handle multi-output case
    if len(y_true.shape) > 1 and y_true.shape[1] > 1:
        n_outputs = y_true.shape[1]
        output_names = output_names or [f'Output {i}' for i in range(n_outputs)]

        # Create figure
        plt.figure(figsize=(15, 5 * n_outputs))

        for i in range(n_outputs):
            # Scatter plot
            plt.subplot(n_outputs, 2, 2*i + 1)
            plt.scatter(y_true[:, i], y_pred[:, i], alpha=0.5)
            plt.plot([y_true[:, i].min(), y_true[:, i].max()],
                    [y_true[:, i].min(), y_true[:, i].max()], 'r--')
            plt.xlabel(f'True {output_names[i]}')
            plt.ylabel(f'Predicted {output_names[i]}')
            plt.title(f'{output_names[i]} - True vs Predicted')
            plt.grid(True, alpha=0.3)

            # Residual plot
            plt.subplot(n_outputs, 2, 2*i + 2)
            residuals = y_pred[:, i] - y_true[:, i]
            plt.scatter(y_pred[:, i], residuals, alpha=0.5)
            plt.axhline(y=0, color='r', linestyle='--')
            plt.xlabel(f'Predicted {output_names[i]}')
            plt.ylabel('Residuals')
            plt.title(f'{output_names[i]} - Residuals')
            plt.grid(True, alpha=0.3)
    else:
        # Single output case
        output_name = output_names[0] if output_names else 'Output'

        # Create figure
        plt.figure(figsize=(15, 5))

        # Scatter plot
        plt.subplot(1, 2, 1)
        plt.scatter(y_true, y_pred, alpha=0.5)
        plt.plot([y_true.min(), y_true.max()], [y_true.min(), y_true.max()], 'r--')
        plt.xlabel(f'True {output_name}')
        plt.ylabel(f'Predicted {output_name}')
        plt.title(f'{output_name} - True vs Predicted')
        plt.grid(True, alpha=0.3)

        # Residual plot
        plt.subplot(1, 2, 2)
        residuals = y_pred - y_true
        plt.scatter(y_pred, residuals, alpha=0.5)
        plt.axhline(y=0, color='r', linestyle='--')
        plt.xlabel(f'Predicted {output_name}')
        plt.ylabel('Residuals')
        plt.title(f'{output_name} - Residuals')
        plt.grid(True, alpha=0.3)

    plt.tight_layout()
    plt.savefig('results/regression_results.png', dpi=300, bbox_inches='tight')
    plt.close()

def plot_feature_importance(feature_importance, feature_names=None):
    """Plot feature importance

    Args:
        feature_importance: Feature importance scores
        feature_names: Feature names
    """
    # Sort feature importance
    if feature_names is None:
        feature_names = [f'Feature {i}' for i in range(len(feature_importance))]

    # Create dataframe
    df = pd.DataFrame({
        'Feature': feature_names,
        'Importance': feature_importance
    })
    df = df.sort_values('Importance', ascending=False)

    # Plot
    plt.figure(figsize=(12, 8))
    sns.barplot(x='Importance', y='Feature', data=df.head(20))
    plt.title('Feature Importance')
    plt.grid(True, alpha=0.3)
    plt.tight_layout()
    plt.savefig('results/feature_importance.png', dpi=300, bbox_inches='tight')
    plt.close()

def plot_clustering_results(X, labels, n_components=None, feature_names=None):
    """Plot clustering results

    Args:
        X: Input features
        labels: Cluster labels
        n_components: Number of components for dimensionality reduction
        feature_names: Feature names
    """
    from sklearn.decomposition import PCA
    from sklearn.manifold import TSNE

    # Apply dimensionality reduction
    if X.shape[1] > 2:
        # First reduce with PCA to n_components or 50 (whichever is smaller)
        n_components = min(n_components or 50, X.shape[1], X.shape[0])
        pca = PCA(n_components=n_components)
        X_pca = pca.fit_transform(X)

        # Then apply t-SNE for visualization
        tsne = TSNE(n_components=2, random_state=42)
        X_tsne = tsne.fit_transform(X_pca)

        # Create dataframe
        df = pd.DataFrame({
            'x': X_tsne[:, 0],
            'y': X_tsne[:, 1],
            'cluster': labels
        })
    else:
        # Create dataframe directly
        df = pd.DataFrame({
            'x': X[:, 0],
            'y': X[:, 1],
            'cluster': labels
        })

    # Plot
    plt.figure(figsize=(10, 8))
    sns.scatterplot(x='x', y='y', hue='cluster', data=df, palette='viridis')
    plt.title('Clustering Results')
    plt.xlabel('Component 1')
    plt.ylabel('Component 2')
    plt.grid(True, alpha=0.3)
    plt.tight_layout()
    plt.savefig('results/clustering_results.png', dpi=300, bbox_inches='tight')
    plt.close()

    # Plot cluster distribution
    plt.figure(figsize=(12, 6))
    cluster_counts = df['cluster'].value_counts().sort_index()
    sns.barplot(x=cluster_counts.index, y=cluster_counts.values)
    plt.title('Cluster Distribution')
    plt.xlabel('Cluster')
    plt.ylabel('Count')
    plt.grid(True, alpha=0.3)
    plt.tight_layout()
    plt.savefig('results/cluster_distribution.png', dpi=300, bbox_inches='tight')
    plt.close()

def plot_anomaly_detection_results(y_true, anomaly_scores):
    """Plot anomaly detection results

    Args:
        y_true: True anomaly labels
        anomaly_scores: Anomaly scores
    """
    # Calculate ROC curve
    fpr, tpr, thresholds = roc_curve(y_true, anomaly_scores)
    roc_auc = auc(fpr, tpr)

    # Calculate PR curve
    precision, recall, _ = precision_recall_curve(y_true, anomaly_scores)
    pr_auc = auc(recall, precision)

    # Create figure
    plt.figure(figsize=(15, 5))

    # ROC curve
    plt.subplot(1, 2, 1)
    plt.plot(fpr, tpr, label=f'ROC curve (AUC = {roc_auc:.3f})')
    plt.plot([0, 1], [0, 1], 'k--')
    plt.xlabel('False Positive Rate')
    plt.ylabel('True Positive Rate')
    plt.title('ROC Curve')
    plt.legend(loc='lower right')
    plt.grid(True, alpha=0.3)

    # PR curve
    plt.subplot(1, 2, 2)
    plt.plot(recall, precision, label=f'PR curve (AUC = {pr_auc:.3f})')
    plt.xlabel('Recall')
    plt.ylabel('Precision')
    plt.title('Precision-Recall Curve')
    plt.legend(loc='lower left')
    plt.grid(True, alpha=0.3)

    plt.tight_layout()
    plt.savefig('results/anomaly_detection_results.png', dpi=300, bbox_inches='tight')
    plt.close()

    # Plot score distribution
    plt.figure(figsize=(12, 6))
    plt.hist(anomaly_scores[y_true == 0], bins=50, alpha=0.5, label='Normal')
    plt.hist(anomaly_scores[y_true == 1], bins=50, alpha=0.5, label='Anomaly')
    plt.xlabel('Anomaly Score')
    plt.ylabel('Count')
    plt.title('Anomaly Score Distribution')
    plt.legend()
    plt.grid(True, alpha=0.3)
    plt.tight_layout()
    plt.savefig('results/anomaly_score_distribution.png', dpi=300, bbox_inches='tight')
    plt.close()

def plot_attention_maps(attention_weights, component_attention, feature_names=None):
    """Plot attention maps

    Args:
        attention_weights: Attention weights from transformer
        component_attention: Component attention weights
        feature_names: Feature names
    """
    # Process attention weights
    n_layers = len(attention_weights)

    # Create figure for attention weights
    plt.figure(figsize=(15, 5 * n_layers))

    for i in range(n_layers):
        plt.subplot(n_layers, 1, i + 1)
        sns.heatmap(attention_weights[i][0], cmap='viridis')
        plt.title(f'Layer {i+1} Attention Weights')
        plt.xlabel('Key Position')
        plt.ylabel('Query Position')

    plt.tight_layout()
    plt.savefig('results/attention_weights.png', dpi=300, bbox_inches='tight')
    plt.close()

    # Process component attention
    n_layers = len(component_attention)
    n_components = component_attention[0].shape[-1]

    # Create figure for component attention
    plt.figure(figsize=(15, 5 * n_layers))

    for i in range(n_layers):
        plt.subplot(n_layers, 1, i + 1)
        sns.heatmap(component_attention[i][0], cmap='viridis')
        plt.title(f'Layer {i+1} Component Attention')
        plt.xlabel('Component')
        plt.ylabel('Position')

    plt.tight_layout()
    plt.savefig('results/component_attention.png', dpi=300, bbox_inches='tight')
    plt.close()

def plot_uncertainty_calibration(y_true, y_pred, uncertainty):
    """Plot uncertainty calibration

    Args:
        y_true: True values
        y_pred: Predicted values
        uncertainty: Uncertainty estimates
    """
    # Calculate absolute error
    if len(y_true.shape) > 1 and y_true.shape[1] > 1:
        abs_error = np.mean(np.abs(y_pred - y_true), axis=1)
        uncertainty = np.mean(uncertainty, axis=1)
    else:
        abs_error = np.abs(y_pred - y_true)

    # Sort by uncertainty
    sorted_indices = np.argsort(uncertainty)
    sorted_error = abs_error[sorted_indices]
    sorted_uncertainty = uncertainty[sorted_indices]

    # Calculate moving average
    window_size = max(1, len(sorted_error) // 20)
    error_ma = np.convolve(sorted_error, np.ones(window_size)/window_size, mode='valid')
    uncertainty_ma = np.convolve(sorted_uncertainty, np.ones(window_size)/window_size, mode='valid')

    # Create figure
    plt.figure(figsize=(12, 6))

    # Plot error vs uncertainty
    plt.scatter(uncertainty, abs_error, alpha=0.3, label='Individual Predictions')
    plt.plot(uncertainty_ma, error_ma, 'r-', linewidth=2, label='Moving Average')

    # Add diagonal line for perfect calibration
    max_val = max(np.max(uncertainty), np.max(abs_error))
    plt.plot([0, max_val], [0, max_val], 'k--', label='Perfect Calibration')

    plt.xlabel('Predicted Uncertainty')
    plt.ylabel('Absolute Error')
    plt.title('Uncertainty Calibration')
    plt.legend()
    plt.grid(True, alpha=0.3)
    plt.tight_layout()
    plt.savefig('results/uncertainty_calibration.png', dpi=300, bbox_inches='tight')
    plt.close()

    # Calculate correlation with proper handling of edge cases
    # Check for constant values (zero standard deviation)
    u_std = np.std(uncertainty)
    e_std = np.std(abs_error)

    if u_std < 1e-10 or e_std < 1e-10:
        correlation = 0  # No correlation if either variable is constant
    else:
        # Center the data
        u_centered = uncertainty - np.mean(uncertainty)
        e_centered = abs_error - np.mean(abs_error)

        # Calculate correlation coefficient
        numerator = np.sum(u_centered * e_centered)
        denominator = np.sqrt(np.sum(u_centered**2)) * np.sqrt(np.sum(e_centered**2))

        # Avoid division by zero
        if denominator > 1e-10:
            correlation = numerator / denominator
            # Handle potential numerical issues
            if correlation > 1.0: correlation = 1.0
            if correlation < -1.0: correlation = -1.0
        else:
            correlation = 0

    # Create bins based on uncertainty percentiles
    n_bins = 10
    bin_edges = np.percentile(uncertainty, np.linspace(0, 100, n_bins + 1))
    bin_indices = np.digitize(uncertainty, bin_edges)

    # Calculate mean error and uncertainty for each bin
    bin_mean_error = np.zeros(n_bins)
    bin_mean_uncertainty = np.zeros(n_bins)

    for i in range(n_bins):
        bin_mask = (bin_indices == i + 1)
        if np.sum(bin_mask) > 0:
            bin_mean_error[i] = np.mean(abs_error[bin_mask])
            bin_mean_uncertainty[i] = np.mean(uncertainty[bin_mask])

    # Create figure for binned calibration
    plt.figure(figsize=(12, 6))

    plt.bar(range(n_bins), bin_mean_error, alpha=0.5, label='Mean Absolute Error')
    plt.bar(range(n_bins), bin_mean_uncertainty, alpha=0.5, label='Mean Uncertainty')

    plt.xlabel('Uncertainty Bin (Low to High)')
    plt.ylabel('Value')
    plt.title(f'Binned Uncertainty Calibration (Correlation: {correlation:.3f})')
    plt.legend()
    plt.grid(True, alpha=0.3)
    plt.tight_layout()
    plt.savefig('results/binned_uncertainty_calibration.png', dpi=300, bbox_inches='tight')
    plt.close()

def plot_model_comparison(model_names, metrics, metric_name='rmse'):
    """Plot model comparison

    Args:
        model_names: List of model names
        metrics: Dictionary of metrics for each model
        metric_name: Metric to compare
    """
    # Extract metric values
    metric_values = []
    for model in model_names:
        if isinstance(metrics[model], dict) and 'average' in metrics[model]:
            # Multi-output case
            metric_values.append(metrics[model]['average'][metric_name])
        else:
            # Single output case
            metric_values.append(metrics[model][metric_name])

    # Create figure
    plt.figure(figsize=(12, 6))

    # Create bar plot
    bars = plt.bar(model_names, metric_values)

    # Add values on top of bars
    for bar in bars:
        height = bar.get_height()
        plt.text(bar.get_x() + bar.get_width()/2., height,
                f'{height:.4f}', ha='center', va='bottom')

    plt.xlabel('Model')
    plt.ylabel(metric_name.upper())
    plt.title(f'Model Comparison - {metric_name.upper()}')
    plt.grid(True, alpha=0.3)
    plt.tight_layout()
    plt.savefig(f'results/model_comparison_{metric_name}.png', dpi=300, bbox_inches='tight')
    plt.close()

def plot_computational_efficiency(model_names, train_times, inference_times):
    """Plot computational efficiency

    Args:
        model_names: List of model names
        train_times: Dictionary of training times for each model
        inference_times: Dictionary of inference times for each model
    """
    # Create figure
    plt.figure(figsize=(15, 6))

    # Training time
    plt.subplot(1, 2, 1)
    bars = plt.bar(model_names, [train_times[model] for model in model_names])

    # Add values on top of bars
    for bar in bars:
        height = bar.get_height()
        plt.text(bar.get_x() + bar.get_width()/2., height,
                f'{height:.2f}s', ha='center', va='bottom')

    plt.xlabel('Model')
    plt.ylabel('Time (seconds)')
    plt.title('Training Time')
    plt.grid(True, alpha=0.3)

    # Inference time
    plt.subplot(1, 2, 2)
    bars = plt.bar(model_names, [inference_times[model] for model in model_names])

    # Add values on top of bars
    for bar in bars:
        height = bar.get_height()
        plt.text(bar.get_x() + bar.get_width()/2., height,
                f'{height:.4f}s', ha='center', va='bottom')

    plt.xlabel('Model')
    plt.ylabel('Time (seconds)')
    plt.title('Inference Time (per sample)')
    plt.grid(True, alpha=0.3)

    plt.tight_layout()
    plt.savefig('results/computational_efficiency.png', dpi=300, bbox_inches='tight')
    plt.close()

def plot_memory_usage_prediction(y_true, y_pred, timestamps=None):
    """Plot memory usage prediction

    Args:
        y_true: True memory usage
        y_pred: Predicted memory usage
        timestamps: Timestamps for x-axis
    """
    if timestamps is None:
        timestamps = np.arange(len(y_true))

    # Create figure
    plt.figure(figsize=(15, 6))

    plt.plot(timestamps, y_true, label='Actual')
    plt.plot(timestamps, y_pred, label='Predicted')

    plt.xlabel('Time')
    plt.ylabel('Memory Usage')
    plt.title('Memory Usage Prediction')
    plt.legend()
    plt.grid(True, alpha=0.3)

    plt.tight_layout()
    plt.savefig('results/memory_usage_prediction.png', dpi=300, bbox_inches='tight')
    plt.close()

    # Plot zoomed in view of a segment
    segment_size = min(1000, len(y_true))
    start_idx = np.random.randint(0, len(y_true) - segment_size)

    plt.figure(figsize=(15, 6))

    plt.plot(timestamps[start_idx:start_idx+segment_size],
             y_true[start_idx:start_idx+segment_size], label='Actual')
    plt.plot(timestamps[start_idx:start_idx+segment_size],
             y_pred[start_idx:start_idx+segment_size], label='Predicted')

    plt.xlabel('Time')
    plt.ylabel('Memory Usage')
    plt.title('Memory Usage Prediction (Zoomed)')
    plt.legend()
    plt.grid(True, alpha=0.3)

    plt.tight_layout()
    plt.savefig('results/memory_usage_prediction_zoomed.png', dpi=300, bbox_inches='tight')
    plt.close()
