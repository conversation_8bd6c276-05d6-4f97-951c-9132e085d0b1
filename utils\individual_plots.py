"""Visualization utilities"""
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
import pandas as pd
import os
from sklearn.metrics import r2_score, confusion_matrix, precision_recall_curve, roc_curve, auc
from sklearn.decomposition import PCA
from sklearn.preprocessing import StandardScaler
from sklearn.manifold import TSNE
try:
    import umap
    UMAP_AVAILABLE = True
except ImportError:
    UMAP_AVAILABLE = False

def ensure_dir(d):
    """Ensure directory exists, creating it if necessary."""
    if not os.path.exists(d):
        os.makedirs(d, exist_ok=True)
    return d

def create_result_dirs():
    base = 'results'
    ensure_dir(base)
    cats = ['regression', 'anomaly', 'clustering', 'training', 'common', 'dimensionality_reduction']
    return {c: ensure_dir(os.path.join(base, c)) for c in cats}

RESULT_DIRS = create_result_dirs()

def create_regression_prediction_plot(y_true, y_pred, output_names=None):
    """Create regression predictions plot"""
    save_dir = RESULT_DIRS['regression']
    plt.figure(figsize=(12, 10))  # Increased figure size

    # Handle multi-output case
    if len(y_true.shape) > 1 and y_true.shape[1] > 1:
        n_outputs = y_true.shape[1]
        output_names = output_names or [f'Output {i+1}' for i in range(n_outputs)]

        # Use dynamic range starting at 0 and extending to the maximum value
        # Handle potential NaN values when calculating min/max
        y_true_max = np.nanmax(y_true) if not np.all(np.isnan(y_true)) else 0
        y_pred_max = np.nanmax(y_pred) if not np.all(np.isnan(y_pred)) else 0
        global_min = 0
        global_max = max(y_true_max, y_pred_max) * 1.05  # Add 5% buffer

        # Plot outputs with larger markers, filtering out NaN values
        for i in range(n_outputs):
            # Create mask for non-NaN values
            valid_mask = ~np.isnan(y_true[:, i]) & ~np.isnan(y_pred[:, i])
            if np.any(valid_mask):  # Only plot if there are valid points
                plt.scatter(y_true[valid_mask, i], y_pred[valid_mask, i], alpha=0.6, label=output_names[i], s=80)

        # Calculate R² with proper handling of NaN values
        r2_values = []
        for i in range(n_outputs):
            try:
                # Create mask for non-NaN values
                valid_mask = ~np.isnan(y_true[:, i]) & ~np.isnan(y_pred[:, i])
                if np.sum(valid_mask) > 1:  # Need at least 2 points for R²
                    r2 = r2_score(y_true[valid_mask, i], y_pred[valid_mask, i])
                    r2_values.append(r2)
            except Exception as e:
                print(f"Error calculating R² for output {i}: {e}")

        # Calculate mean R² if we have any valid values
        r2 = np.mean(r2_values) if r2_values else np.nan
        # Only add legend if we have any valid data points with labels
        if any(np.any(~np.isnan(y_true[:, i]) & ~np.isnan(y_pred[:, i])) for i in range(n_outputs)):
            plt.legend(fontsize=12)
    else:
        # Single output case
        # Handle potential NaN values when calculating min/max
        y_true_max = np.nanmax(y_true) if not np.all(np.isnan(y_true)) else 0
        y_pred_max = np.nanmax(y_pred) if not np.all(np.isnan(y_pred)) else 0
        global_min = 0
        global_max = max(y_true_max, y_pred_max) * 1.05  # Add 5% buffer

        # Create mask for non-NaN values
        valid_mask = ~np.isnan(y_true) & ~np.isnan(y_pred)
        if np.any(valid_mask):  # Only plot if there are valid points
            plt.scatter(y_true[valid_mask], y_pred[valid_mask], alpha=0.6, s=80)

        # Calculate R² with proper handling of NaN values
        try:
            if np.sum(valid_mask) > 1:  # Need at least 2 points for R²
                r2 = r2_score(y_true[valid_mask], y_pred[valid_mask])
            else:
                r2 = np.nan
        except Exception as e:
            print(f"Error calculating R²: {e}")
            r2 = np.nan

    # Add diagonal line and set axes to dynamic range
    plt.plot([global_min, global_max], [global_min, global_max], 'r--', linewidth=2)
    plt.xlim(global_min, global_max)
    plt.ylim(global_min, global_max)
    plt.gca().set_aspect('equal')

    # Improved labels and title with larger font
    plt.xlabel('True Values', fontsize=16)
    plt.ylabel('Predicted Values', fontsize=16)
    plt.title('Regression Predictions', fontsize=18, fontweight='bold')

    # Add R² annotation with larger font, handling NaN values
    r2_text = f'R² = {r2:.4f}' if not np.isnan(r2) else 'R² = N/A'
    plt.annotate(r2_text, xy=(0.05, 0.95), xycoords='axes fraction',
                fontsize=14, bbox=dict(boxstyle="round,pad=0.3", fc="white", ec="gray", alpha=0.8))

    # Better grid
    plt.grid(True, alpha=0.3, linestyle='--')

    # Increase tick label size
    plt.tick_params(axis='both', which='major', labelsize=14)

    plt.tight_layout()
    plt.savefig(os.path.join(save_dir, 'regression_predictions.svg'), bbox_inches='tight')
    plt.close()

def create_error_distribution_plot(y_true, y_pred, output_names=None):
    """Create error distribution plot"""
    save_dir = RESULT_DIRS['regression']
    plt.figure(figsize=(8, 6))

    if len(y_true.shape) > 1 and y_true.shape[1] > 1:
        # Plot error distributions for each output
        n_outputs = y_true.shape[1]
        output_names = output_names or [f'Output {i+1}' for i in range(n_outputs)]
        has_valid_data = False

        for i in range(min(3, n_outputs)):
            # Create mask for non-NaN values
            valid_mask = ~np.isnan(y_true[:, i]) & ~np.isnan(y_pred[:, i])
            if np.sum(valid_mask) > 1:  # Need at least 2 points for KDE
                errors = y_pred[valid_mask, i] - y_true[valid_mask, i]
                sns.kdeplot(errors, label=output_names[i])
                has_valid_data = True

        if has_valid_data:
            plt.legend()
        else:
            plt.text(0.5, 0.5, 'No valid data for error distribution',
                    ha='center', va='center', fontsize=14, transform=plt.gca().transAxes)
    else:
        # Single output case
        # Create mask for non-NaN values
        valid_mask = ~np.isnan(y_true) & ~np.isnan(y_pred)
        if np.sum(valid_mask) > 1:  # Need at least 2 points for KDE
            errors = y_pred[valid_mask] - y_true[valid_mask]
            sns.histplot(errors, kde=True)
        else:
            plt.text(0.5, 0.5, 'No valid data for error distribution',
                    ha='center', va='center', fontsize=14, transform=plt.gca().transAxes)

    plt.xlabel('Prediction Error')
    plt.ylabel('Density')
    plt.title('Error Distribution')
    plt.grid(True, alpha=0.3)

    plt.tight_layout()
    plt.savefig(os.path.join(save_dir, 'error_distribution.svg'), bbox_inches='tight')
    plt.close()

def create_feature_importance_plot(feature_importance, feature_names):
    save_dir = RESULT_DIRS['common']
    plt.figure(figsize=(18, 16))  # Increased figure size for better visibility

    # Make a copy to avoid modifying the original array
    feature_importance_clean = feature_importance.copy()

    # Check for NaN or Inf values in feature_importance
    if np.any(np.isnan(feature_importance_clean)) or np.any(np.isinf(feature_importance_clean)):
        # Count NaN and Inf values for better reporting
        nan_count = np.sum(np.isnan(feature_importance_clean))
        inf_count = np.sum(np.isinf(feature_importance_clean))
        total_invalid = nan_count + inf_count

        # Replace NaN and Inf values with zeros
        feature_importance_clean = np.nan_to_num(feature_importance_clean, nan=0.0, posinf=0.0, neginf=0.0)
        print(f"Warning: {total_invalid} NaN or Inf values found in feature importance ({nan_count} NaN, {inf_count} Inf). Replaced with zeros.")

    # Get top 10 features by importance (or fewer if there aren't 10 features)
    n_features = min(10, len(feature_importance_clean))
    indices = np.argsort(feature_importance_clean)[-n_features:][::-1]
    names = [feature_names[i] for i in indices]
    values = feature_importance_clean[indices]

    # Check if we have any non-zero values
    if np.all(values == 0):
        plt.text(0.5, 0.5, 'No valid feature importance data available',
                ha='center', va='center', fontsize=18, transform=plt.gca().transAxes)
        plt.title('Feature Importance Ranking', fontsize=24, fontweight='bold')
        plt.tight_layout()
        plt.savefig(os.path.join(save_dir, 'feature_importance.svg'), bbox_inches='tight', dpi=300)
        plt.close()
        return

    # Calculate true relative values (percentages)
    # Normalize to the maximum value (top feature = 100%)
    max_value = values[0]
    if max_value > 0:
        norm_values = (values / max_value) * 100
    else:
        norm_values = values * 0  # All zeros if max_value is zero

    # Create a visually appealing gradient colormap
    custom_cmap = plt.cm.get_cmap('viridis', len(names))
    colors = [custom_cmap(i) for i in np.linspace(0, 0.85, len(names))]

    # Create DataFrame for plotting
    df = pd.DataFrame({'Importance': norm_values, 'Feature': names, 'AbsValue': values})

    # Create horizontal bar plot with improved spacing
    ax = sns.barplot(x='Importance', y='Feature', hue='Feature', data=df, palette=colors, dodge=False, legend=False)

    # Add more space for feature names
    plt.subplots_adjust(left=0.25)

    # Add value annotations with improved formatting
    for i, (norm_v, abs_v) in enumerate(zip(norm_values, values)):
        # Show absolute value on the right side of the bar
        ax.text(norm_v + 1.0, i, f'{abs_v:.3f}', va='center', fontsize=14, fontweight='bold')
        # Show percentage inside the bar with better formatting
        ax.text(max(norm_v/2, 5), i, f'{norm_v:.1f}%', va='center', ha='center',
               color='white', fontweight='bold', fontsize=16)

    # Improved labels and title with larger font
    plt.xlabel('Importance Score', fontsize=18)
    plt.ylabel('Feature Name', fontsize=18)
    plt.title('Feature Importance Ranking', fontsize=24, fontweight='bold')

    # Add grid lines for better readability
    plt.grid(True, alpha=0.3, axis='x', linestyle='--')

    # Increase tick label size and make y-axis labels bold
    plt.tick_params(axis='both', which='major', labelsize=16)
    for tick in ax.get_yticklabels():
        tick.set_fontweight('bold')

    # Add explanation text at the bottom
    plt.figtext(0.5, 0.01, "[ Higher values = more important features ]",
               ha='center', fontsize=16, fontweight='bold',
               bbox=dict(facecolor='white', alpha=0.9, edgecolor='gray', boxstyle='round,pad=0.5'))

    # Set x-axis limits to add some padding, ensuring they're not NaN or Inf
    x_max = max(norm_values) * 1.15 if len(norm_values) > 0 and max(norm_values) > 0 else 100
    plt.xlim(0, x_max)

    # Add a thin border around the plot
    for spine in ax.spines.values():
        spine.set_linewidth(1.5)
        spine.set_color('black')

    plt.tight_layout(rect=[0, 0.03, 1, 0.97])  # Adjust layout to make room for the explanation text
    plt.savefig(os.path.join(save_dir, 'feature_importance.svg'), bbox_inches='tight', dpi=300)
    plt.close()

def create_pca_visualization_plot(X, labels=None, scores=None):
    if labels is not None:
        save_dir = RESULT_DIRS['clustering']
        title = 'Cluster Distribution in Feature Space'
        color_label = 'Cluster'
    elif scores is not None:
        save_dir = RESULT_DIRS['regression']
        title = 'Data Distribution with Target Values'
        color_label = 'Target Value'
    else:
        save_dir = RESULT_DIRS['common']
        title = 'Data Distribution in Feature Space'
        color_label = None

    plt.figure(figsize=(12, 10))  # Increased figure size

    if X.shape[1] > 2:
        n_comp = min(2, X.shape[1], X.shape[0])
        pca = PCA(n_components=n_comp)
        X_pca = pca.fit_transform(X)

        var = pca.explained_variance_ratio_
        var_text = f"Explained variance: PC1 {var[0]:.2%}"
        if n_comp > 1:
            var_text += f", PC2 {var[1]:.2%} (Total: {var[0]+var[1]:.2%})"

        df = pd.DataFrame({'x': X_pca[:, 0], 'y': X_pca[:, 1]})
        x_label = f'Principal Component 1 ({var[0]:.2%})'
        y_label = f'Principal Component 2 ({var[1]:.2%})' if n_comp > 1 else 'Principal Component 2'

        # Calculate data range for better axis limits
        x_min, x_max = df['x'].min(), df['x'].max()
        y_min, y_max = df['y'].min(), df['y'].max()
        x_range = x_max - x_min
        y_range = y_max - y_min
        x_buffer = x_range * 0.1
        y_buffer = y_range * 0.1
    else:
        df = pd.DataFrame({'x': X[:, 0], 'y': X[:, 1]})
        x_label = 'Feature 1'
        y_label = 'Feature 2'
        var_text = None

        # Calculate data range for better axis limits
        x_min, x_max = df['x'].min(), df['x'].max()
        y_min, y_max = df['y'].min(), df['y'].max()
        x_range = x_max - x_min
        y_range = y_max - y_min
        x_buffer = x_range * 0.1
        y_buffer = y_range * 0.1

    if labels is not None:
        df['label'] = labels
        sns.scatterplot(x='x', y='y', hue='label', data=df, palette='viridis',
                       s=80, alpha=0.7, edgecolor='w', linewidth=0.5)
        plt.legend(title=color_label, loc='best', frameon=True, framealpha=0.9,
                  fontsize=12, title_fontsize=14)

        # Set better axis limits
        plt.xlim(x_min - x_buffer, x_max + x_buffer)
        plt.ylim(y_min - y_buffer, y_max + y_buffer)

        # Move cluster count information to a better position to avoid overlap
        counts = pd.Series(labels).value_counts().sort_index()
        count_text = "Clusters: " + ", ".join([f"C{i}: {count}" for i, count in enumerate(counts)])

        # Use a text box at the top of the plot instead of figtext at the bottom
        plt.annotate(count_text, xy=(0.5, 0.97), xycoords='figure fraction',
                    fontsize=12, va='top', ha='center',
                    bbox=dict(boxstyle="round,pad=0.5", fc="white", ec="gray", alpha=0.9))

    elif scores is not None:
        if len(scores.shape) > 1 and scores.shape[1] > 1:
            score_vals = np.mean(scores, axis=1)
        else:
            score_vals = scores.flatten()

        df['score'] = score_vals
        scatter = plt.scatter(df['x'], df['y'], c=df['score'], cmap='viridis',
                             s=80, alpha=0.7, edgecolor='w', linewidth=0.5)
        cbar = plt.colorbar(scatter, label=color_label)
        cbar.ax.tick_params(labelsize=12)
        cbar.set_label(color_label, fontsize=14)

        # Set better axis limits
        plt.xlim(x_min - x_buffer, x_max + x_buffer)
        plt.ylim(y_min - y_buffer, y_max + y_buffer)
    else:
        sns.scatterplot(x='x', y='y', data=df, s=80, alpha=0.7, edgecolor='w', linewidth=0.5)

        # Set better axis limits
        plt.xlim(x_min - x_buffer, x_max + x_buffer)
        plt.ylim(y_min - y_buffer, y_max + y_buffer)

    if var_text:
        plt.annotate(var_text, xy=(0.02, 0.98), xycoords='axes fraction',
                    fontsize=12, va='top', ha='left',
                    bbox=dict(boxstyle="round,pad=0.3", fc="white", ec="gray", alpha=0.8))

        # Note about explained variance (removed warning)

    plt.title(title, fontsize=16, fontweight='bold')
    plt.xlabel(x_label, fontsize=14)
    plt.ylabel(y_label, fontsize=14)
    plt.grid(True, alpha=0.3, linestyle='--')

    # Add tick labels with better formatting
    plt.tick_params(axis='both', which='major', labelsize=12)

    plt.tight_layout()
    plt.savefig(os.path.join(save_dir, 'pca_visualization.svg'), bbox_inches='tight')
    plt.close()

def create_anomaly_score_distribution_plot(anomaly_scores, y_true=None):
    """Create anomaly score distribution plot"""
    save_dir = RESULT_DIRS['anomaly']
    plt.figure(figsize=(8, 6))

    if y_true is not None:
        sns.histplot(anomaly_scores[y_true == 0], kde=True, label='Normal', alpha=0.5)
        sns.histplot(anomaly_scores[y_true == 1], kde=True, label='Anomaly', alpha=0.5)
        plt.legend()
    else:
        sns.histplot(anomaly_scores, kde=True)

    plt.title('Anomaly Score Distribution')
    plt.xlabel('Anomaly Score')
    plt.ylabel('Count')
    plt.grid(True, alpha=0.3)

    plt.tight_layout()
    plt.savefig(os.path.join(save_dir, 'anomaly_score_distribution.svg'), bbox_inches='tight')
    plt.close()

def create_feature_correlation_heatmap(X, feature_names=None):
    """Create feature correlation heatmap"""
    save_dir = RESULT_DIRS['common']
    plt.figure(figsize=(10, 8))

    # Select top features by variance
    if X.shape[1] > 10:
        var = np.var(X, axis=0)
        top_indices = np.argsort(var)[-10:]
        X_top = X[:, top_indices]
        feature_names = [feature_names[i] if feature_names else f'Feature {i}' for i in top_indices]
    else:
        X_top = X
        feature_names = feature_names or [f'Feature {i}' for i in range(X.shape[1])]

    # Calculate standard deviations for each feature
    std_devs = np.std(X_top, axis=0)

    # Create correlation matrix
    n_features = X_top.shape[1]
    corr = np.eye(n_features)  # Initialize with ones on diagonal (self-correlation = 1)

    # Compute correlations manually with proper handling of zero standard deviations
    for i in range(n_features):
        for j in range(i+1, n_features):
            # Skip calculation if either feature has zero standard deviation
            if std_devs[i] < 1e-10 or std_devs[j] < 1e-10:
                corr[i, j] = corr[j, i] = 0
                continue

            # Get feature vectors
            x, y = X_top[:, i], X_top[:, j]

            # Center the data
            x_centered = x - np.mean(x)
            y_centered = y - np.mean(y)

            # Calculate correlation coefficient
            numerator = np.sum(x_centered * y_centered)
            denominator = np.sqrt(np.sum(x_centered**2)) * np.sqrt(np.sum(y_centered**2))

            # Avoid division by zero
            if denominator > 1e-10:
                corr_val = numerator / denominator
                # Handle potential numerical issues
                if corr_val > 1.0: corr_val = 1.0
                if corr_val < -1.0: corr_val = -1.0
                corr[i, j] = corr[j, i] = corr_val
            else:
                corr[i, j] = corr[j, i] = 0

    # Check if correlation matrix contains any NaN values
    has_nan = np.isnan(corr).any()

    if has_nan:
        # Create a mask for NaN values
        mask = np.isnan(corr)
        # Use the mask to avoid NaN warnings
        sns.heatmap(corr, annot=True, fmt='.2f', cmap='coolwarm',
                   xticklabels=feature_names, yticklabels=feature_names,
                   mask=mask)
    else:
        sns.heatmap(corr, annot=True, fmt='.2f', cmap='coolwarm',
                   xticklabels=feature_names, yticklabels=feature_names)
    plt.title('Feature Correlation Heatmap')

    plt.tight_layout()
    plt.savefig(os.path.join(save_dir, 'feature_correlation_heatmap.svg'), bbox_inches='tight')
    plt.close()

def create_training_loss_plot(history):
    """Create training and validation loss plot"""
    save_dir = RESULT_DIRS['training']
    plt.figure(figsize=(8, 6))

    epochs = range(1, len(history['train_loss']) + 1)
    plt.plot(epochs, history['train_loss'], 'b-', label='Training Loss')
    if 'val_loss' in history and len(history['val_loss']) > 0:
        plt.plot(epochs, history['val_loss'], 'r-', label='Validation Loss')
    plt.title('Training and Validation Loss')
    plt.xlabel('Epoch')
    plt.ylabel('Loss')
    plt.legend()
    plt.grid(True, alpha=0.3)

    plt.tight_layout()
    plt.savefig(os.path.join(save_dir, 'training_loss.svg'), bbox_inches='tight')
    plt.close()

def create_learning_rate_plot(history):
    """Create learning rate plot"""
    save_dir = RESULT_DIRS['training']
    plt.figure(figsize=(8, 6))

    if 'learning_rate' in history and len(history['learning_rate']) > 0:
        epochs = range(1, len(history['learning_rate']) + 1)
        plt.plot(epochs, history['learning_rate'], 'g-')
        plt.title('Learning Rate')
        plt.xlabel('Epoch')
        plt.ylabel('Learning Rate')
        plt.grid(True, alpha=0.3)
    else:
        plt.text(0.5, 0.5, 'Learning Rate Not Available', ha='center', va='center')
        plt.title('Learning Rate')

    plt.tight_layout()
    plt.savefig(os.path.join(save_dir, 'learning_rate.svg'), bbox_inches='tight')
    plt.close()

def create_loss_convergence_plot(history):
    """Create loss convergence plot with log scale"""
    save_dir = RESULT_DIRS['training']
    plt.figure(figsize=(8, 6))

    epochs = range(1, len(history['train_loss']) + 1)
    plt.semilogy(epochs, history['train_loss'], 'b-', label='Training Loss')
    if 'val_loss' in history and len(history['val_loss']) > 0:
        plt.semilogy(epochs, history['val_loss'], 'r-', label='Validation Loss')
    plt.title('Loss Convergence (Log Scale)')
    plt.xlabel('Epoch')
    plt.ylabel('Loss (log scale)')
    plt.legend()
    plt.grid(True, alpha=0.3)

    plt.tight_layout()
    plt.savefig(os.path.join(save_dir, 'loss_convergence.svg'), bbox_inches='tight')
    plt.close()

def create_training_improvement_rate_plot(history):
    """Create training improvement rate plot"""
    save_dir = RESULT_DIRS['training']
    plt.figure(figsize=(8, 6))

    train_loss = np.array(history['train_loss'])
    if len(train_loss) > 10:
        # Calculate improvement rate over epochs
        improvement_rate = []
        window_size = max(2, len(train_loss) // 10)
        for i in range(window_size, len(train_loss), window_size):
            start_loss = train_loss[i-window_size]
            end_loss = train_loss[i-1]
            if start_loss > 0:
                improvement = (start_loss - end_loss) / start_loss
                improvement_rate.append(improvement)

        if improvement_rate:
            epochs_points = range(window_size, len(train_loss), window_size)
            plt.bar(epochs_points[:len(improvement_rate)], improvement_rate)
            plt.title('Training Improvement Rate')
            plt.xlabel('Epoch')
            plt.ylabel('Improvement Rate')
            plt.grid(True, alpha=0.3)
        else:
            plt.text(0.5, 0.5, 'Improvement Rate Not Available', ha='center', va='center')
            plt.title('Training Metrics')
    else:
        plt.text(0.5, 0.5, 'Additional Metrics Not Available', ha='center', va='center')
        plt.title('Training Metrics')

    plt.tight_layout()
    plt.savefig(os.path.join(save_dir, 'training_improvement_rate.svg'), bbox_inches='tight')
    plt.close()

def create_contrastive_loss_plot(history):
    """Create contrastive loss plot"""
    save_dir = RESULT_DIRS['training']
    plt.figure(figsize=(8, 6))

    if 'contrastive_loss' in history and len(history['contrastive_loss']) > 0:
        epochs = range(1, len(history['contrastive_loss']) + 1)
        plt.plot(epochs, history['contrastive_loss'], 'm-')
        plt.title('Contrastive Loss')
        plt.xlabel('Epoch')
        plt.ylabel('Loss')
        plt.grid(True, alpha=0.3)
    else:
        plt.text(0.5, 0.5, 'Contrastive Loss Not Available', ha='center', va='center')
        plt.title('Contrastive Loss')

    plt.tight_layout()
    plt.savefig(os.path.join(save_dir, 'contrastive_loss.svg'), bbox_inches='tight')
    plt.close()

def create_cluster_visualization_plot(X, labels, centroids=None):
    save_dir = RESULT_DIRS['clustering']
    plt.figure(figsize=(12, 10))  # Increased figure size

    if X.shape[1] > 2:
        n_comp = min(2, X.shape[1], X.shape[0])
        pca = PCA(n_components=n_comp)
        X_pca = pca.fit_transform(X)

        # Get explained variance
        var = pca.explained_variance_ratio_

        if centroids is not None:
            centroids_pca = pca.transform(centroids)

        df = pd.DataFrame({
            'x': X_pca[:, 0],
            'y': X_pca[:, 1],
            'cluster': labels
        })

        # Calculate data range for better axis limits
        x_min, x_max = df['x'].min(), df['x'].max()
        y_min, y_max = df['y'].min(), df['y'].max()
        x_range = x_max - x_min
        y_range = y_max - y_min
        x_buffer = x_range * 0.1
        y_buffer = y_range * 0.1

        sns.scatterplot(x='x', y='y', hue='cluster', data=df, palette='viridis', alpha=0.7, s=70)

        if centroids is not None:
            plt.scatter(
                centroids_pca[:, 0],
                centroids_pca[:, 1],
                s=250,  # Larger centroids
                marker='X',
                color='red',
                edgecolor='black',
                linewidth=2,
                label='Centroids'
            )

            for i, (x, y) in enumerate(centroids_pca):
                plt.text(x, y, f'C{i}', fontsize=14, fontweight='bold',
                        ha='center', va='center', color='white',
                        bbox=dict(boxstyle="circle,pad=0.3", fc='black', ec='white', alpha=0.7))

        # Set better axis limits
        plt.xlim(x_min - x_buffer, x_max + x_buffer)
        plt.ylim(y_min - y_buffer, y_max + y_buffer)
    else:
        df = pd.DataFrame({
            'x': X[:, 0],
            'y': X[:, 1],
            'cluster': labels
        })

        # Calculate data range for better axis limits
        x_min, x_max = df['x'].min(), df['x'].max()
        y_min, y_max = df['y'].min(), df['y'].max()
        x_range = x_max - x_min
        y_range = y_max - y_min
        x_buffer = x_range * 0.1
        y_buffer = y_range * 0.1

        sns.scatterplot(x='x', y='y', hue='cluster', data=df, palette='viridis', alpha=0.7, s=70)

        if centroids is not None:
            plt.scatter(
                centroids[:, 0],
                centroids[:, 1],
                s=250,  # Larger centroids
                marker='X',
                color='red',
                edgecolor='black',
                linewidth=2,
                label='Centroids'
            )

            for i, (x, y) in enumerate(centroids):
                plt.text(x, y, f'C{i}', fontsize=14, fontweight='bold',
                        ha='center', va='center', color='white',
                        bbox=dict(boxstyle="circle,pad=0.3", fc='black', ec='white', alpha=0.7))

        # Set better axis limits
        plt.xlim(x_min - x_buffer, x_max + x_buffer)
        plt.ylim(y_min - y_buffer, y_max + y_buffer)

    # Improved title and labels
    plt.title('Cluster Visualization', fontsize=16, fontweight='bold')

    if X.shape[1] > 2:
        plt.xlabel(f'Principal Component 1 ({var[0]:.2%})', fontsize=14)
        plt.ylabel(f'Principal Component 2 ({var[1]:.2%})', fontsize=14)

        # Note about explained variance (removed warning)
    else:
        plt.xlabel('Feature 1', fontsize=14)
        plt.ylabel('Feature 2', fontsize=14)

    plt.grid(True, alpha=0.3)

    # Better legend positioning and formatting
    plt.legend(title='Cluster', loc='best', fontsize=12, title_fontsize=14,
              frameon=True, framealpha=0.9, edgecolor='gray')

    # Add cluster count information
    counts = pd.Series(labels).value_counts().sort_index()
    count_text = "Clusters: " + ", ".join([f"C{i}: {count}" for i, count in enumerate(counts)])
    plt.figtext(0.5, 0.01, count_text, ha='center', fontsize=12,
               bbox=dict(facecolor='white', alpha=0.9, edgecolor='gray', boxstyle='round,pad=0.5'))

    plt.tight_layout(rect=[0, 0.03, 1, 0.97])  # Adjust layout to make room for the count text
    plt.savefig(os.path.join(save_dir, 'cluster_visualization.svg'), bbox_inches='tight')
    plt.close()

def create_cluster_distribution_plot(labels):
    save_dir = RESULT_DIRS['clustering']
    plt.figure(figsize=(10, 6))

    counts = pd.Series(labels).value_counts().sort_index()
    total = len(labels)
    pcts = [f"{count/total*100:.1f}%" for count in counts.values]

    sns.barplot(
        x=counts.index,
        y=counts.values,
        hue=counts.index,
        palette='viridis',
        legend=False
    )

    for i, (count, pct) in enumerate(zip(counts.values, pcts)):
        plt.text(
            i, count/2,
            f"{count}\n{pct}",
            ha='center', va='center',
            fontweight='bold', color='white'
        )

    plt.title('Cluster Distribution', fontsize=14, fontweight='bold')
    plt.xlabel('Cluster')
    plt.ylabel('Count')
    plt.grid(True, alpha=0.3)

    plt.tight_layout()
    plt.savefig(os.path.join(save_dir, 'cluster_distribution.svg'), bbox_inches='tight')
    plt.close()

def create_confusion_matrix_plot(y_true, y_pred):
    """Create confusion matrix plot"""
    save_dir = RESULT_DIRS['anomaly']
    plt.figure(figsize=(8, 6))

    cm = confusion_matrix(y_true, y_pred)
    sns.heatmap(cm, annot=True, fmt='d', cmap='Blues')
    plt.xlabel('Predicted Label')
    plt.ylabel('True Label')
    plt.title('Confusion Matrix', fontsize=14, fontweight='bold')
    plt.xticks([0.5, 1.5], ['Normal', 'Anomaly'])
    plt.yticks([0.5, 1.5], ['Normal', 'Anomaly'])

    plt.tight_layout()
    plt.savefig(os.path.join(save_dir, 'confusion_matrix.svg'), bbox_inches='tight')
    plt.close()

def create_roc_curve_plot(y_true, anomaly_scores):
    """Create ROC curve plot"""
    save_dir = RESULT_DIRS['anomaly']
    plt.figure(figsize=(8, 6))

    fpr, tpr, _ = roc_curve(y_true, anomaly_scores)
    roc_auc = auc(fpr, tpr)
    plt.plot(fpr, tpr, color='darkorange', lw=2, label=f'ROC curve (AUC = {roc_auc:.3f})')
    plt.plot([0, 1], [0, 1], color='navy', lw=2, linestyle='--')
    plt.xlim([0.0, 1.0])
    plt.ylim([0.0, 1.05])
    plt.xlabel('False Positive Rate')
    plt.ylabel('True Positive Rate')
    plt.title('Receiver Operating Characteristic', fontsize=14, fontweight='bold')
    plt.legend(loc="lower right")

    plt.tight_layout()
    plt.savefig(os.path.join(save_dir, 'roc_curve.svg'), bbox_inches='tight')
    plt.close()

def create_precision_recall_curve_plot(y_true, anomaly_scores):
    """Create precision-recall curve plot"""
    save_dir = RESULT_DIRS['anomaly']
    plt.figure(figsize=(8, 6))

    precision, recall, _ = precision_recall_curve(y_true, anomaly_scores)
    pr_auc = auc(recall, precision)
    plt.plot(recall, precision, color='green', lw=2, label=f'PR curve (AUC = {pr_auc:.3f})')
    plt.xlim([0.0, 1.0])
    plt.ylim([0.0, 1.05])
    plt.xlabel('Recall')
    plt.ylabel('Precision')
    plt.title('Precision-Recall Curve', fontsize=14, fontweight='bold')
    plt.legend(loc="lower left")

    plt.tight_layout()
    plt.savefig(os.path.join(save_dir, 'precision_recall_curve.svg'), bbox_inches='tight')
    plt.close()

def create_tsne_visualization(X, labels=None, perplexity=30, n_iter=1000, title_suffix=""):
    """Create t-SNE visualization for high-dimensional data"""
    save_dir = RESULT_DIRS['dimensionality_reduction']
    plt.figure(figsize=(12, 10))

    # Apply t-SNE
    tsne = TSNE(n_components=2, perplexity=perplexity, n_iter=n_iter, random_state=42)
    X_tsne = tsne.fit_transform(X)

    # Create DataFrame for plotting
    df = pd.DataFrame({'x': X_tsne[:, 0], 'y': X_tsne[:, 1]})

    # Calculate data range for better axis limits
    x_min, x_max = df['x'].min(), df['x'].max()
    y_min, y_max = df['y'].min(), df['y'].max()
    x_range = x_max - x_min
    y_range = y_max - y_min
    x_buffer = x_range * 0.1
    y_buffer = y_range * 0.1

    if labels is not None:
        df['label'] = labels
        sns.scatterplot(x='x', y='y', hue='label', data=df, palette='viridis',
                       s=80, alpha=0.7, edgecolor='w', linewidth=0.5)
        plt.legend(title='Cluster', loc='best', frameon=True, framealpha=0.9,
                  fontsize=12, title_fontsize=14)

        # Add cluster count information
        counts = pd.Series(labels).value_counts().sort_index()
        count_text = "Clusters: " + ", ".join([f"C{i}: {count}" for i, count in enumerate(counts)])
        plt.annotate(count_text, xy=(0.5, 0.97), xycoords='figure fraction',
                    fontsize=12, va='top', ha='center',
                    bbox=dict(boxstyle="round,pad=0.5", fc="white", ec="gray", alpha=0.9))
    else:
        sns.scatterplot(x='x', y='y', data=df, s=80, alpha=0.7, edgecolor='w', linewidth=0.5)

    # Set better axis limits
    plt.xlim(x_min - x_buffer, x_max + x_buffer)
    plt.ylim(y_min - y_buffer, y_max + y_buffer)

    # Add information about t-SNE parameters
    info_text = f"t-SNE parameters: perplexity={perplexity}, iterations={n_iter}"
    plt.annotate(info_text, xy=(0.02, 0.02), xycoords='figure fraction',
                fontsize=10, va='bottom', ha='left',
                bbox=dict(boxstyle="round,pad=0.3", fc="white", ec="gray", alpha=0.8))

    # Add note about t-SNE preserving local structure
    note_text = "Note: t-SNE preserves local structure but not global distances"
    plt.annotate(note_text, xy=(0.98, 0.02), xycoords='figure fraction',
                fontsize=10, va='bottom', ha='right',
                bbox=dict(boxstyle="round,pad=0.3", fc="white", ec="gray", alpha=0.8))

    plt.title(f't-SNE Visualization{title_suffix}', fontsize=16, fontweight='bold')
    plt.xlabel('t-SNE Component 1', fontsize=14)
    plt.ylabel('t-SNE Component 2', fontsize=14)
    plt.grid(True, alpha=0.3, linestyle='--')

    plt.tight_layout()
    plt.savefig(os.path.join(save_dir, f'tsne_visualization{title_suffix.replace(" ", "_").lower()}.svg'), bbox_inches='tight')
    plt.close()

    return X_tsne

def create_umap_visualization(X, labels=None, n_neighbors=15, min_dist=0.1, title_suffix=""):
    """Create UMAP visualization for high-dimensional data"""
    if not UMAP_AVAILABLE:
        print("UMAP not available. Install with 'pip install umap-learn'")
        return None

    save_dir = RESULT_DIRS['dimensionality_reduction']
    plt.figure(figsize=(12, 10))

    # Apply UMAP
    reducer = umap.UMAP(n_neighbors=n_neighbors, min_dist=min_dist, random_state=42)
    X_umap = reducer.fit_transform(X)

    # Create DataFrame for plotting
    df = pd.DataFrame({'x': X_umap[:, 0], 'y': X_umap[:, 1]})

    # Calculate data range for better axis limits
    x_min, x_max = df['x'].min(), df['x'].max()
    y_min, y_max = df['y'].min(), df['y'].max()
    x_range = x_max - x_min
    y_range = y_max - y_min
    x_buffer = x_range * 0.1
    y_buffer = y_range * 0.1

    if labels is not None:
        df['label'] = labels
        sns.scatterplot(x='x', y='y', hue='label', data=df, palette='viridis',
                       s=80, alpha=0.7, edgecolor='w', linewidth=0.5)
        plt.legend(title='Cluster', loc='best', frameon=True, framealpha=0.9,
                  fontsize=12, title_fontsize=14)

        # Add cluster count information
        counts = pd.Series(labels).value_counts().sort_index()
        count_text = "Clusters: " + ", ".join([f"C{i}: {count}" for i, count in enumerate(counts)])
        plt.annotate(count_text, xy=(0.5, 0.97), xycoords='figure fraction',
                    fontsize=12, va='top', ha='center',
                    bbox=dict(boxstyle="round,pad=0.5", fc="white", ec="gray", alpha=0.9))
    else:
        sns.scatterplot(x='x', y='y', data=df, s=80, alpha=0.7, edgecolor='w', linewidth=0.5)

    # Set better axis limits
    plt.xlim(x_min - x_buffer, x_max + x_buffer)
    plt.ylim(y_min - y_buffer, y_max + y_buffer)

    # Add information about UMAP parameters
    info_text = f"UMAP parameters: n_neighbors={n_neighbors}, min_dist={min_dist}"
    plt.annotate(info_text, xy=(0.02, 0.02), xycoords='figure fraction',
                fontsize=10, va='bottom', ha='left',
                bbox=dict(boxstyle="round,pad=0.3", fc="white", ec="gray", alpha=0.8))

    # Add note about UMAP preserving both local and global structure
    note_text = "Note: UMAP preserves both local and global structure"
    plt.annotate(note_text, xy=(0.98, 0.02), xycoords='figure fraction',
                fontsize=10, va='bottom', ha='right',
                bbox=dict(boxstyle="round,pad=0.3", fc="white", ec="gray", alpha=0.8))

    plt.title(f'UMAP Visualization{title_suffix}', fontsize=16, fontweight='bold')
    plt.xlabel('UMAP Component 1', fontsize=14)
    plt.ylabel('UMAP Component 2', fontsize=14)
    plt.grid(True, alpha=0.3, linestyle='--')

    plt.tight_layout()
    plt.savefig(os.path.join(save_dir, f'umap_visualization{title_suffix.replace(" ", "_").lower()}.svg'), bbox_inches='tight')
    plt.close()

    return X_umap

def create_anomaly_detection_results_plot(X, y_true, y_pred):
    """Create anomaly detection results plot"""
    save_dir = RESULT_DIRS['anomaly']
    plt.figure(figsize=(12, 10))  # Increased figure size

    # Apply PCA with standardization to improve visualization
    if X.shape[1] > 2:
        pca = PCA(n_components=2)
        X_pca = pca.fit_transform(X)
        X_pca_std = StandardScaler().fit_transform(X_pca)

        # Create separate DataFrames for each category
        df_viz = pd.DataFrame({
            'PC1': X_pca_std[:, 0],
            'PC2': X_pca_std[:, 1],
            'True Label': y_true,
            'Predicted Label': y_pred
        })

        # Calculate data range for better axis limits
        x_min, x_max = df_viz['PC1'].min(), df_viz['PC1'].max()
        y_min, y_max = df_viz['PC2'].min(), df_viz['PC2'].max()
        x_range = x_max - x_min
        y_range = y_max - y_min
        x_buffer = x_range * 0.1
        y_buffer = y_range * 0.1
    else:
        df_viz = pd.DataFrame({
            'Feature 1': X[:, 0],
            'Feature 2': X[:, 1],
            'True Label': y_true,
            'Predicted Label': y_pred
        })

        # Calculate data range for better axis limits
        x_min, x_max = df_viz['Feature 1'].min(), df_viz['Feature 1'].max()
        y_min, y_max = df_viz['Feature 2'].min(), df_viz['Feature 2'].max()
        x_range = x_max - x_min
        y_range = y_max - y_min
        x_buffer = x_range * 0.1
        y_buffer = y_range * 0.1

    # Create separate DataFrames for each category for better control
    # Corrected interpretation: 0 = Anomaly, 1 = Normal (to match confusion matrix)
    df_tp = df_viz[(df_viz['True Label'] == 0) & (df_viz['Predicted Label'] == 0)]  # True Anomaly
    df_fn = df_viz[(df_viz['True Label'] == 0) & (df_viz['Predicted Label'] == 1)]  # False Normal
    df_fp = df_viz[(df_viz['True Label'] == 1) & (df_viz['Predicted Label'] == 0)]  # False Anomaly
    df_tn = df_viz[(df_viz['True Label'] == 1) & (df_viz['Predicted Label'] == 1)]  # True Normal

    # Plot each category separately with different markers and sizes
    if X.shape[1] > 2:
        if len(df_tn) > 0:
            plt.scatter(df_tn['PC1'], df_tn['PC2'], c='#d62728', s=100, alpha=0.9,
                       label='True Normal, Pred Normal', marker='*', edgecolors='k', linewidths=0.5)
        if len(df_fp) > 0:
            plt.scatter(df_fp['PC1'], df_fp['PC2'], c='#ff7f0e', s=80, alpha=0.8,
                       label='True Normal, Pred Anomaly', marker='s', edgecolors='k', linewidths=0.5)
        if len(df_fn) > 0:
            plt.scatter(df_fn['PC1'], df_fn['PC2'], c='#9467bd', s=80, alpha=0.8,
                       label='True Anomaly, Pred Normal', marker='^', edgecolors='k', linewidths=0.5)
        if len(df_tp) > 0:
            plt.scatter(df_tp['PC1'], df_tp['PC2'], c='#1f77b4', s=60, alpha=0.7,
                       label='True Anomaly, Pred Anomaly', marker='o', edgecolors='k', linewidths=0.5)

        # Set better axis limits
        plt.xlim(x_min - x_buffer, x_max + x_buffer)
        plt.ylim(y_min - y_buffer, y_max + y_buffer)
    else:
        if len(df_tn) > 0:
            plt.scatter(df_tn['Feature 1'], df_tn['Feature 2'], c='#d62728', s=100, alpha=0.9,
                       label='True Normal, Pred Normal', marker='*', edgecolors='k', linewidths=0.5)
        if len(df_fp) > 0:
            plt.scatter(df_fp['Feature 1'], df_fp['Feature 2'], c='#ff7f0e', s=80, alpha=0.8,
                       label='True Normal, Pred Anomaly', marker='s', edgecolors='k', linewidths=0.5)
        if len(df_fn) > 0:
            plt.scatter(df_fn['Feature 1'], df_fn['Feature 2'], c='#9467bd', s=80, alpha=0.8,
                       label='True Anomaly, Pred Normal', marker='^', edgecolors='k', linewidths=0.5)
        if len(df_tp) > 0:
            plt.scatter(df_tp['Feature 1'], df_tp['Feature 2'], c='#1f77b4', s=60, alpha=0.7,
                       label='True Anomaly, Pred Anomaly', marker='o', edgecolors='k', linewidths=0.5)

        # Set better axis limits
        plt.xlim(x_min - x_buffer, x_max + x_buffer)
        plt.ylim(y_min - y_buffer, y_max + y_buffer)

    # Add legend with better positioning and formatting
    plt.legend(loc='upper right', frameon=True, framealpha=0.9, fontsize=10,
              edgecolor='gray', fancybox=True, shadow=True)

    # Improve axis labels and title
    plt.title('Anomaly Detection Results', fontsize=16, fontweight='bold')

    # Better axis labels
    if X.shape[1] > 2:
        plt.xlabel('Principal Component 1 (Standardized)', fontsize=14)
        plt.ylabel('Principal Component 2 (Standardized)', fontsize=14)
    else:
        plt.xlabel('Feature 1', fontsize=14)
        plt.ylabel('Feature 2', fontsize=14)

    # Add grid and improve appearance
    plt.grid(True, alpha=0.3, linestyle='--')

    # Add count information as text
    tn_count = len(df_tn)
    fp_count = len(df_fp)
    fn_count = len(df_fn)
    tp_count = len(df_tp)

    plt.text(0.02, 0.98, f"TN: {tn_count}", transform=plt.gca().transAxes,
            fontsize=12, va='top', ha='left', color='#d62728', fontweight='bold',
            bbox=dict(facecolor='white', alpha=0.7, edgecolor='gray', boxstyle='round,pad=0.3'))
    plt.text(0.02, 0.90, f"FP: {fp_count}", transform=plt.gca().transAxes,
            fontsize=12, va='top', ha='left', color='#ff7f0e', fontweight='bold',
            bbox=dict(facecolor='white', alpha=0.7, edgecolor='gray', boxstyle='round,pad=0.3'))
    plt.text(0.02, 0.82, f"FN: {fn_count}", transform=plt.gca().transAxes,
            fontsize=12, va='top', ha='left', color='#9467bd', fontweight='bold',
            bbox=dict(facecolor='white', alpha=0.7, edgecolor='gray', boxstyle='round,pad=0.3'))
    plt.text(0.02, 0.74, f"TP: {tp_count}", transform=plt.gca().transAxes,
            fontsize=12, va='top', ha='left', color='#1f77b4', fontweight='bold',
            bbox=dict(facecolor='white', alpha=0.7, edgecolor='gray', boxstyle='round,pad=0.3'))

    plt.tight_layout()
    plt.savefig(os.path.join(save_dir, 'anomaly_detection_results.svg'), bbox_inches='tight')
    plt.close()

def create_performance_metrics_plot(y_true, y_pred):
    save_dir = RESULT_DIRS['anomaly']
    plt.figure(figsize=(10, 8))

    tn, fp, fn, tp = confusion_matrix(y_true, y_pred).ravel()
    accuracy = (tp + tn) / (tp + tn + fp + fn)
    precision = tp / (tp + fp) if (tp + fp) > 0 else 0
    recall = tp / (tp + fn) if (tp + fn) > 0 else 0
    f1 = 2 * precision * recall / (precision + recall) if (precision + recall) > 0 else 0
    specificity = tn / (tn + fp) if (tn + fp) > 0 else 0

    metrics = {
        'Accuracy': accuracy,
        'Precision': precision,
        'Recall': recall,
        'F1 Score': f1,
        'Specificity': specificity
    }

    names = list(metrics.keys())
    values = list(metrics.values())

    colors = plt.cm.YlGn(np.linspace(0.6, 0.9, len(values)))
    plt.barh(names, values, color=colors)

    plt.xlim([0, 1])
    plt.title('Anomaly Detection Performance Metrics', fontsize=14, fontweight='bold')
    plt.xlabel('Score (higher is better)', fontsize=12)
    plt.grid(True, alpha=0.3, axis='x')

    for i, v in enumerate(values):
        plt.text(v + 0.02, i, f'{v:.3f}', va='center', fontweight='bold')
        plt.text(v/2, i, f'{v:.1%}', va='center', ha='center', color='black')

    plt.figtext(0.5, 0.01,
               "Higher values indicate better performance",
               ha='center', fontsize=10,
               bbox=dict(facecolor='white', alpha=0.8, edgecolor='gray', boxstyle='round,pad=0.5'))

    plt.tight_layout()
    plt.savefig(os.path.join(save_dir, 'performance_metrics.svg'), bbox_inches='tight')
    plt.close()

    return metrics

def create_residual_plot(y_true, y_pred, output_names=None):
    """Create residual plot"""
    save_dir = RESULT_DIRS['regression']
    plt.figure(figsize=(8, 6))

    if len(y_true.shape) > 1 and y_true.shape[1] > 1:
        n_outputs = y_true.shape[1]
        output_names = output_names or [f'Output {i+1}' for i in range(n_outputs)]

        for i in range(min(3, n_outputs)):
            # Create mask for non-NaN values
            valid_mask = ~np.isnan(y_true[:, i]) & ~np.isnan(y_pred[:, i])
            if np.any(valid_mask):  # Only plot if there are valid points
                plt.scatter(y_pred[valid_mask, i],
                           y_pred[valid_mask, i] - y_true[valid_mask, i],
                           alpha=0.5, label=output_names[i])
        # Only add legend if we have any valid data points with labels
        has_valid_data = False
        for i in range(min(3, n_outputs)):
            valid_mask = ~np.isnan(y_true[:, i]) & ~np.isnan(y_pred[:, i])
            if np.any(valid_mask):
                has_valid_data = True
                break

        if has_valid_data:
            plt.legend()
    else:
        # Create mask for non-NaN values
        valid_mask = ~np.isnan(y_true) & ~np.isnan(y_pred)
        if np.any(valid_mask):  # Only plot if there are valid points
            plt.scatter(y_pred[valid_mask], y_pred[valid_mask] - y_true[valid_mask], alpha=0.5)

    plt.axhline(y=0, color='r', linestyle='--')
    plt.xlabel('Predicted Values')
    plt.ylabel('Residuals')
    plt.title('Residual Plot')
    plt.grid(True, alpha=0.3)

    plt.tight_layout()
    plt.savefig(os.path.join(save_dir, 'residual_plot.svg'), bbox_inches='tight')
    plt.close()

def calculate_correlation(x, y):
    """Calculate correlation coefficient with proper handling of edge cases"""
    # Remove NaN values
    valid_mask = ~np.isnan(x) & ~np.isnan(y)
    if np.sum(valid_mask) <= 1:  # Need at least 2 valid points
        return 0

    x_valid = x[valid_mask]
    y_valid = y[valid_mask]

    # Check for constant values (zero standard deviation)
    x_std = np.std(x_valid)
    y_std = np.std(y_valid)
    if x_std < 1e-10 or y_std < 1e-10:
        return 0

    # Center the data
    x_centered = x_valid - np.mean(x_valid)
    y_centered = y_valid - np.mean(y_valid)

    # Calculate correlation coefficient
    numerator = np.sum(x_centered * y_centered)
    denominator = np.sqrt(np.sum(x_centered**2)) * np.sqrt(np.sum(y_centered**2))

    # Avoid division by zero
    if denominator < 1e-10:
        return 0

    corr_val = numerator / denominator

    # Handle potential numerical issues
    if corr_val > 1.0: corr_val = 1.0
    if corr_val < -1.0: corr_val = -1.0

    return corr_val

def create_feature_target_correlation_plot(X, y_true, feature_names=None):
    """Create feature-target correlation plot"""
    save_dir = RESULT_DIRS['regression']
    plt.figure(figsize=(10, 6))

    # Ensure y_true is 2D for multi-output case
    if len(y_true.shape) == 1:
        y_true = y_true.reshape(-1, 1)

    if y_true.shape[1] > 1:
        n_outputs = y_true.shape[1]
        output_names = [f'Output {i+1}' for i in range(n_outputs)]

        # Calculate feature-target correlations
        correlations = []
        for i in range(n_outputs):
            feature_corrs = []
            for j in range(min(10, X.shape[1])):
                try:
                    # Use the calculate_correlation function which already handles NaN values
                    corr = calculate_correlation(X[:, j], y_true[:, i])
                    feature_corrs.append(corr)
                except Exception as e:
                    print(f"Error calculating correlation: {e}")
                    feature_corrs.append(np.nan)
            correlations.append(feature_corrs)

        # Create feature names if not provided
        feature_names_short = feature_names[:min(10, X.shape[1])] if feature_names else [f'Feature {i}' for i in range(min(10, X.shape[1]))]

        # Create correlation dataframe and plot heatmap
        corr_df = pd.DataFrame(correlations, columns=feature_names_short, index=output_names)

        # Create a display version with formatted values
        corr_df_display = corr_df.copy()
        for col in corr_df_display.columns:
            corr_df_display[col] = corr_df_display[col].apply(
                lambda x: '{:.2f}'.format(x) if not pd.isna(x) else 'N/A'
            )

        # Check if all values are NaN
        if corr_df.isna().all().all():
            plt.text(0.5, 0.5, 'No valid correlation data available',
                    ha='center', va='center', fontsize=14, transform=plt.gca().transAxes)
        else:
            # Plot heatmap with mask for NaN values
            sns.heatmap(corr_df, annot=corr_df_display, fmt='', cmap='coolwarm',
                       vmin=-1, vmax=1, mask=pd.isna(corr_df))
        plt.title('Feature-Target Correlations')
    else:
        # Single output correlations
        feature_corrs = []
        feature_names_short = feature_names[:min(10, X.shape[1])] if feature_names else [f'Feature {i}' for i in range(min(10, X.shape[1]))]

        for i in range(min(10, X.shape[1])):
            try:
                # Use the calculate_correlation function which already handles NaN values
                corr = calculate_correlation(X[:, i], y_true[:, 0])
                feature_corrs.append(corr)
            except Exception as e:
                print(f"Error calculating correlation: {e}")
                feature_corrs.append(np.nan)

        # Filter out NaN values
        valid_indices = [i for i, corr in enumerate(feature_corrs) if not np.isnan(corr)]
        valid_corrs = [feature_corrs[i] for i in valid_indices]
        valid_names = [feature_names_short[i] for i in valid_indices]

        if valid_corrs:
            # Sort by absolute correlation value
            sorted_indices = np.argsort(np.abs(valid_corrs))[::-1]
            sorted_corrs = [valid_corrs[i] for i in sorted_indices]
            sorted_names = [valid_names[i] for i in sorted_indices]

            # Create horizontal bar chart
            bars = plt.barh(range(len(sorted_corrs)), sorted_corrs)
            plt.yticks(range(len(sorted_corrs)), sorted_names)
            plt.title('Feature-Target Correlations')
            plt.xlabel('Correlation')
            plt.xlim(-1, 1)

            # Color bars based on correlation direction
            for i, bar in enumerate(bars):
                bar.set_color('r' if sorted_corrs[i] < 0 else 'b')
        else:
            plt.text(0.5, 0.5, 'No valid correlation data available',
                    ha='center', va='center', fontsize=14, transform=plt.gca().transAxes)
            plt.title('Feature-Target Correlations')

    plt.tight_layout()
    plt.savefig(os.path.join(save_dir, 'feature_target_correlation.svg'), bbox_inches='tight')
    plt.close()

def create_feature_distributions_plot(X, feature_names=None):
    """Create feature distributions plot"""
    save_dir = RESULT_DIRS['common']
    plt.figure(figsize=(10, 6))

    # Select top features by variance
    if X.shape[1] > 5:
        var = np.var(X, axis=0)
        top_indices = np.argsort(var)[-5:]
        X_top = X[:, top_indices]
        feature_names = [feature_names[i] if feature_names else f'Feature {i}' for i in top_indices]
    else:
        X_top = X
        feature_names = feature_names or [f'Feature {i}' for i in range(X.shape[1])]

    for i in range(X_top.shape[1]):
        sns.kdeplot(X_top[:, i], label=feature_names[i])

    plt.title('Feature Distributions')
    plt.xlabel('Value')
    plt.ylabel('Density')
    plt.legend()
    plt.grid(True, alpha=0.3)

    plt.tight_layout()
    plt.savefig(os.path.join(save_dir, 'feature_distributions.svg'), bbox_inches='tight')
    plt.close()

def create_regression_metrics_plot(y_true, y_pred, output_names=None):
    save_dir = RESULT_DIRS['regression']
    plt.figure(figsize=(10, 8))

    if len(y_true.shape) > 1 and y_true.shape[1] > 1:
        n_outputs = y_true.shape[1]
        output_names = output_names or [f'Output {i+1}' for i in range(n_outputs)]

        metrics = []
        metric_names = ['MAE', 'MSE', 'RMSE', 'R²']
        for i in range(n_outputs):
            # Create mask for non-NaN values
            valid_mask = ~np.isnan(y_true[:, i]) & ~np.isnan(y_pred[:, i])
            if np.sum(valid_mask) > 0:
                # Calculate metrics only on valid data
                valid_true = y_true[valid_mask, i]
                valid_pred = y_pred[valid_mask, i]

                mae = np.mean(np.abs(valid_pred - valid_true))
                mse = np.mean((valid_pred - valid_true)**2)
                rmse = np.sqrt(mse)

                # Calculate R² only if we have enough data points
                if np.sum(valid_mask) > 1:
                    try:
                        r2 = r2_score(valid_true, valid_pred)
                    except Exception as e:
                        print(f"Error calculating R² for output {i}: {e}")
                        r2 = np.nan
                else:
                    r2 = np.nan
            else:
                # If no valid data, set metrics to NaN
                mae, mse, rmse, r2 = np.nan, np.nan, np.nan, np.nan

            metrics.append([mae, mse, rmse, r2])

        metrics_df = pd.DataFrame(metrics, columns=metric_names, index=output_names)
        # Replace NaN with 'N/A' for display
        metrics_df_display = metrics_df.copy()
        # Format the display values with 3 decimal places and replace NaN with 'N/A'
        for col in metrics_df_display.columns:
            metrics_df_display[col] = metrics_df_display[col].apply(
                lambda x: '{:.3f}'.format(x) if not pd.isna(x) else 'N/A'
            )

        # Check if all values are NaN
        if metrics_df.isna().all().all():
            plt.text(0.5, 0.5, 'No valid metrics data available',
                    ha='center', va='center', fontsize=14, transform=plt.gca().transAxes)
        else:
            # Create mask for NaN values
            mask = pd.isna(metrics_df)
            sns.heatmap(metrics_df, annot=metrics_df_display, fmt='', cmap='YlGnBu',
                       linewidths=0.5, mask=mask)
        plt.title('Regression Error Metrics by Output', fontsize=14, fontweight='bold')
        plt.xlabel('Metric Type', fontsize=12)
        plt.ylabel('Output Variable', fontsize=12)

        plt.figtext(0.5, 0.01,
                   "MAE: Mean Absolute Error, MSE: Mean Squared Error, RMSE: Root Mean Squared Error, R²: Coefficient of Determination",
                   ha='center', fontsize=9,
                   bbox=dict(facecolor='white', alpha=0.8, edgecolor='gray', boxstyle='round,pad=0.5'))
    else:
        # Create mask for non-NaN values
        valid_mask = ~np.isnan(y_true) & ~np.isnan(y_pred)

        if np.sum(valid_mask) > 0:
            # Calculate metrics only on valid data
            valid_true = y_true[valid_mask]
            valid_pred = y_pred[valid_mask]

            mae = np.mean(np.abs(valid_pred - valid_true))
            mse = np.mean((valid_pred - valid_true)**2)
            rmse = np.sqrt(mse)

            # Calculate R² only if we have enough data points
            if np.sum(valid_mask) > 1:
                try:
                    r2 = r2_score(valid_true, valid_pred)
                except Exception as e:
                    print(f"Error calculating R²: {e}")
                    r2 = np.nan
            else:
                r2 = np.nan
        else:
            # If no valid data, set metrics to NaN
            mae, mse, rmse, r2 = np.nan, np.nan, np.nan, np.nan

        metric_names = ['MAE', 'MSE', 'RMSE', 'R²']
        metric_values = [mae, mse, rmse, r2]

        # Filter out NaN values for display
        valid_metrics = [(name, val) for name, val in zip(metric_names, metric_values) if not np.isnan(val)]

        if valid_metrics:
            valid_names, valid_values = zip(*valid_metrics)

            colors = ['#3498db', '#2ecc71', '#e74c3c', '#9b59b6'][:len(valid_names)]
            plt.bar(valid_names, valid_values, color=colors, alpha=0.8)
            plt.title('Regression Error Metrics', fontsize=14, fontweight='bold')

            if len(valid_values) > 0:
                plt.ylim(0, max(valid_values) * 1.2)

                for i, v in enumerate(valid_values):
                    plt.text(i, v + max(valid_values) * 0.02, f'{v:.3f}', ha='center', fontweight='bold')
        else:
            plt.text(0.5, 0.5, 'No valid metrics available', ha='center', va='center',
                    fontsize=14, transform=plt.gca().transAxes)
            plt.title('Regression Error Metrics', fontsize=14, fontweight='bold')

        plt.grid(True, alpha=0.3, axis='y')
        plt.figtext(0.5, 0.01,
                   "MAE/MSE/RMSE: lower is better | R²: higher is better (max=1)",
                   ha='center', fontsize=9,
                   bbox=dict(facecolor='white', alpha=0.8, edgecolor='gray', boxstyle='round,pad=0.5'))

    plt.tight_layout()
    plt.savefig(os.path.join(save_dir, 'regression_metrics.svg'), bbox_inches='tight')
    plt.close()

def create_silhouette_scores_plot(cluster_range, silhouette_scores, silhouette_optimal=None,
                           silhouette_scores_kmeans=None, ch_scores=None, db_scores=None):
    """Create silhouette scores plot for optimal cluster determination"""
    save_dir = RESULT_DIRS['clustering']

    # Create a figure with two subplots if we have additional metrics
    has_additional_metrics = ch_scores is not None or db_scores is not None

    if has_additional_metrics:
        _, axs = plt.subplots(2, 1, figsize=(14, 12), gridspec_kw={'height_ratios': [1, 1]})
        ax1 = axs[0]
        ax2 = axs[1]
    else:
        plt.figure(figsize=(14, 8))
        ax1 = plt.gca()

    # Plot GMM silhouette scores
    ax1.plot(cluster_range, silhouette_scores, 'o-', color='#3498db', linewidth=2, markersize=8, label='GMM Silhouette')

    # Plot KMeans silhouette scores if available
    if silhouette_scores_kmeans is not None and len(silhouette_scores_kmeans) == len(cluster_range):
        ax1.plot(cluster_range, silhouette_scores_kmeans, 's--', color='#2ecc71', linewidth=2, markersize=8, label='KMeans Silhouette')

    # Mark the optimal value
    if silhouette_optimal is not None:
        try:
            optimal_idx = cluster_range.index(silhouette_optimal)
            ax1.axvline(x=silhouette_optimal, color='#e74c3c', linestyle='--', linewidth=2, label=f'Optimal: {silhouette_optimal}')
            ax1.plot(silhouette_optimal, silhouette_scores[optimal_idx], 'o', color='#e74c3c', ms=12, markeredgewidth=2, markeredgecolor='black')

            # Add text annotation for the optimal value
            ax1.annotate(f'Optimal: {silhouette_optimal}\nScore: {silhouette_scores[optimal_idx]:.4f}',
                        xy=(silhouette_optimal, silhouette_scores[optimal_idx]),
                        xytext=(silhouette_optimal + 0.5, silhouette_scores[optimal_idx]),
                        arrowprops=dict(facecolor='black', shrink=0.05, width=1.5),
                        fontsize=12, fontweight='bold')
        except (ValueError, IndexError) as e:
            print(f"Warning: Could not plot silhouette optimal line: {e}")

    # Add value annotations to each point
    for x, y in zip(cluster_range, silhouette_scores):
        ax1.annotate(f'{y:.4f}', xy=(x, y), xytext=(0, 10),
                    textcoords='offset points', ha='center', fontsize=9)

    ax1.set_title('Silhouette Scores by Cluster Count', fontsize=16, fontweight='bold')
    ax1.set_xlabel('Number of Clusters', fontsize=14)
    ax1.set_ylabel('Silhouette Score', fontsize=14)
    ax1.grid(True, alpha=0.3)
    ax1.legend(fontsize=12, loc='best')

    # Set y-axis limits with some padding
    if max(silhouette_scores) > 0:
        y_max = max(silhouette_scores) * 1.15
        y_min = min(0, min(silhouette_scores) * 1.15)
        ax1.set_ylim(y_min, y_max)

    # Plot additional metrics if available
    if has_additional_metrics:
        # Plot Calinski-Harabasz scores
        if ch_scores is not None and len(ch_scores) == len(cluster_range):
            # Normalize CH scores for better visualization
            if max(ch_scores) > 0:
                ch_norm = [score / max(ch_scores) for score in ch_scores]
                ax2.plot(cluster_range, ch_norm, 'o-', color='#9b59b6', linewidth=2, markersize=8, label='Calinski-Harabasz (normalized)')

                # Add value annotations
                for x, y, orig_y in zip(cluster_range, ch_norm, ch_scores):
                    ax2.annotate(f'{orig_y:.1f}', xy=(x, y), xytext=(0, 10),
                                textcoords='offset points', ha='center', fontsize=9)

        # Plot Davies-Bouldin scores
        if db_scores is not None and len(db_scores) == len(cluster_range):
            # For DB, lower is better, so invert for visualization
            valid_db = [score for score in db_scores if not np.isinf(score)]
            if valid_db:
                db_max = max(valid_db)
                # Normalize and invert DB scores (1 is best, 0 is worst)
                db_norm = [1 - (score / db_max) if not np.isinf(score) else 0 for score in db_scores]
                ax2.plot(cluster_range, db_norm, 's--', color='#f39c12', linewidth=2, markersize=8, label='Davies-Bouldin (inverted, normalized)')

                # Add value annotations
                for x, y, orig_y in zip(cluster_range, db_norm, db_scores):
                    if not np.isinf(orig_y):
                        ax2.annotate(f'{orig_y:.2f}', xy=(x, y), xytext=(0, -15),
                                    textcoords='offset points', ha='center', fontsize=9)

        ax2.set_title('Additional Clustering Metrics (Normalized)', fontsize=16, fontweight='bold')
        ax2.set_xlabel('Number of Clusters', fontsize=14)
        ax2.set_ylabel('Normalized Score', fontsize=14)
        ax2.grid(True, alpha=0.3)
        ax2.legend(fontsize=12, loc='best')
        ax2.set_ylim(0, 1.1)

    # Add explanation text
    if has_additional_metrics:
        plt.figtext(0.5, 0.01,
                   "Silhouette: Higher is better (-1 to 1) | Calinski-Harabasz: Higher is better | Davies-Bouldin: Lower is better",
                   ha='center', fontsize=10,
                   bbox=dict(facecolor='white', alpha=0.8, edgecolor='gray', boxstyle='round,pad=0.5'))
    else:
        plt.figtext(0.5, 0.01,
                   "Higher silhouette scores indicate better-defined clusters. Values range from -1 to 1.",
                   ha='center', fontsize=10,
                   bbox=dict(facecolor='white', alpha=0.8, edgecolor='gray', boxstyle='round,pad=0.5'))

    plt.tight_layout(rect=[0, 0.03, 1, 0.97])
    plt.savefig(os.path.join(save_dir, 'silhouette_scores.svg'), bbox_inches='tight')
    plt.close()

def create_inertia_plot(cluster_range, inertia_values, elbow_optimal=None):
    """Create inertia plot for optimal cluster determination (Elbow method)"""
    save_dir = RESULT_DIRS['clustering']
    plt.figure(figsize=(10, 6))

    plt.plot(cluster_range, inertia_values, 'o-', color='green')
    if elbow_optimal is not None:
        try:
            optimal_idx = cluster_range.index(elbow_optimal)
            plt.axvline(x=elbow_optimal, color='red', linestyle='--')
            plt.plot(elbow_optimal, inertia_values[optimal_idx], 'ro', ms=10)
        except (ValueError, IndexError) as e:
            print(f"Warning: Could not plot elbow optimal line: {e}")

    plt.title('Inertia by Cluster Count (Elbow Method)')
    plt.xlabel('Number of Clusters')
    plt.ylabel('Inertia')
    plt.grid(True, alpha=0.3)

    plt.tight_layout()
    plt.savefig(os.path.join(save_dir, 'inertia_elbow.svg'), bbox_inches='tight')
    plt.close()

def create_calinski_scores_plot(cluster_range, calinski_scores):
    """Create Calinski-Harabasz scores plot for cluster evaluation"""
    save_dir = RESULT_DIRS['clustering']
    plt.figure(figsize=(10, 6))

    plt.plot(cluster_range, calinski_scores, 'o-', color='purple')
    plt.title('Calinski-Harabasz Scores by Cluster Count')
    plt.xlabel('Number of Clusters')
    plt.ylabel('Calinski-Harabasz Score')
    plt.grid(True, alpha=0.3)

    plt.tight_layout()
    plt.savefig(os.path.join(save_dir, 'calinski_scores.svg'), bbox_inches='tight')
    plt.close()

def create_davies_scores_plot(cluster_range, davies_scores):
    """Create Davies-Bouldin scores plot for cluster evaluation"""
    save_dir = RESULT_DIRS['clustering']
    plt.figure(figsize=(10, 6))

    plt.plot(cluster_range, davies_scores, 'o-', color='orange')
    plt.title('Davies-Bouldin Scores by Cluster Count')
    plt.xlabel('Number of Clusters')
    plt.ylabel('Davies-Bouldin Score')
    plt.grid(True, alpha=0.3)

    plt.tight_layout()
    plt.savefig(os.path.join(save_dir, 'davies_scores.svg'), bbox_inches='tight')
    plt.close()

def create_bic_score_plot(metrics):
    """Create plot showing BIC scores for different numbers of clusters and covariance types"""
    if 'bic_scores' not in metrics:
        return

    save_dir = RESULT_DIRS['clustering']
    plt.figure(figsize=(12, 8))

    # Extract BIC scores
    components = []
    cov_types = set()
    scores_by_cov = {}

    for n_str, data in metrics['bic_scores'].items():
        n = int(n_str)
        components.append(n)
        cov_type = data['best_cov_type']
        score = data['score']

        cov_types.add(cov_type)
        if cov_type not in scores_by_cov:
            scores_by_cov[cov_type] = {}
        scores_by_cov[cov_type][n] = score

    # Sort components
    components = sorted(components)

    # Plot BIC scores for each covariance type
    colors = plt.cm.tab10(np.linspace(0, 1, len(cov_types)))
    color_dict = {cov: color for cov, color in zip(cov_types, colors)}

    for cov_type, scores in scores_by_cov.items():
        x = []
        y = []
        for n in components:
            if n in scores:
                x.append(n)
                y.append(scores[n])

        plt.plot(x, y, 'o-', label=f'{cov_type}', color=color_dict[cov_type], linewidth=2, markersize=8)

    # Mark optimal model
    if 'optimal_bic' in metrics:
        opt = metrics['optimal_bic']
        opt_n = opt['components']
        opt_cov = opt['cov_type']
        opt_score = opt['score']

        plt.scatter([opt_n], [opt_score], s=200, c='red', marker='*',
                   edgecolors='black', linewidths=1.5, zorder=10,
                   label=f'Optimal: {opt_n} components, {opt_cov}')

    # Mark current model if different from optimal
    if 'current_components' in metrics and metrics['current_components'] != opt_n:
        current_n = metrics['current_components']
        if current_n in scores_by_cov.get(opt_cov, {}):
            current_score = scores_by_cov[opt_cov][current_n]
            plt.scatter([current_n], [current_score], s=150, c='orange', marker='X',
                       edgecolors='black', linewidths=1.5, zorder=9,
                       label=f'Current: {current_n} components')

    plt.title('BIC Scores by Number of Components and Covariance Type', fontsize=16, fontweight='bold')
    plt.xlabel('Number of Components', fontsize=14)
    plt.ylabel('BIC Score (lower is better)', fontsize=14)
    plt.grid(True, alpha=0.3, linestyle='--')
    plt.legend(fontsize=12, title='Covariance Type', title_fontsize=14)

    # Set x-axis to integer ticks
    plt.xticks(components)

    # Add note about BIC
    plt.figtext(0.5, 0.01,
               "BIC (Bayesian Information Criterion) balances model fit and complexity.\nLower values indicate better models.",
               ha='center', fontsize=12,
               bbox=dict(facecolor='white', alpha=0.8, edgecolor='gray', boxstyle='round,pad=0.5'))

    plt.tight_layout(rect=[0, 0.05, 1, 0.95])
    plt.savefig(os.path.join(save_dir, 'bic_scores.svg'), bbox_inches='tight')
    plt.close()

def create_optimal_clusters_plots(plots, metrics):
    """Create all plots for optimal cluster determination"""
    if 'cluster_range' in plots and 'silhouette_scores' in plots:
        create_silhouette_scores_plot(
            plots['cluster_range'],
            plots['silhouette_scores'],
            metrics.get('silhouette_optimal'),
            plots.get('silhouette_scores_kmeans'),
            plots.get('ch_scores'),
            plots.get('db_scores')
        )

    if 'cluster_range' in plots and 'inertia_values' in plots:
        create_inertia_plot(
            plots['cluster_range'],
            plots['inertia_values'],
            metrics.get('elbow_optimal')
        )

    if 'cluster_range' in plots and 'calinski_scores' in plots:
        create_calinski_scores_plot(
            plots['cluster_range'],
            plots['calinski_scores']
        )

    if 'cluster_range' in plots and 'davies_scores' in plots:
        create_davies_scores_plot(
            plots['cluster_range'],
            plots['davies_scores']
        )

    # Create BIC score plot if BIC scores are available
    if 'bic_scores' in metrics:
        create_bic_score_plot(metrics)

def create_metric_discrepancy_plot(metrics, component_range, silhouette_scores, ch_scores, db_scores):
    """Create a plot showing the discrepancy between different clustering metrics"""
    save_dir = RESULT_DIRS['clustering']
    plt.figure(figsize=(12, 10))

    # Create a normalized version of each metric for comparison
    # For silhouette and CH, higher is better, so normalize to [0, 1]
    # For DB, lower is better, so normalize to [1, 0]

    # Normalize silhouette scores (already in [-1, 1], but we'll rescale to [0, 1])
    sil_min = min(silhouette_scores)
    sil_max = max(silhouette_scores)
    if sil_max > sil_min:
        sil_norm = [(s - sil_min) / (sil_max - sil_min) for s in silhouette_scores]
    else:
        sil_norm = [0.5] * len(silhouette_scores)

    # Normalize CH scores
    ch_min = min(ch_scores)
    ch_max = max(ch_scores)
    if ch_max > ch_min:
        ch_norm = [(s - ch_min) / (ch_max - ch_min) for s in ch_scores]
    else:
        ch_norm = [0.5] * len(ch_scores)

    # Normalize DB scores (invert since lower is better)
    db_min = min(db_scores)
    db_max = max(db_scores)
    if db_max > db_min:
        db_norm = [1 - ((s - db_min) / (db_max - db_min)) for s in db_scores]
    else:
        db_norm = [0.5] * len(db_scores)

    # Plot normalized metrics
    plt.plot(component_range, sil_norm, 'o-', color='#3498db', linewidth=2, markersize=8, label='Silhouette (normalized)')
    plt.plot(component_range, ch_norm, 's-', color='#2ecc71', linewidth=2, markersize=8, label='Calinski-Harabasz (normalized)')
    plt.plot(component_range, db_norm, '^-', color='#e74c3c', linewidth=2, markersize=8, label='Davies-Bouldin (normalized, inverted)')

    # Add BIC scores if available
    if 'bic_scores' in metrics and 'optimal_bic' in metrics:
        try:
            bic_values = []
            bic_components = []

            for n_str in sorted(metrics['bic_scores'].keys(), key=lambda x: int(x)):
                n = int(n_str)
                if n in component_range:
                    bic_components.append(n)
                    bic_values.append(metrics['bic_scores'][n_str]['score'])

            if bic_values:
                # Normalize BIC scores (invert since lower is better)
                bic_min = min(bic_values)
                bic_max = max(bic_values)
                if bic_max > bic_min:
                    bic_norm = [1 - ((s - bic_min) / (bic_max - bic_min)) for s in bic_values]
                    plt.plot(bic_components, bic_norm, 'D-', color='#9b59b6', linewidth=2, markersize=8, label='BIC (normalized, inverted)')
        except Exception as e:
            print(f"Error plotting BIC scores: {e}")

    # Mark optimal values
    best_metrics = {}

    # Find best silhouette
    if silhouette_scores:
        best_sil_idx = np.argmax(silhouette_scores)
        best_sil_comp = component_range[best_sil_idx]
        best_metrics['Silhouette'] = best_sil_comp
        plt.scatter([best_sil_comp], [sil_norm[best_sil_idx]], s=150, color='#3498db',
                   edgecolor='black', linewidth=2, zorder=10, marker='*')

    # Find best CH
    if ch_scores:
        best_ch_idx = np.argmax(ch_scores)
        best_ch_comp = component_range[best_ch_idx]
        best_metrics['Calinski-Harabasz'] = best_ch_comp
        plt.scatter([best_ch_comp], [ch_norm[best_ch_idx]], s=150, color='#2ecc71',
                   edgecolor='black', linewidth=2, zorder=10, marker='*')

    # Find best DB
    if db_scores:
        best_db_idx = np.argmin(db_scores)
        best_db_comp = component_range[best_db_idx]
        best_metrics['Davies-Bouldin'] = best_db_comp
        plt.scatter([best_db_comp], [db_norm[best_db_idx]], s=150, color='#e74c3c',
                   edgecolor='black', linewidth=2, zorder=10, marker='*')

    # Mark BIC optimal if available
    if 'optimal_bic' in metrics:
        opt_comp = metrics['optimal_bic']['components']
        if opt_comp in component_range:
            idx = component_range.index(opt_comp)
            if idx < len(db_norm):  # Use db_norm length as a proxy for valid indices
                best_metrics['BIC'] = opt_comp
                plt.scatter([opt_comp], [db_norm[idx]], s=150, color='#9b59b6',
                           edgecolor='black', linewidth=2, zorder=10, marker='*')

    # Add current model
    if 'current_components' in metrics:
        current_comp = metrics['current_components']
        if current_comp in component_range:
            plt.axvline(x=current_comp, color='black', linestyle='--', linewidth=1.5,
                       label=f'Current: {current_comp} components')

    # Add annotations for optimal values
    y_pos = 0.9
    for metric, comp in best_metrics.items():
        plt.annotate(f"{metric}: {comp}", xy=(0.02, y_pos), xycoords='axes fraction',
                    fontsize=12, fontweight='bold', color='black',
                    bbox=dict(facecolor='white', alpha=0.8, edgecolor='gray', boxstyle='round,pad=0.3'))
        y_pos -= 0.05

    # Add title and labels
    plt.title('Clustering Metric Comparison (Normalized)', fontsize=16, fontweight='bold')
    plt.xlabel('Number of Components', fontsize=14)
    plt.ylabel('Normalized Score (higher is better)', fontsize=14)
    plt.xticks(component_range)
    plt.ylim(-0.05, 1.05)
    plt.grid(True, alpha=0.3, linestyle='--')
    plt.legend(loc='lower right', fontsize=12)

    # Add explanation
    plt.figtext(0.5, 0.01,
               "All metrics normalized to [0,1] scale for comparison. Higher values indicate better clustering.\n"
               "Stars (*) indicate optimal values for each metric. Discrepancies suggest different aspects of cluster quality.",
               ha='center', fontsize=12,
               bbox=dict(facecolor='white', alpha=0.8, edgecolor='gray', boxstyle='round,pad=0.5'))

    plt.tight_layout(rect=[0, 0.05, 1, 0.95])
    plt.savefig(os.path.join(save_dir, 'metric_discrepancy.svg'), bbox_inches='tight')
    plt.close()

def create_cluster_metrics_plot(metrics):
    save_dir = RESULT_DIRS['clustering']
    plt.figure(figsize=(10, 8))

    # Filter numeric metrics, excluding silhouette_optimal which is not a quality metric
    # but rather the optimal number of components
    num_metrics = {k: v for k, v in metrics.items()
                  if isinstance(v, (int, float)) and not isinstance(v, bool)
                  and k != 'silhouette_optimal'}  # Exclude silhouette_optimal

    # Add actual clustering quality metrics if they're not already present
    if 'silhouette' not in num_metrics and 'silhouette_optimal' in metrics:
        # If we have silhouette scores in the plots dictionary, use the optimal one
        if hasattr(metrics, 'get') and metrics.get('silhouette_scores') is not None:
            try:
                optimal_idx = list(metrics.get('cluster_range')).index(metrics['silhouette_optimal'])
                num_metrics['silhouette'] = metrics.get('silhouette_scores')[optimal_idx]
            except (ValueError, IndexError, KeyError, TypeError):
                pass

    names = list(num_metrics.keys())
    values = list(num_metrics.values())

    # Normalize for visualization
    norm_values = []
    for v in values:
        if v >= 0:
            norm_values.append(min(v, 1.0))
        else:
            norm_values.append(max(-1.0, v))

    if len(norm_values) > 0:
        colors = plt.cm.viridis(np.linspace(0.2, 0.8, len(norm_values)))
        plt.barh(names, norm_values, color=colors)

        for i, v in enumerate(values):
            plt.text(norm_values[i]/2, i, f"{v:.3f}", ha='center', va='center',
                    fontweight='bold', color='white')
            plt.text(norm_values[i]+0.05, i, f"{v:.3f}", va='center', fontweight='bold')
    else:
        # If no metrics to display, show a message
        plt.text(0.5, 0.5, 'No clustering quality metrics available',
                ha='center', va='center', fontsize=14, transform=plt.gca().transAxes)

    plt.title('Clustering Quality Metrics', fontsize=14, fontweight='bold')
    plt.xlim(-1, 1)
    plt.axvline(x=0, color='gray', linestyle='--', alpha=0.7)
    plt.grid(True, alpha=0.3)

    # Add a note about the optimal number of components
    if 'silhouette_optimal' in metrics:
        plt.figtext(0.5, 0.05,
                   f"Optimal number of components: {metrics['silhouette_optimal']}",
                   ha='center', fontsize=12, fontweight='bold',
                   bbox=dict(facecolor='white', alpha=0.9, edgecolor='gray', boxstyle='round,pad=0.5'))

    plt.figtext(0.5, 0.01,
               "Silhouette, Calinski-Harabasz: Higher is better\nDavies-Bouldin, Inertia: Lower is better",
               ha='center', fontsize=10,
               bbox=dict(facecolor='white', alpha=0.8, edgecolor='gray', boxstyle='round,pad=0.5'))

    plt.tight_layout(rect=[0, 0.07, 1, 0.97])  # Adjust layout to make room for the text
    plt.savefig(os.path.join(save_dir, 'cluster_metrics.svg'), bbox_inches='tight')
    plt.close()

def create_feature_importance_by_cluster_plot(X, labels, centroids, feature_names=None):
    save_dir = RESULT_DIRS['clustering']
    plt.figure(figsize=(12, 8))

    if X.shape[1] > 10:
        var = np.var(X, axis=0)
        top_indices = np.argsort(var)[-10:]
        names = [feature_names[i] if feature_names else f'Feature {i}' for i in top_indices]
    else:
        top_indices = None
        names = feature_names or [f'Feature {i}' for i in range(X.shape[1])]

    if centroids is not None and len(np.unique(labels)) > 0:
        centroid_data = centroids if X.shape[1] <= 10 else centroids[:, top_indices]
        centroid_df = pd.DataFrame(centroid_data, columns=names)

        # Normalize for better visualization
        centroid_df = (centroid_df - centroid_df.min()) / (centroid_df.max() - centroid_df.min() + 1e-10)

        sns.heatmap(
            centroid_df.T,
            annot=True,
            fmt='.2f',
            cmap='coolwarm',
            linewidths=0.5
        )

        plt.title('Feature Values by Cluster (Normalized)', fontsize=14, fontweight='bold')
        plt.xlabel('Cluster')
        plt.ylabel('Feature')
    else:
        plt.text(0.5, 0.5, 'Feature Importance Not Available', ha='center', va='center', fontsize=14)
        plt.title('Feature Importance by Cluster', fontsize=14, fontweight='bold')

    plt.tight_layout()
    plt.savefig(os.path.join(save_dir, 'feature_importance_by_cluster.svg'), bbox_inches='tight')
    plt.close()

def create_parallel_coordinates_plot(X, labels, feature_names=None):
    save_dir = RESULT_DIRS['clustering']
    plt.figure(figsize=(12, 6))

    if X.shape[1] > 0 and len(np.unique(labels)) > 0:
        if X.shape[1] > 10:
            var = np.var(X, axis=0)
            top_indices = np.argsort(var)[-10:]
            X_subset = X[:, top_indices]
            names = [feature_names[i] if feature_names else f'Feature {i}' for i in top_indices]
        else:
            X_subset = X
            names = feature_names or [f'Feature {i}' for i in range(X.shape[1])]

        df = pd.DataFrame(X_subset, columns=names)
        df['cluster'] = labels

        pd.plotting.parallel_coordinates(
            df,
            'cluster',
            colormap='viridis',
            alpha=0.5
        )

        plt.title('Cluster Feature Comparison', fontsize=14, fontweight='bold')
        plt.grid(True, alpha=0.3)
        plt.xticks(rotation=45, ha='right')
    else:
        plt.text(0.5, 0.5, 'Cluster Comparison Not Available', ha='center', va='center', fontsize=14)
        plt.title('Cluster Comparison', fontsize=14, fontweight='bold')

    plt.tight_layout()
    plt.savefig(os.path.join(save_dir, 'parallel_coordinates.svg'), bbox_inches='tight')
    plt.close()

def create_3d_cluster_visualization(X, labels, centroids=None):
    save_dir = RESULT_DIRS['clustering']

    fig = plt.figure(figsize=(14, 12))  # Increased figure size
    ax = fig.add_subplot(111, projection='3d')

    if X.shape[1] > 3:
        pca = PCA(n_components=3)
        X_pca = pca.fit_transform(X)

        if centroids is not None:
            centroids_pca = pca.transform(centroids)

        var = pca.explained_variance_ratio_
        var_text = f"Explained Variance: PC1 {var[0]:.2%}, PC2 {var[1]:.2%}, PC3 {var[2]:.2%} (Total: {sum(var):.2%})"
    else:
        X_pca = X
        centroids_pca = centroids
        var_text = None
        var = None

    colors = plt.cm.viridis(np.linspace(0, 1, len(np.unique(labels))))

    # Calculate data range for better axis limits
    x_min, x_max = X_pca[:, 0].min(), X_pca[:, 0].max()
    y_min, y_max = X_pca[:, 1].min(), X_pca[:, 1].max()
    z_min, z_max = X_pca[:, 2].min(), X_pca[:, 2].max()

    # Add buffer to axis limits
    x_range = x_max - x_min
    y_range = y_max - y_min
    z_range = z_max - z_min
    buffer = 0.1

    for i, label in enumerate(np.unique(labels)):
        mask = labels == label
        ax.scatter(
            X_pca[mask, 0],
            X_pca[mask, 1],
            X_pca[mask, 2],
            color=colors[i],
            s=60,  # Slightly larger points
            alpha=0.7,
            edgecolor='w',
            linewidth=0.5,
            label=f'Cluster {label}'
        )

    if centroids is not None and centroids_pca is not None:
        ax.scatter(
            centroids_pca[:, 0],
            centroids_pca[:, 1],
            centroids_pca[:, 2],
            s=250,  # Larger centroids
            marker='X',
            color='red',
            edgecolor='black',
            linewidth=2,
            label='Centroids'
        )

        for i, (x, y, z) in enumerate(centroids_pca):
            ax.text(x, y, z, f'C{i}', fontsize=14, fontweight='bold',
                   color='white', bbox=dict(boxstyle="round,pad=0.3", fc='black', ec='white', alpha=0.7))

    # Set better axis limits with buffer
    ax.set_xlim(x_min - buffer * x_range, x_max + buffer * x_range)
    ax.set_ylim(y_min - buffer * y_range, y_max + buffer * y_range)
    ax.set_zlim(z_min - buffer * z_range, z_max + buffer * z_range)

    # Improved axis labels with larger font size
    if X.shape[1] > 3 and var is not None:
        ax.set_xlabel(f'Principal Component 1 ({var[0]:.2%})', fontsize=14)
        ax.set_ylabel(f'Principal Component 2 ({var[1]:.2%})', fontsize=14)
        ax.set_zlabel(f'Principal Component 3 ({var[2]:.2%})', fontsize=14)
    else:
        ax.set_xlabel('Feature 1', fontsize=14)
        ax.set_ylabel('Feature 2', fontsize=14)
        ax.set_zlabel('Feature 3', fontsize=14)

    # Improved title
    ax.set_title('3D Cluster Visualization', fontsize=16, fontweight='bold')

    # Better legend positioning and formatting
    ax.legend(loc='upper right', frameon=True, framealpha=0.9, fontsize=12)

    # Move variance text to a better position to avoid overlap
    if var_text:
        plt.figtext(0.5, 0.02, var_text, ha='center', fontsize=12,
                   bbox=dict(facecolor='white', alpha=0.9, edgecolor='gray', boxstyle='round,pad=0.5'))

    ax.grid(True, alpha=0.3, linestyle='--')
    ax.view_init(elev=30, azim=45)

    # Add tick labels with better formatting
    ax.tick_params(axis='both', which='major', labelsize=12)

    plt.tight_layout(rect=[0, 0.03, 1, 1])  # Adjust layout to make room for the variance text
    plt.savefig(os.path.join(save_dir, '3d_cluster_visualization.svg'), bbox_inches='tight')
    plt.close()

def generate_individual_plots(X, y_true=None, y_pred=None, feature_importance=None, feature_names=None,
                             anomaly_scores=None, history=None, labels=None, centroids=None, metrics=None,
                             plots=None, task_type='regression', output_names=None):
    """Generate plots based on task type"""
    create_result_dirs()

    # Common plots
    if X is not None:
        create_feature_distributions_plot(X, feature_names)
        create_feature_correlation_heatmap(X, feature_names)

    if feature_importance is not None and feature_names is not None:
        create_feature_importance_plot(feature_importance, feature_names)

    # Task-specific plots
    if task_type == 'regression' and y_true is not None and y_pred is not None:
        create_regression_prediction_plot(y_true, y_pred, output_names)
        create_error_distribution_plot(y_true, y_pred, output_names)
        create_residual_plot(y_true, y_pred, output_names)
        create_regression_metrics_plot(y_true, y_pred, output_names)
        if X is not None:
            create_feature_target_correlation_plot(X, y_true, feature_names)
            create_pca_visualization_plot(X, scores=y_true)

    elif task_type == 'anomaly':
        if anomaly_scores is not None:
            create_anomaly_score_distribution_plot(anomaly_scores, y_true)
            if X is not None:
                create_pca_visualization_plot(X, scores=anomaly_scores)

                # Add t-SNE visualization for high-dimensional data
                if X.shape[1] > 10 and y_true is not None:
                    try:
                        # Create binary labels for visualization
                        binary_labels = np.zeros(len(y_true), dtype=int)
                        binary_labels[y_true == 1] = 1  # Anomalies
                        create_tsne_visualization(X, binary_labels, title_suffix=" for Anomaly Detection")
                    except Exception as e:
                        print(f"Error creating t-SNE visualization: {e}")

                # Add UMAP visualization if available
                if X.shape[1] > 10 and UMAP_AVAILABLE and y_true is not None:
                    try:
                        # Create binary labels for visualization
                        binary_labels = np.zeros(len(y_true), dtype=int)
                        binary_labels[y_true == 1] = 1  # Anomalies
                        create_umap_visualization(X, binary_labels, title_suffix=" for Anomaly Detection")
                    except Exception as e:
                        print(f"Error creating UMAP visualization: {e}")

        if y_true is not None and y_pred is not None:
            create_confusion_matrix_plot(y_true, y_pred)
            create_performance_metrics_plot(y_true, y_pred)
            if X is not None:
                create_anomaly_detection_results_plot(X, y_true, y_pred)

            if anomaly_scores is not None:
                create_roc_curve_plot(y_true, anomaly_scores)
                create_precision_recall_curve_plot(y_true, anomaly_scores)

    elif task_type == 'clustering' and labels is not None:
        create_cluster_distribution_plot(labels)
        if X is not None:
            create_cluster_visualization_plot(X, labels, centroids)
            create_pca_visualization_plot(X, labels=labels)
            create_feature_importance_by_cluster_plot(X, labels, centroids, feature_names)
            create_parallel_coordinates_plot(X, labels, feature_names)

            # Add t-SNE visualization for high-dimensional data
            if X.shape[1] > 10:
                try:
                    create_tsne_visualization(X, labels, title_suffix=" for Clustering")
                except Exception as e:
                    print(f"Error creating t-SNE visualization: {e}")

            # Add UMAP visualization if available
            if X.shape[1] > 10 and UMAP_AVAILABLE:
                try:
                    create_umap_visualization(X, labels, title_suffix=" for Clustering")
                except Exception as e:
                    print(f"Error creating UMAP visualization: {e}")

            if X.shape[1] >= 3:
                create_3d_cluster_visualization(X, labels, centroids)

        if metrics is not None:
            create_cluster_metrics_plot(metrics)

        if plots is not None and 'cluster_range' in plots:
            create_optimal_clusters_plots(plots, metrics or {})

            # Create metric discrepancy plot if we have all the required metrics
            if ('silhouette_scores' in plots and 'ch_scores' in plots and 'db_scores' in plots):
                try:
                    create_metric_discrepancy_plot(
                        metrics or {},
                        plots['cluster_range'],
                        plots['silhouette_scores'],
                        plots['ch_scores'],
                        plots['db_scores']
                    )
                except Exception as e:
                    print(f"Error creating metric discrepancy plot: {e}")

    elif task_type == 'training' and history is not None:
        create_training_loss_plot(history)
        create_loss_convergence_plot(history)
        create_training_improvement_rate_plot(history)

        if 'learning_rate' in history and len(history['learning_rate']) > 0:
            create_learning_rate_plot(history)

        if 'contrastive_loss' in history and len(history['contrastive_loss']) > 0:
            create_contrastive_loss_plot(history)

    print(f"Plots for {task_type} task saved to 'results' directory")
