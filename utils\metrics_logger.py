"""
Metrics logging utility for tracking and analyzing model performance metrics.
This module provides functions to log various metrics to text files for later analysis.
"""
import os
import json
import time
import datetime
import numpy as np
import pandas as pd
from typing import Dict, List, Union, Any, Optional, Tuple

# Create logs directory structure if it doesn't exist
os.makedirs("logs", exist_ok=True)
os.makedirs("logs/metrics", exist_ok=True)

# Create task-specific subdirectories
for task_type in ["regression", "anomaly", "all"]:
    os.makedirs(os.path.join("logs", "metrics", task_type), exist_ok=True)

def get_timestamp() -> str:
    """Get a formatted timestamp for logging."""
    return datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")

def get_run_id() -> str:
    """Generate a unique run ID based on timestamp."""
    return datetime.datetime.now().strftime("%Y%m%d_%H%M%S")

def ensure_log_directories() -> None:
    """Ensure all log directories exist."""
    os.makedirs("logs", exist_ok=True)
    os.makedirs("logs/metrics", exist_ok=True)

    # Create task-specific subdirectories
    for task_type in ["regression", "anomaly", "all"]:
        os.makedirs(os.path.join("logs", "metrics", task_type), exist_ok=True)

def create_log_file(task_type: str, run_id: Optional[str] = None) -> str:
    """
    Create a log file for the specified task type.

    Args:
        task_type: Type of task ('regression', 'anomaly', 'all')
        run_id: Optional run identifier

    Returns:
        Path to the created log file
    """
    # Make sure all log directories exist
    ensure_log_directories()

    if run_id is None:
        run_id = get_run_id()

    # Get the log directory path
    log_dir = os.path.join("logs", "metrics", task_type)
    # Double-check that the directory exists
    os.makedirs(log_dir, exist_ok=True)

    log_file = os.path.join(log_dir, f"{run_id}.txt")

    # Create the file with header
    with open(log_file, 'w') as f:
        f.write(f"# Metrics Log - {task_type.upper()}\n")
        f.write(f"# Run ID: {run_id}\n")
        f.write(f"# Timestamp: {get_timestamp()}\n")
        f.write("="*80 + "\n\n")

    return log_file

def log_run_config(log_file: str, config: Dict[str, Any]) -> None:
    """
    Log the run configuration parameters.

    Args:
        log_file: Path to the log file
        config: Dictionary of configuration parameters
    """
    with open(log_file, 'a') as f:
        f.write("## RUN CONFIGURATION\n")
        for key, value in config.items():
            f.write(f"{key}: {value}\n")
        f.write("\n" + "="*80 + "\n\n")

def log_regression_metrics(
    log_file: str,
    metrics: List[Tuple[float, float, float]],
    target_cols: List[str],
    epoch: Optional[int] = None,
    component_metrics: Optional[Dict[str, Dict[str, float]]] = None
) -> None:
    """
    Log regression metrics.

    Args:
        log_file: Path to the log file
        metrics: List of tuples (rmse, mae, r2) for each target
        target_cols: Names of target columns
        epoch: Optional epoch number for training logs
        component_metrics: Optional dictionary of component-specific metrics
    """
    with open(log_file, 'a') as f:
        header = "## REGRESSION METRICS"
        if epoch is not None:
            header += f" - EPOCH {epoch}"
        f.write(f"{header}\n")

        # Table header
        f.write(f"{'Target':<20} {'RMSE':<10} {'MAE':<10} {'R²':<10}\n")
        f.write("-"*50 + "\n")

        # Metrics for each target
        for i, (rmse, mae, r2) in enumerate(metrics):
            target_name = target_cols[i] if i < len(target_cols) else f"Target_{i}"
            f.write(f"{target_name:<20} {rmse:<10.4f} {mae:<10.4f} {r2:<10.4f}\n")

        # Average metrics
        avg_rmse = np.mean([m[0] for m in metrics])
        avg_mae = np.mean([m[1] for m in metrics])
        avg_r2 = np.mean([m[2] for m in metrics])
        f.write("-"*50 + "\n")
        f.write(f"{'Average':<20} {avg_rmse:<10.4f} {avg_mae:<10.4f} {avg_r2:<10.4f}\n")
        f.write("\n")

        # Component-specific metrics if provided
        if component_metrics:
            f.write("### COMPONENT-SPECIFIC METRICS\n\n")

            for component_name, metrics_dict in component_metrics.items():
                f.write(f"#### {component_name}\n")

                # Table header for this component
                metrics_keys = list(metrics_dict.keys())
                header_line = "Target".ljust(20)
                for key in metrics_keys:
                    header_line += f" {key}".ljust(12)
                f.write(f"{header_line}\n")
                f.write("-" * (20 + 12 * len(metrics_keys)) + "\n")

                # If metrics are per target
                if isinstance(next(iter(metrics_dict.values())), dict):
                    for target_idx, target_name in enumerate(target_cols):
                        line = target_name.ljust(20)
                        for key in metrics_keys:
                            if target_name in metrics_dict[key]:
                                line += f"{metrics_dict[key][target_name]:<12.4f}"
                            else:
                                line += "N/A".ljust(12)
                        f.write(f"{line}\n")
                # If metrics are aggregated
                else:
                    line = "All Targets".ljust(20)
                    for key in metrics_keys:
                        line += f"{metrics_dict[key]:<12.4f}"
                    f.write(f"{line}\n")

                f.write("\n")

        f.write("\n")

def log_anomaly_metrics(
    log_file: str,
    confusion_matrix: Tuple[int, int, int, int],
    additional_metrics: Optional[Dict[str, float]] = None
) -> None:
    """
    Log anomaly detection metrics.

    Args:
        log_file: Path to the log file
        confusion_matrix: Tuple of (tn, fp, fn, tp)
        additional_metrics: Optional dictionary of additional metrics
    """
    tn, fp, fn, tp = confusion_matrix

    # Calculate derived metrics
    total = tn + fp + fn + tp
    accuracy = (tp + tn) / total if total > 0 else 0
    precision = tp / (tp + fp) if (tp + fp) > 0 else 0
    recall = tp / (tp + fn) if (tp + fn) > 0 else 0
    f1 = 2 * precision * recall / (precision + recall) if (precision + recall) > 0 else 0
    specificity = tn / (tn + fp) if (tn + fp) > 0 else 0

    with open(log_file, 'a') as f:
        f.write("## ANOMALY DETECTION METRICS\n")

        # Confusion matrix
        f.write("### Confusion Matrix\n")
        f.write(f"True Negatives (TN): {tn}\n")
        f.write(f"False Positives (FP): {fp}\n")
        f.write(f"False Negatives (FN): {fn}\n")
        f.write(f"True Positives (TP): {tp}\n\n")

        # Performance metrics
        f.write("### Performance Metrics\n")
        f.write(f"Accuracy:    {accuracy:.4f}\n")
        f.write(f"Precision:   {precision:.4f}\n")
        f.write(f"Recall:      {recall:.4f}\n")
        f.write(f"F1 Score:    {f1:.4f}\n")
        f.write(f"Specificity: {specificity:.4f}\n")

        # Additional metrics if provided
        if additional_metrics:
            f.write("\n### Additional Metrics\n")
            for key, value in additional_metrics.items():
                f.write(f"{key}: {value:.4f}\n")

        f.write("\n" + "="*80 + "\n\n")

def log_feature_importance(
    log_file: str,
    feature_importance: np.ndarray,
    feature_names: List[str],
    top_n: int = 10
) -> None:
    """
    Log feature importance scores.

    Args:
        log_file: Path to the log file
        feature_importance: Array of feature importance scores
        feature_names: List of feature names
        top_n: Number of top features to log
    """
    # Create a list of (feature, importance) tuples
    features = list(zip(feature_names, feature_importance))

    # Sort by importance (descending)
    features.sort(key=lambda x: x[1], reverse=True)

    with open(log_file, 'a') as f:
        f.write("## FEATURE IMPORTANCE\n")

        # Table header
        f.write(f"{'Rank':<6} {'Feature':<30} {'Importance':<10}\n")
        f.write("-"*50 + "\n")

        # Top N features
        for i, (feature, importance) in enumerate(features[:top_n]):
            f.write(f"{i+1:<6} {feature:<30} {importance:.6f}\n")

        f.write("\n" + "="*80 + "\n\n")

def log_training_history(log_file: str, history: Dict[str, List[float]]) -> None:
    """
    Log training history metrics.

    Args:
        log_file: Path to the log file
        history: Dictionary of training history metrics
    """
    with open(log_file, 'a') as f:
        f.write("## TRAINING HISTORY\n")

        # Get the number of epochs
        num_epochs = len(history.get('train_loss', []))

        # Table header
        header = "Epoch"
        for key in history.keys():
            header += f" | {key}"
        f.write(f"{header}\n")

        # Separator line
        separator = "-" * len(header)
        f.write(f"{separator}\n")

        # Write metrics for each epoch
        for epoch in range(num_epochs):
            line = f"{epoch+1}"
            for key, values in history.items():
                if epoch < len(values):
                    value = values[epoch]
                    # Check if value is a numpy array or other non-scalar type
                    if hasattr(value, 'shape') and len(getattr(value, 'shape', [])) > 0:
                        # For arrays, just indicate it's an array with its shape
                        line += f" | array{getattr(value, 'shape', '')}"
                    elif isinstance(value, (int, float)):
                        # For scalar numeric values, format with 6 decimal places
                        line += f" | {value:.6f}"
                    else:
                        # For other types, convert to string
                        line += f" | {value}"
                else:
                    line += " | N/A"
            f.write(f"{line}\n")

        f.write("\n" + "="*80 + "\n\n")

def log_component_metrics(
    log_file: str,
    component_name: str,
    metrics: Dict[str, Any],
    description: Optional[str] = None
) -> None:
    """
    Log metrics for a specific component.

    Args:
        log_file: Path to the log file
        component_name: Name of the component
        metrics: Dictionary of metrics for this component
        description: Optional description of the component
    """
    with open(log_file, 'a') as f:
        f.write(f"## COMPONENT METRICS: {component_name.upper()}\n")

        if description:
            f.write(f"{description}\n\n")

        # Determine if metrics are nested
        is_nested = any(isinstance(v, dict) for v in metrics.values())

        if is_nested:
            # Handle nested metrics (e.g., per layer, per epoch)
            for section, section_metrics in metrics.items():
                f.write(f"### {section}\n")

                if isinstance(section_metrics, dict):
                    # Table format for dictionary metrics
                    f.write(f"{'Metric':<25} {'Value':<15}\n")
                    f.write("-"*40 + "\n")

                    for metric_name, metric_value in section_metrics.items():
                        if isinstance(metric_value, float):
                            f.write(f"{metric_name:<25} {metric_value:<15.6f}\n")
                        else:
                            f.write(f"{metric_name:<25} {metric_value}\n")
                elif isinstance(section_metrics, list):
                    # Handle list of values (e.g., per epoch)
                    f.write(f"{'Epoch':<10} {'Value':<15}\n")
                    f.write("-"*25 + "\n")

                    for i, value in enumerate(section_metrics):
                        if isinstance(value, float):
                            f.write(f"{i+1:<10} {value:<15.6f}\n")
                        else:
                            f.write(f"{i+1:<10} {value}\n")

                f.write("\n")
        else:
            # Simple flat metrics
            f.write(f"{'Metric':<25} {'Value':<15}\n")
            f.write("-"*40 + "\n")

            for metric_name, metric_value in metrics.items():
                if isinstance(metric_value, float):
                    f.write(f"{metric_name:<25} {metric_value:<15.6f}\n")
                else:
                    f.write(f"{metric_name:<25} {metric_value}\n")

        f.write("\n" + "="*80 + "\n\n")

def log_execution_time(log_file: str, start_time: float, end_time: float) -> None:
    """
    Log execution time information.

    Args:
        log_file: Path to the log file
        start_time: Start time in seconds
        end_time: End time in seconds
    """
    execution_time = end_time - start_time

    with open(log_file, 'a') as f:
        f.write("## EXECUTION TIME\n")
        f.write(f"Start time: {datetime.datetime.fromtimestamp(start_time).strftime('%Y-%m-%d %H:%M:%S')}\n")
        f.write(f"End time: {datetime.datetime.fromtimestamp(end_time).strftime('%Y-%m-%d %H:%M:%S')}\n")
        f.write(f"Total execution time: {execution_time:.2f} seconds ({execution_time/60:.2f} minutes)\n")
        f.write("\n" + "="*80 + "\n\n")
